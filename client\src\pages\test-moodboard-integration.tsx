/**
 * Test page for verifying moodboard Supabase integration
 * This page can be used to manually test the integration without full authentication
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useMoodboard } from '@/hooks/use-moodboard'
import { useAuth } from '@/hooks/use-auth'
import moodboardService from '@/services/moodboard-service'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
}

export default function TestMoodboardIntegration() {
  const { toast } = useToast()
  const { user, isLoading: isAuthLoading } = useAuth()
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [testMoodboardId, setTestMoodboardId] = useState<string | null>(null)

  // Test moodboard data
  const [testTitle, setTestTitle] = useState('Test Moodboard Integration')
  const [testDescription, setTestDescription] = useState('Testing Supabase integration for moodboards')

  const {
    moodboardList,
    isLoadingList,
    createMoodboard,
    deleteMoodboard,
    isCreating,
    isDeleting,
    refetchList
  } = useMoodboard()

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const clearTestResults = () => {
    setTestResults([])
  }

  const runIntegrationTests = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to run integration tests",
        variant: "destructive"
      })
      return
    }

    setIsRunningTests(true)
    clearTestResults()

    try {
      // Test 1: Service Connection
      addTestResult({
        name: "Service Connection",
        status: "pending",
        message: "Testing connection to moodboard service..."
      })

      // Test 2: Create Moodboard
      addTestResult({
        name: "Create Moodboard",
        status: "pending",
        message: "Creating test moodboard..."
      })

      const createResult = await createMoodboard({
        title: testTitle,
        description: testDescription,
        tags: ['test', 'integration'],
        is_public: false,
        is_favorite: false
      })

      if (createResult?.data?.id) {
        setTestMoodboardId(createResult.data.id)
        setTestResults(prev => prev.map(result => 
          result.name === "Create Moodboard" 
            ? { ...result, status: "success", message: `Moodboard created with ID: ${createResult.data.id}` }
            : result
        ))
      } else {
        throw new Error("Failed to create moodboard")
      }

      // Test 3: List Moodboards
      addTestResult({
        name: "List Moodboards",
        status: "pending",
        message: "Fetching moodboard list..."
      })

      await refetchList()
      
      setTestResults(prev => prev.map(result => 
        result.name === "List Moodboards" 
          ? { ...result, status: "success", message: `Found ${moodboardList.length} moodboards` }
          : result
      ))

      // Test 4: Auto-save Simulation
      addTestResult({
        name: "Auto-save Simulation",
        status: "pending",
        message: "Testing auto-save functionality..."
      })

      if (createResult?.data?.id) {
        await moodboardService.autoSaveMoodboard(createResult.data.id, {
          test: "auto-save-data",
          timestamp: new Date().toISOString()
        })

        setTestResults(prev => prev.map(result => 
          result.name === "Auto-save Simulation" 
            ? { ...result, status: "success", message: "Auto-save completed successfully" }
            : result
        ))
      }

      // Test 5: Update Moodboard
      addTestResult({
        name: "Update Moodboard",
        status: "pending",
        message: "Testing moodboard update..."
      })

      if (createResult?.data?.id) {
        await moodboardService.updateMoodboard(createResult.data.id, {
          title: testTitle + " (Updated)",
          description: testDescription + " - Updated via test"
        })

        setTestResults(prev => prev.map(result => 
          result.name === "Update Moodboard" 
            ? { ...result, status: "success", message: "Moodboard updated successfully" }
            : result
        ))
      }

      // Mark service connection as successful
      setTestResults(prev => prev.map(result => 
        result.name === "Service Connection" 
          ? { ...result, status: "success", message: "Successfully connected to moodboard service" }
          : result
      ))

      toast({
        title: "Integration Tests Completed",
        description: "All tests completed successfully!",
      })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      setTestResults(prev => prev.map(result => 
        result.status === "pending" 
          ? { ...result, status: "error", message: `Failed: ${errorMessage}` }
          : result
      ))

      toast({
        title: "Integration Tests Failed",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsRunningTests(false)
    }
  }

  const cleanupTestData = async () => {
    if (!testMoodboardId) return

    try {
      await deleteMoodboard(testMoodboardId)
      setTestMoodboardId(null)
      toast({
        title: "Cleanup Completed",
        description: "Test moodboard deleted successfully"
      })
    } catch (error) {
      toast({
        title: "Cleanup Failed",
        description: "Failed to delete test moodboard",
        variant: "destructive"
      })
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />
      case 'pending':
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
    }
  }

  if (isAuthLoading) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Moodboard Integration Test</h1>
        <p className="text-gray-600">
          Test the Supabase integration for the interactive moodboard tool
        </p>
      </div>

      {/* Authentication Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
        </CardHeader>
        <CardContent>
          {user ? (
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span>Authenticated as: {user.email}</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <XCircle className="w-5 h-5 text-red-600" />
              <span>Not authenticated - please log in to run tests</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Configuration */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Test Moodboard Title</label>
            <Input
              value={testTitle}
              onChange={(e) => setTestTitle(e.target.value)}
              placeholder="Enter test moodboard title"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Test Description</label>
            <Textarea
              value={testDescription}
              onChange={(e) => setTestDescription(e.target.value)}
              placeholder="Enter test description"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button
              onClick={runIntegrationTests}
              disabled={!user || isRunningTests || isCreating}
              className="flex items-center gap-2"
            >
              {isRunningTests && <Loader2 className="w-4 h-4 animate-spin" />}
              Run Integration Tests
            </Button>
            
            {testMoodboardId && (
              <Button
                onClick={cleanupTestData}
                variant="outline"
                disabled={isDeleting}
                className="flex items-center gap-2"
              >
                {isDeleting && <Loader2 className="w-4 h-4 animate-spin" />}
                Cleanup Test Data
              </Button>
            )}
          </div>
          
          {testMoodboardId && (
            <div className="text-sm text-gray-600">
              Test Moodboard ID: <code className="bg-gray-100 px-1 rounded">{testMoodboardId}</code>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <div className="font-medium">{result.name}</div>
                    <div className="text-sm text-gray-600">{result.message}</div>
                  </div>
                  <Badge variant={result.status === 'success' ? 'default' : result.status === 'error' ? 'destructive' : 'secondary'}>
                    {result.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Moodboards */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Current Moodboards ({moodboardList.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingList ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          ) : moodboardList.length > 0 ? (
            <div className="space-y-2">
              {moodboardList.slice(0, 5).map((moodboard) => (
                <div key={moodboard.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <div className="font-medium">{moodboard.title}</div>
                    <div className="text-sm text-gray-600">
                      Created: {new Date(moodboard.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <Badge variant={moodboard.status === 'active' ? 'default' : 'secondary'}>
                    {moodboard.status}
                  </Badge>
                </div>
              ))}
              {moodboardList.length > 5 && (
                <div className="text-sm text-gray-600 text-center py-2">
                  ... and {moodboardList.length - 5} more
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-600">
              No moodboards found
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
