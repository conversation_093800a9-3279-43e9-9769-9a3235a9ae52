import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowLeft, CheckCircle, ArrowRight } from "lucide-react";
import { Agent, categoriesData } from "@/data/agents-data";
import { useState } from "react";
import { LangFlowRunPanel } from "./langflow-run-panel";

interface AgentProfileViewProps {
  agent: Agent;
  onBack: () => void;
}

// Componente para la barra de progreso de capacidades
const CapabilityBar = ({ name, value }: { name: string; value: number }) => (
  <div className="mb-3">
    <div className="flex justify-between items-center mb-1">
      <span className="text-sm font-medium">{name}</span>
      <span className="text-sm font-medium">{value}%</span>
    </div>
    <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden border border-gray-300">
      <div
        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"
        style={{ width: `${value}%` }}
      ></div>
    </div>
  </div>
);

// Componente para el indicador de compatibilidad
const CompatibilityBadge = ({ name }: { name: string }) => (
  <div className="bg-gray-100 py-1 px-3 rounded-md text-sm border border-gray-300 text-gray-700">
    {name}
  </div>
);

// Componente para el caso de uso
const UseCase = ({
  title,
  description,
}: {
  title: string;
  description: string;
}) => (
  <div className="bg-white rounded-xl p-4 border-2 border-gray-200">
    <div className="flex items-start">
      <CheckCircle
        className="text-blue-500 mt-1 mr-2 flex-shrink-0"
        size={18}
      />
      <div>
        <h4 className="font-bold text-base mb-1">{title}</h4>
        <p className="text-gray-700 text-sm">{description}</p>
      </div>
    </div>
  </div>
);

export default function AgentProfileView({
  agent,
  onBack,
}: AgentProfileViewProps) {
  const [activeTab, setActiveTab] = useState<
    "overview" | "examples" | "reviews" | "performance"
  >("overview");

  // Calcular el color de la categoría para usarlo en múltiples lugares
  const categoryColor =
    categoriesData.find((cat) => cat.id === agent.categoryId)?.color ||
    "#3B82F6";

  // Generar una lista de capacidades basadas en las habilidades del agente
  const capabilities = agent.skills.map((skill) => ({
    name: skill.name,
    value: skill.level * 10, // Convertir de escala 1-10 a porcentaje
  }));

  // Lista de plataformas compatibles (esto podría venir de datos reales del agente)
  const compatibilityList = agent.compatibility || [
    "Google Docs",
    "WordPress",
    "Mailchimp",
  ];

  // Lista de casos de uso basados en la categoría
  const useCases = [
    {
      title: "Creación de Contenido para Blog",
      description:
        "Genera artículos SEO optimizados que atraen tráfico calificado y convierten visitantes en clientes.",
    },
    {
      title: "Campañas de Email Marketing",
      description:
        "Crea secuencias de emails que nutren leads, anuncian productos y generan engagement.",
    },
    {
      title: "Contenido para Redes Sociales",
      description:
        "Diseña posts específicos para cada plataforma que resuenan con tu audiencia y fomentan interacción.",
    },
    {
      title: "Copywriting para Anuncios",
      description:
        "Redacta textos persuasivos para anuncios que destacan beneficios clave y generan conversiones.",
    },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Columna lateral izquierda */}
      <motion.div
        initial={{ x: -20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Botón de regreso */}
        <div>
          <Button
            variant="outline"
            className="mb-4 border-2 border-black rounded-lg shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[5px_5px_0px_0px_rgba(0,0,0,0.9)] hover:translate-y-[-2px] transition-all"
            onClick={onBack}
          >
            <ArrowLeft size={16} className="mr-2" /> Volver al Marketplace
          </Button>
        </div>

        {/* Tarjeta de Perfil del Agente */}
        <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden">
          <div
            className="h-24 w-full flex items-center justify-center"
            style={{ backgroundColor: categoryColor }}
          >
            <div className="w-24 h-24 bg-white p-3 rounded-xl border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.8)] flex items-center justify-center">
              {agent.avatar.startsWith("/") ? (
                <img
                  src={agent.avatar}
                  alt={agent.name}
                  className="w-full h-full object-contain"
                />
              ) : (
                <div className="text-6xl">{agent.avatar || "🤖"}</div>
              )}
            </div>
          </div>
          <div className="p-5 text-center">
            <h2 className="text-2xl font-black mb-1">{agent.name}</h2>
            <div className="flex justify-center">
              <div className="flex text-yellow-500 mb-3">
                {"★".repeat(Math.floor(agent.rating || 0))}
                {agent.rating % 1 >= 0.5 ? "½" : ""}
                {"☆".repeat(5 - Math.ceil(agent.rating || 0))}
                <span className="text-gray-600 text-sm ml-1">
                  {agent.rating} ({agent.reviewsCount || 0} reseñas)
                </span>
              </div>
            </div>
            <Button
              className="w-full mb-4 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-lg border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:translate-y-[-4px] transition-all"
              onClick={() => {
                // Redireccionar según el ID del agente
                if (agent.id === "seox-analyzer") {
                  window.location.href = "/seox-analyzer";
                } else if (agent.id === "instagram-copywriter") {
                  window.location.href =
                    "/dashboard/agentes/instagram-copywriter";
                } else {
                  // Para otros agentes, seguir con el comportamiento predeterminado
                  setActiveTab("examples");
                }
              }}
            >
              Probar este Agente
            </Button>
          </div>

          {/* Beneficios Clave */}
          <div className="px-5 pb-5">
            <ul className="space-y-2">
              {agent.keyBenefits?.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle
                    className="text-green-500 mr-2 flex-shrink-0 mt-0.5"
                    size={16}
                  />
                  <span className="text-sm">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Capacidades */}
        <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-5">
          <h3 className="text-lg font-black mb-4">Capacidades</h3>
          <div className="space-y-1">
            {capabilities.map((capability, index) => (
              <CapabilityBar
                key={index}
                name={capability.name}
                value={capability.value}
              />
            ))}
          </div>
        </div>

        {/* Compatibilidad */}
        <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-5">
          <h3 className="text-lg font-black mb-4">Compatible Con</h3>
          <div className="flex flex-wrap gap-2">
            {compatibilityList.map((item, index) => (
              <CompatibilityBadge key={index} name={item} />
            ))}
          </div>
        </div>
      </motion.div>

      {/* Columna principal (derecha) */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.1 }}
        className="lg:col-span-2 space-y-6"
      >
        {/* Navegación por pestañas */}
        <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-1">
          <div className="flex">
            <button
              className={`flex-1 py-3 px-4 text-center font-bold rounded-lg transition-all ${activeTab === "overview" ? "bg-blue-600 text-white" : "hover:bg-gray-100"}`}
              onClick={() => setActiveTab("overview")}
            >
              Visión General
            </button>
            <button
              className={`flex-1 py-3 px-4 text-center font-bold rounded-lg transition-all ${activeTab === "examples" ? "bg-blue-600 text-white" : "hover:bg-gray-100"}`}
              onClick={() => setActiveTab("examples")}
            >
              Ejemplos
            </button>
            <button
              className={`flex-1 py-3 px-4 text-center font-bold rounded-lg transition-all ${activeTab === "reviews" ? "bg-blue-600 text-white" : "hover:bg-gray-100"}`}
              onClick={() => setActiveTab("reviews")}
            >
              Reseñas
            </button>
            <button
              className={`flex-1 py-3 px-4 text-center font-bold rounded-lg transition-all ${activeTab === "performance" ? "bg-blue-600 text-white" : "hover:bg-gray-100"}`}
              onClick={() => setActiveTab("performance")}
            >
              Rendimiento
            </button>
          </div>
        </div>

        {/* Contenido de la pestaña activa */}
        {activeTab === "overview" && (
          <div className="space-y-6">
            {/* About the Agent */}
            <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
              <div className="flex items-center mb-2">
                <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                <h3 className="text-xl font-black">Acerca de {agent.name}</h3>
              </div>
              <div className="prose max-w-none">
                <p className="text-gray-700">
                  {agent.about ||
                    "No hay información disponible sobre este agente."}
                </p>
              </div>

              {/* Sección específica de LangFlow */}
              {agent.isLangFlow && agent.langFlowData && (
                <div className="mt-6 bg-blue-100 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-200 text-blue-800 mr-2">
                      LangFlow
                    </span>
                    <h4 className="text-lg font-bold text-blue-800">
                      Información técnica
                    </h4>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <p className="text-sm font-medium text-blue-900">Nodos</p>
                      <p className="text-lg font-medium">
                        {agent.langFlowData.nodeCount}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-900">
                        Conexiones
                      </p>
                      <p className="text-lg font-medium">
                        {agent.langFlowData.edgeCount}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-900">
                        Última actualización
                      </p>
                      <p className="text-sm font-medium">
                        {agent.langFlowData.lastUpdated || "No disponible"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-900">
                        Creado por
                      </p>
                      <p className="text-sm font-medium">
                        {agent.langFlowData.createdBy || "Desconocido"}
                      </p>
                    </div>
                  </div>
                  <div className="text-sm text-blue-800 italic">
                    Este agente fue creado utilizando LangFlow, una plataforma
                    de diseño visual de flujos de IA.
                  </div>
                </div>
              )}

              <div className="mt-6 bg-blue-50 border border-blue-100 rounded-lg p-4">
                <h4 className="text-lg font-bold mb-3 text-blue-800">
                  ¿Qué hace especial a este agente?
                </h4>
                <ul className="space-y-2">
                  {agent.keyBenefits?.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle
                        className="text-blue-600 mr-2 flex-shrink-0 mt-0.5"
                        size={18}
                      />
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* How it Works */}
            <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
              <div className="flex items-center mb-4">
                <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                <h3 className="text-xl font-black">Cómo Funciona</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {agent.howItWorksSteps?.map((step, index) => (
                  <div
                    key={index}
                    className="bg-white border-2 border-gray-200 rounded-xl p-4"
                  >
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold text-lg">
                        {index + 1}
                      </div>
                      {index < agent.howItWorksSteps.length - 1 && (
                        <ArrowRight
                          className="hidden md:block text-gray-400"
                          size={20}
                        />
                      )}
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">{step.title}</h4>
                      <p className="text-gray-700 text-sm">
                        {step.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Use Cases */}
            <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
              <div className="flex items-center mb-4">
                <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                <h3 className="text-xl font-black">Casos de Uso</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {useCases.map((useCase, index) => (
                  <UseCase
                    key={index}
                    title={useCase.title}
                    description={useCase.description}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === "examples" && (
          <div className="space-y-6">
            {/* Panel de ejecución para agentes LangFlow */}
            {agent.isLangFlow && <LangFlowRunPanel agent={agent} />}

            {/* Ejemplos o Showcase */}
            <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
              <div className="flex items-center mb-4">
                <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                <h3 className="text-xl font-black">Ejemplos de Uso</h3>
              </div>

              {agent.examples && agent.examples.length > 0 ? (
                <div className="space-y-6">
                  {agent.examples.map((example, index) => (
                    <div
                      key={index}
                      className="border-2 border-gray-200 rounded-lg p-6 bg-white"
                    >
                      <h4 className="font-bold text-xl mb-3 text-blue-700">
                        {example.title}
                      </h4>
                      <p className="text-gray-700 whitespace-pre-line">
                        {example.content}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <p>No hay ejemplos disponibles para este agente.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === "reviews" && (
          <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
            <div className="flex items-center mb-4">
              <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
              <h3 className="text-xl font-black">Reseñas de Usuarios</h3>
            </div>
            <div className="text-center py-12 text-gray-500">
              <p>Las reseñas de usuarios estarán disponibles próximamente.</p>
            </div>
          </div>
        )}

        {activeTab === "performance" && (
          <div className="space-y-6">
            {/* Resumen de KPIs */}
            {agent.performance ? (
              <>
                <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                    <h3 className="text-xl font-black">
                      Métricas de Rendimiento
                    </h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* KPI: Tiempo liberado */}
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border-2 border-blue-200 p-4 shadow-lg transform hover:scale-105 transition-transform">
                      <div className="text-4xl text-blue-600 mb-2">⏱️</div>
                      <h4 className="text-lg font-bold text-blue-800">
                        Tiempo Liberado
                      </h4>
                      <p className="text-3xl font-black text-blue-600">
                        {agent.performance!.summary.timeFreed}
                      </p>
                      <p className="text-sm text-blue-700 mt-1">
                        Tiempo ahorrado para tu equipo mensualmente
                      </p>
                    </div>

                    {/* KPI: Tareas Completadas */}
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl border-2 border-green-200 p-4 shadow-lg transform hover:scale-105 transition-transform">
                      <div className="text-4xl text-green-600 mb-2">✅</div>
                      <h4 className="text-lg font-bold text-green-800">
                        Tareas Completadas
                      </h4>
                      <p className="text-3xl font-black text-green-600">
                        {agent.performance!.summary.tasksCompleted}
                      </p>
                      <p className="text-sm text-green-700 mt-1">
                        Últimos 30 días
                      </p>
                    </div>

                    {/* KPI: Velocidad Ejecución */}
                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border-2 border-purple-200 p-4 shadow-lg transform hover:scale-105 transition-transform">
                      <div className="text-4xl text-purple-600 mb-2">⚡</div>
                      <h4 className="text-lg font-bold text-purple-800">
                        Velocidad Promedio
                      </h4>
                      <p className="text-3xl font-black text-purple-600">
                        {agent.performance!.summary.executionSpeed}
                      </p>
                      <p className="text-sm text-purple-700 mt-1">
                        Por artículo/tarea completada
                      </p>
                    </div>

                    {/* KPI: Satisfacción */}
                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl border-2 border-yellow-200 p-4 shadow-lg transform hover:scale-105 transition-transform">
                      <div className="text-4xl text-yellow-600 mb-2">⭐</div>
                      <h4 className="text-lg font-bold text-yellow-800">
                        Satisfacción
                      </h4>
                      <p className="text-3xl font-black text-yellow-600">
                        {agent.performance!.summary.satisfactionLevel}/5
                      </p>
                      <p className="text-sm text-yellow-700 mt-1">
                        Promedio de valoraciones de usuarios
                      </p>
                    </div>
                  </div>
                </div>

                {/* Comparativa IA vs Humano */}
                <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
                  <div className="flex items-center mb-6">
                    <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                    <h3 className="text-xl font-black">
                      Comparativa IA vs Rol Humano
                    </h3>
                  </div>

                  <div className="space-y-8">
                    {agent.performance!.comparison.map((metric, index) => (
                      <div
                        key={index}
                        className="p-4 rounded-xl border-2 border-gray-200 transform hover:shadow-xl hover:translate-y-[-4px] transition-all"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <div className="text-3xl mr-3">{metric.icon}</div>
                            <h4 className="text-xl font-bold">{metric.name}</h4>
                          </div>
                          <div className="bg-green-100 text-green-800 font-bold px-3 py-1 rounded-full text-sm border border-green-300">
                            {metric.improvement}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-6 mt-4">
                          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <p className="text-sm text-blue-700 mb-1">
                              IA - {agent.name}
                            </p>
                            <p className="text-2xl font-black text-blue-700">
                              {metric.aiValue}
                            </p>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <p className="text-sm text-gray-700 mb-1">
                              Promedio Humano
                            </p>
                            <p className="text-2xl font-black text-gray-700">
                              {metric.humanValue}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Gráfico de tendencia de productividad */}
                <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
                  <div className="flex items-center mb-6">
                    <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                    <h3 className="text-xl font-black">
                      Tendencia de Productividad
                    </h3>
                  </div>

                  <div className="mt-4">
                    <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
                      <div className="h-64 relative">
                        {/* Línea de tendencia (simulada) */}
                        <div className="absolute bottom-0 left-0 w-full h-full flex items-end">
                          {agent.performance?.productivityTrend.map(
                            (point, i) => {
                              const maxTasks = Math.max(
                                ...agent.performance!.productivityTrend.map(
                                  (p) => p.tasks,
                                ),
                              );
                              const height = `${(point.tasks / maxTasks) * 100}%`;
                              return (
                                <div
                                  key={i}
                                  className="flex-1 flex flex-col items-center"
                                >
                                  <div
                                    className="w-full bg-blue-500 hover:bg-blue-600 transition-colors rounded-t-md shadow-lg"
                                    style={{ height }}
                                  ></div>
                                  <div className="text-xs mt-2 font-medium text-gray-600">
                                    {point.date}
                                  </div>
                                </div>
                              );
                            },
                          )}
                        </div>

                        {/* Valor más reciente destacado */}
                        <div className="absolute top-2 right-4 bg-blue-600 text-white px-3 py-1.5 rounded-lg font-bold text-sm border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.8)]">
                          Último mes:{" "}
                          {
                            agent.performance!.productivityTrend[
                              agent.performance!.productivityTrend.length - 1
                            ].tasks
                          }{" "}
                          tareas
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Métricas de Calidad */}
                <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
                  <div className="flex items-center mb-6">
                    <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                    <h3 className="text-xl font-black">Métricas de Calidad</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white border-2 border-gray-200 rounded-xl p-4 shadow-md transform hover:shadow-lg hover:translate-y-[-2px] transition-all">
                      <h4 className="font-bold mb-2 flex items-center">
                        <span className="text-yellow-500 mr-2">⚠️</span> Tasa de
                        Revisiones
                      </h4>
                      <div className="flex justify-between items-center">
                        <div className="text-3xl font-black text-gray-700">
                          {agent.performance!.qualityMetrics.revisionRate}%
                        </div>
                        <div className="text-sm text-gray-600">
                          {agent.performance!.qualityMetrics.revisionRate < 15
                            ? "Baja"
                            : agent.performance!.qualityMetrics.revisionRate <
                                30
                              ? "Media"
                              : "Alta"}
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Porcentaje de trabajos que requirieron ajustes
                      </p>
                    </div>

                    <div className="bg-white border-2 border-gray-200 rounded-xl p-4 shadow-md transform hover:shadow-lg hover:translate-y-[-2px] transition-all">
                      <h4 className="font-bold mb-2 flex items-center">
                        <span className="text-green-500 mr-2">😊</span>{" "}
                        Satisfacción de Usuario
                      </h4>
                      <div className="flex justify-between items-center">
                        <div className="text-3xl font-black text-gray-700">
                          {agent.performance!.qualityMetrics.satisfactionRate}%
                        </div>
                        <div className="text-sm text-gray-600">
                          {agent.performance!.qualityMetrics.satisfactionRate <
                          80
                            ? "Regular"
                            : agent.performance!.qualityMetrics
                                  .satisfactionRate < 95
                              ? "Buena"
                              : "Excelente"}
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Usuarios satisfechos con el resultado
                      </p>
                    </div>

                    <div className="bg-white border-2 border-gray-200 rounded-xl p-4 shadow-md transform hover:shadow-lg hover:translate-y-[-2px] transition-all">
                      <h4 className="font-bold mb-2 flex items-center">
                        <span className="text-blue-500 mr-2">🔄</span>{" "}
                        Consistencia
                      </h4>
                      <div className="flex justify-between items-center">
                        <div className="text-3xl font-black text-gray-700">
                          {agent.performance!.qualityMetrics.consistencyRate}%
                        </div>
                        <div className="text-sm text-gray-600">
                          {agent.performance!.qualityMetrics.consistencyRate <
                          85
                            ? "Media"
                            : agent.performance!.qualityMetrics
                                  .consistencyRate < 95
                              ? "Alta"
                              : "Muy Alta"}
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Estabilidad en la calidad de entrega
                      </p>
                    </div>
                  </div>
                </div>

                {/* Nota sobre los benchmarks */}
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-5">
                  <div className="flex">
                    <div className="text-blue-500 mr-3 flex-shrink-0">ℹ️</div>
                    <div>
                      <h4 className="font-bold text-blue-800 mb-1">
                        Nota sobre los Benchmarks
                      </h4>
                      <p className="text-sm text-blue-700">
                        Las comparativas de "Promedio Humano" están basadas en
                        estudios de mercado y datos públicos sobre el
                        rendimiento promedio de profesionales de marketing de
                        contenido. Estos valores pueden variar según la
                        industria, experiencia y contexto específico.
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
                <div className="flex items-center mb-4">
                  <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
                  <h3 className="text-xl font-black">
                    Métricas de Rendimiento
                  </h3>
                </div>
                <div className="text-center py-12 text-gray-500">
                  <p>
                    Las métricas de rendimiento no están disponibles para este
                    agente.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Call to Action */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8"
        >
          <div className="text-center">
            <h2 className="text-2xl md:text-3xl font-black text-white mb-2">
              ¿Estás listo para potenciar tu marketing con IA?
            </h2>
            <p className="text-white text-lg mb-6">
              Activa este agente ahora y comienza a optimizar tus estrategias de
              marketing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-blue-600 hover:bg-gray-100 font-bold rounded-lg border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:translate-y-[-4px] transition-all">
                Activar {agent.name}
              </Button>
              <Button
                variant="outline"
                className="bg-transparent text-white border-2 border-white rounded-lg shadow-[4px_4px_0px_0px_rgba(0,0,0,0.5)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.5)] hover:translate-y-[-4px] transition-all"
                onClick={onBack}
              >
                Explorar Más Agentes
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
