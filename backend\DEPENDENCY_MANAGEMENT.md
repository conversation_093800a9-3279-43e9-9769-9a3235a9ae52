# Dependency Management with Poetry

This guide provides comprehensive instructions for managing dependencies with Poetry across development, CI/CD, and production environments.

## Table of Contents

1. [Introduction](#introduction)
2. [Basic Commands](#basic-commands)
3. [Project Structure](#project-structure)
4. [Development Workflow](#development-workflow)
5. [Group Management](#group-management)
6. [Version Management](#version-management)
7. [Docker Integration](#docker-integration)
8. [CI/CD Integration](#cicd-integration)
9. [Troubleshooting](#troubleshooting)

## Introduction

Poetry is a modern Python dependency management tool that simplifies package management and environment isolation. Key benefits include:

- Deterministic builds via `poetry.lock`
- Simple command-line interface
- Logical organization of dependencies via groups
- Built-in environment management
- Improved security and reproducibility

## Basic Commands

### Installation

```bash
# Install Poetry
curl -sSL https://install.python-poetry.org | python3 -

# Verify installation
poetry --version
```

### Project Setup

```bash
# Initialize a new project
poetry init

# Install dependencies
poetry install

# Activate virtual environment
poetry shell

# Run a command within the virtual environment
poetry run python app/main.py
```

### Dependency Management

```bash
# Add a package
poetry add fastapi

# Add a dev dependency
poetry add --group dev pytest

# Add with specific version constraints
poetry add "fastapi>=0.95.0,<1.0.0"

# Update dependencies
poetry update

# Show installed packages
poetry show

# Show outdated packages
poetry show --outdated
```

## Project Structure

Our project uses the following structure for Poetry:

```
backend/
├── pyproject.toml    # Project metadata and dependencies
├── poetry.lock       # Lock file for deterministic builds
├── app/              # Application code
├── tests/            # Test code
└── .venv/            # Virtual environment (if in-project venv is enabled)
```

## Development Workflow

1. **Clone the repository**

```bash
git clone https://github.com/yourusername/plataforma.git
cd plataforma/backend
```

2. **Install dependencies**

```bash
poetry install
```

3. **Activate the environment**

```bash
poetry shell
# Or run commands with:
poetry run uvicorn app.main:app --reload
```

4. **Add new dependencies as needed**

```bash
# For runtime dependencies
poetry add new-package

# For development dependencies
poetry add --group dev dev-package
```

5. **Commit both pyproject.toml and poetry.lock**

```bash
git add pyproject.toml poetry.lock
git commit -m "Add new dependencies"
```

## Group Management

Poetry supports dependency groups for organizational purposes. We use the following groups:

- `main` (default): Core dependencies needed for production
- `dev`: Development-only dependencies (testing, linting, etc.)
- `web`: Web framework related dependencies
- `database`: Database-related dependencies
- `ai`: AI and ML-related dependencies
- `security`: Security-related dependencies

### Working with Groups

```bash
# Add a package to a specific group
poetry add --group ai langchain

# Install without a specific group
poetry install --without dev

# Install only a specific group
poetry install --only main

# Update packages in a specific group
poetry update --group database
```

## Version Management

### Pinning Versions

When adding packages, specify version constraints to ensure compatibility:

```bash
# Exact version
poetry add fastapi==0.115.0

# Compatible releases (recommended)
poetry add "fastapi^0.115.0"

# Greater than or equal
poetry add "fastapi>=0.115.0"

# Complex constraints
poetry add "fastapi>=0.115.0,<0.116.0"
```

### Updating Packages

```bash
# Update all packages
poetry update

# Update a specific package
poetry update fastapi

# Show outdated packages
poetry show --outdated

# Add a package while respecting the lock file
poetry add --lock fastapi
```

## Docker Integration

We provide three Docker configurations:

1. `Dockerfile.poetry`: Production-ready build that uses Poetry for dependency installation
2. `Dockerfile.poetry.dev`: Development build with hot-reloading and dev dependencies
3. `docker-compose.poetry.yml`: Docker Compose configuration for the entire stack
4. `docker-compose.poetry.override.yml`: Development override for Docker Compose

### Building with Docker

```bash
# Build the production image
docker build -f backend/Dockerfile.poetry -t plataforma-backend .

# Build and start the development stack
docker-compose -f docker-compose.poetry.yml -f docker-compose.poetry.override.yml up
```

### Dependency Changes in Docker

When you change dependencies:

1. Update `pyproject.toml`
2. Run `poetry lock` to update the lockfile
3. Rebuild the Docker image to apply changes:
   ```bash
   docker-compose -f docker-compose.poetry.yml build backend
   ```

## CI/CD Integration

Our Poetry CI/CD integration supports multiple platforms:

- GitHub Actions
- GitLab CI
- Jenkins
- CircleCI
- AWS CodeBuild

To generate CI/CD configurations:

```bash
cd backend
./poetry_ci_integration.py github  # or gitlab, jenkins, circle, aws
```

### Key CI/CD Features

- Caching Poetry dependencies for faster builds
- Separate lint, test, and build stages
- Reproducible Docker builds using Poetry
- Proper versioning and tagging of images

## Troubleshooting

### Common Issues

#### 1. Lock file out of sync

```bash
poetry lock --no-update
```

#### 2. Package conflicts

```bash
# Show why a package is installed
poetry show --tree package_name

# Try updating with poetry update if there are conflicts
poetry update package_name
```

#### 3. Virtual environment issues

```bash
# Remove and recreate virtual environment
poetry env remove python
poetry install
```

#### 4. Import errors in Docker

Check that your `Dockerfile.poetry` is correctly configured and that all dependencies are in the `pyproject.toml` file.

#### 5. Dependencies not installing in CI

Ensure your CI configuration is caching dependencies correctly and that Poetry is properly installed in the CI environment.

### Support Tools

We've provided several tools to help with troubleshooting:

- `verify_poetry_setup.sh`: Validates Poetry installation and dependency imports
- `organize_poetry_dependencies.py`: Reorganizes dependencies into logical groups
- `bulk_poetry_migration.py`: Migrates all dependencies from requirements.txt
- `finalize_poetry_migration.sh`: Validates and finalizes the migration

---

## Contributing to Dependency Management

When adding new dependencies to the project:

1. Discuss with the team first for major dependencies
2. Use semantic versioning constraints (e.g., `^1.2.3`)
3. Add them to the appropriate group
4. Update both `pyproject.toml` and `poetry.lock`
5. Document any important integration details

For questions or issues with dependency management, please contact the DevOps team.