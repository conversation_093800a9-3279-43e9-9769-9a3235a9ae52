#!/usr/bin/env python3
"""
Test script para probar la integración de Emma con AgenticSeek CloudBrowser
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def test_agenticseek_status():
    """Test del status de AgenticSeek"""
    print("🔍 Testing AgenticSeek status...")
    
    try:
        response = requests.get(f"{BASE_URL}/agenticseek/status")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_take_screenshot():
    """Test de tomar screenshot"""
    print("\n📸 Testing take screenshot...")
    
    try:
        response = requests.post(f"{BASE_URL}/agenticseek/take-screenshot")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_get_screenshot():
    """Test de obtener screenshot"""
    print("\n🖼️ Testing get screenshot...")
    
    try:
        response = requests.get(f"{BASE_URL}/agenticseek/screenshot")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Screenshot available!")
            # Guardar screenshot para verificar
            with open("test_screenshot.png", "wb") as f:
                f.write(response.content)
            print("Screenshot saved as test_screenshot.png")
            return True
        else:
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_agenticseek_chat():
    """Test del chat de AgenticSeek"""
    print("\n💬 Testing AgenticSeek chat...")
    
    try:
        payload = {
            "message": "Busca información sobre inteligencia artificial en Google"
        }
        
        response = requests.post(
            f"{BASE_URL}/agenticseek-browser/chat",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        result = response.json()
        print(f"Success: {result.get('success')}")
        print(f"Agent Used: {result.get('agent_used')}")
        print(f"Response: {result.get('response', '')[:200]}...")
        
        return response.status_code == 200 and result.get('success')
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Ejecutar todos los tests"""
    print("🚀 Starting Emma AgenticSeek CloudBrowser Tests...")
    print("=" * 60)
    
    tests = [
        ("AgenticSeek Status", test_agenticseek_status),
        ("Take Screenshot", test_take_screenshot),
        ("Get Screenshot", test_get_screenshot),
        ("AgenticSeek Chat", test_agenticseek_chat),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 40)
        
        start_time = time.time()
        success = test_func()
        end_time = time.time()
        
        results.append((test_name, success, end_time - start_time))
        
        if success:
            print(f"✅ {test_name} PASSED ({end_time - start_time:.2f}s)")
        else:
            print(f"❌ {test_name} FAILED ({end_time - start_time:.2f}s)")
        
        time.sleep(1)  # Pausa entre tests
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    for test_name, success, duration in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name:<20} ({duration:.2f}s)")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! CloudBrowser integration is working!")
    else:
        print("⚠️ Some tests failed. Check the logs above.")

if __name__ == "__main__":
    main()
