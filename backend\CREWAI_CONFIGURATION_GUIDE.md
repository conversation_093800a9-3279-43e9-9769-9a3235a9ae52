# Guía de Configuración y Optimización de CrewAI para Vibe Marketing

## Resumen de Soluciones Implementadas

Hemos realizado las siguientes mejoras para resolver los problemas con el equipo de agentes CrewAI:

1. **Creación de scripts de diagnóstico y reparación**
   - `fix_crewai.sh`: Script para reinstalar correctamente las dependencias de CrewAI
   - `test_crew_setup.py`: Script para verificar la configuración y funcionamiento del sistema

2. **Organización de archivos de configuración**
   - Centralizados en el directorio `/backend/config/`
   - Configuración coherente entre los distintos archivos YAML

3. **Optimización de parámetros de agentes**
   - Configuración de memoria y razonamiento mejorada
   - Jerarquía clara con Emma como orquestadora principal
   - Personalidades y roles bien definidos

4. **Ajuste de la comunicación entre agentes**
   - Uso de protocolos de delegación avanzados
   - Configuración de cache para mejorar el rendimiento

## Paso a Paso para Activar el Sistema

### 1. Configurar las variables de entorno

Asegúrate de tener un archivo `.env` correctamente configurado con:

```
GEMINI_API_KEY=YOUR_VALID_GEMINI_API_KEY
STABILITY_API_KEY=YOUR_VALID_STABILITY_API_KEY
```

### 2. Reinstalar dependencias de CrewAI

Ejecuta el script de reparación para asegurar que todas las dependencias estén correctamente instaladas:

```bash
cd backend
./fix_crewai.sh
```

### 3. Verificar la configuración

Ejecuta el script de prueba para confirmar que todo está correctamente configurado:

```bash
cd backend
./test_crew_setup.py
```

### 4. Iniciar el sistema

Una vez que todas las verificaciones sean exitosas, puedes iniciar el sistema:

```bash
cd backend
python main.py
```

## Arquitectura de Agentes

### Estructura de Equipo

El sistema implementa un equipo de 5 agentes especializados:

1. **Emma** (Jefa de Orquestación AI)
   - Rol: Analiza objetivos, planifica y coordina agentes
   - Herramientas: Acceso a todas las herramientas
   - Personalidad: Estratégica, empática, visionaria

2. **MemeAgent** (Generador de memes virales)
   - Rol: Crea imágenes tipo meme para redes sociales
   - Herramientas: MemeImageTool
   - Personalidad: Humorística, irreverente

3. **PhotographicAgent** (Diseñador fotográfico)
   - Rol: Genera imágenes fotorrealistas
   - Herramientas: PhotographicImageTool
   - Personalidad: Detallista, visual

4. **CinematicAgent** (Diseñador cinematográfico)
   - Rol: Crea imágenes con estilo de cine
   - Herramientas: CinematicImageTool
   - Personalidad: Épica, dramática

5. **CopyAgent** (Copywriter creativo)
   - Rol: Escribe textos persuasivos
   - Herramientas: CopyTool
   - Personalidad: Creativa, persuasiva

### Flujo de Trabajo Optimizado

1. Emma recibe el brief de campaña
2. Analiza el objetivo y la audiencia
3. Define la estrategia y delega tareas específicas a cada agente
4. Supervisa la ejecución y ajusta la estrategia según necesidad
5. Compila los recursos generados en una campaña coherente

### Parámetros Optimizados

Hemos optimizado los siguientes parámetros para mejorar el rendimiento:

- **Memoria**: Habilitada para mantener contexto durante toda la sesión
- **Cache**: Activado para mejorar tiempos de respuesta
- **Iteraciones de razonamiento**: 3-5 iteraciones para balance entre calidad y velocidad
- **Temperatura**: Variable según el rol (0.65-0.75)

## Solución de Problemas

Si encuentras problemas con el sistema, puedes seguir estos pasos:

1. **Verificar logs**: Revisa `crew_test.log` para diagnósticos detallados
2. **Reinstalar dependencias**: Ejecuta `./fix_crewai.sh` nuevamente
3. **Verificar la configuración**: Ejecuta `./test_crew_setup.py` para diagnóstico completo
4. **Revisar variables de entorno**: Asegúrate de tener API keys válidas en `.env`

### Problemas comunes y soluciones

- **Error "No module named 'crewai'"**: Ejecuta `./fix_crewai.sh` para reinstalar
- **Error con importaciones específicas**: Verifica la versión correcta de CrewAI (0.117.1)
- **Error al inicializar agentes**: Verifica la consistencia de nombres entre archivos de configuración
- **Errores de API**: Confirma que las API keys sean válidas y estén correctamente configuradas

## Mantenimiento y Ampliación

Para añadir nuevos agentes o funcionalidades:

1. Define el nuevo agente en `config/agents.yaml`
2. Añade sus tareas en `config/tasks.yaml`
3. Si necesita nuevas herramientas, defínelas en `config/tools.yaml` e impleméntalas en `crew_tools.py`
4. Actualiza la configuración del crew en `config/crew.yaml`
5. Ejecuta `./test_crew_setup.py` para verificar que todo funcione correctamente

## Recomendaciones Adicionales

- Mantén los archivos YAML de configuración consistentes entre sí
- Usa nombres coherentes para agentes en todos los archivos
- Asegúrate de tener instaladas versiones compatibles de todas las dependencias
- Considera usar Docker para un entorno aislado y reproducible