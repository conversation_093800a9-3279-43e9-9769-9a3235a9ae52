/**
 * Verify Upload Fix for Visual Complexity Analyzer
 * This script tests that the RLS policy fix resolves the upload issues
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('✅ Verifying Upload Fix...');

async function verifyUploadFix() {
  try {
    console.log('🔍 Starting verification test...');

    // Check authentication
    const { data: { user }, error: authError } = await window.supabase.auth.getUser();
    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return false;
    }

    console.log('✅ Authenticated as:', user.email);
    console.log('👤 User ID:', user.id);

    // Create test image
    const canvas = document.createElement('canvas');
    canvas.width = 50;
    canvas.height = 50;
    const ctx = canvas.getContext('2d');
    
    // Create a colorful test pattern
    ctx.fillStyle = '#FF6B6B';
    ctx.fillRect(0, 0, 25, 25);
    ctx.fillStyle = '#4ECDC4';
    ctx.fillRect(25, 0, 25, 25);
    ctx.fillStyle = '#45B7D1';
    ctx.fillRect(0, 25, 25, 25);
    ctx.fillStyle = '#96CEB4';
    ctx.fillRect(25, 25, 25, 25);

    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const testFile = new File([blob], 'upload-fix-verification.png', { type: 'image/png' });

    console.log('📄 Created test file:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });

    // Test 1: Direct Supabase Storage upload
    console.log('\n🧪 Test 1: Direct Supabase Storage upload');
    const timestamp = Date.now();
    const fileName = `${user.id}/${timestamp}_upload_fix_test.png`;
    
    console.log('📝 Testing with filename:', fileName);
    
    const { data: uploadData, error: uploadError } = await window.supabase.storage
      .from('design-analysis-images')
      .upload(fileName, testFile, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('❌ Direct upload failed:', uploadError);
      console.error('🔍 Error details:', {
        message: uploadError.message,
        code: uploadError.code,
        status: uploadError.status
      });
      
      if (uploadError.message.includes('row-level security policy')) {
        console.error('🚨 RLS POLICY STILL FAILING - Fix did not work');
        return false;
      }
      
      return false;
    }

    console.log('✅ Direct upload successful:', uploadData.path);

    // Test 2: Download the uploaded file
    console.log('\n🧪 Test 2: Download uploaded file');
    const { data: downloadData, error: downloadError } = await window.supabase.storage
      .from('design-analysis-images')
      .download(fileName);

    if (downloadError) {
      console.error('❌ Download failed:', downloadError);
      return false;
    }

    console.log('✅ Download successful:', {
      size: downloadData.size,
      type: downloadData.type
    });

    // Test 3: Service method upload
    console.log('\n🧪 Test 3: Service method upload');
    if (!window.designAnalysisService) {
      console.error('❌ designAnalysisService not available');
      return false;
    }

    const serviceResult = await window.designAnalysisService.uploadImage(testFile, user.id);
    console.log('✅ Service upload successful:', serviceResult);

    // Test 4: Complete saveAnalysis flow
    console.log('\n🧪 Test 4: Complete saveAnalysis flow');
    const testAnalysisData = {
      user_id: user.id,
      tool_type: 'visual-complexity',
      original_filename: testFile.name,
      file_size: testFile.size,
      file_type: testFile.type,
      overall_score: 90,
      complexity_scores: {
        color: 9,
        layout: 9,
        typography: 9,
        elements: 9
      },
      analysis_areas: [
        {
          name: 'Upload Fix Verification',
          score: 9,
          description: 'Testing that upload fix works correctly'
        }
      ],
      recommendations: [
        {
          category: 'Verification',
          issue: 'Upload was failing',
          importance: 'alta',
          recommendation: 'Fixed RLS policy UUID/TEXT casting issue'
        }
      ],
      ai_analysis_summary: 'Upload fix verification test completed successfully',
      agent_message: 'RLS policy fix has resolved the upload issues',
      visuai_insights: 'Images can now be uploaded and stored correctly',
      tags: ['upload-fix-verification', 'rls-policy-fix']
    };

    const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, testFile);
    
    console.log('✅ Analysis saved successfully:', {
      id: savedAnalysis.id,
      file_url: savedAnalysis.file_url,
      hasFileUrl: !!savedAnalysis.file_url
    });

    if (!savedAnalysis.file_url) {
      console.error('❌ CRITICAL: Analysis saved but file_url is still null!');
      return false;
    }

    // Test 5: Load the saved image
    console.log('\n🧪 Test 5: Load saved image');
    const imageUrl = await window.designAnalysisService.getImageUrl(savedAnalysis.file_url);
    
    if (!imageUrl) {
      console.error('❌ Failed to get image URL');
      return false;
    }

    console.log('✅ Image URL retrieved:', imageUrl.substring(0, 50) + '...');

    // Test 6: Verify image loads in browser
    const img = new Image();
    const imageLoadPromise = new Promise((resolve, reject) => {
      img.onload = () => {
        console.log('✅ Image loads successfully:', img.naturalWidth + 'x' + img.naturalHeight);
        resolve(true);
      };
      img.onerror = () => {
        console.error('❌ Image failed to load in browser');
        reject(false);
      };
    });

    img.src = imageUrl;
    await imageLoadPromise;

    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    
    // Delete the analysis
    await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
    console.log('✅ Test analysis deleted');
    
    // Delete the direct upload file
    await window.supabase.storage
      .from('design-analysis-images')
      .remove([fileName]);
    console.log('✅ Direct upload file deleted');

    // Delete the service upload file
    await window.supabase.storage
      .from('design-analysis-images')
      .remove([serviceResult]);
    console.log('✅ Service upload file deleted');

    console.log('\n🎉 SUCCESS: Upload fix verification completed successfully!');
    console.log('✅ All tests passed:');
    console.log('  - Direct Supabase Storage upload: ✅');
    console.log('  - File download: ✅');
    console.log('  - Service method upload: ✅');
    console.log('  - Complete saveAnalysis flow: ✅');
    console.log('  - Image URL retrieval: ✅');
    console.log('  - Image loading in browser: ✅');
    
    return true;

  } catch (error) {
    console.error('❌ Verification test failed:', error);
    console.error('🔍 Error details:', {
      message: error.message,
      stack: error.stack
    });
    return false;
  }
}

// Run the verification
verifyUploadFix().then(success => {
  if (success) {
    console.log('\n🏆 UPLOAD FIX VERIFICATION: SUCCESS');
    console.log('💡 The RLS policy fix has resolved the upload issues');
    console.log('📸 Images should now upload and display correctly in the Visual Complexity Analyzer');
  } else {
    console.log('\n❌ UPLOAD FIX VERIFICATION: FAILED');
    console.log('💡 Additional investigation may be needed');
  }
}).catch(error => {
  console.error('❌ Verification script failed:', error);
});

// Export for manual testing
window.verifyUploadFix = verifyUploadFix;
