"""
API endpoints for search and replace (replace objects) functionality.
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from app.core.auth import verify_api_key
from app.schemas.search_replace import SearchReplaceRequest, FrontendSearchReplaceResponse
from app.services.search_replace_service import search_replace_objects_stability

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/search-replace",
    response_model=FrontendSearchReplaceResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Replace objects in image using AI",
    description="""
    Replace objects in an image using Stability AI v2beta search and replace API.

    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Aspect Ratio:** Between 1:2.5 and 2.5:1
    **Cost:** 4 credits per successful generation

    This endpoint automatically identifies and replaces objects in images without requiring a mask.
    Simply specify what object to find (search_prompt) and what to replace it with (prompt).

    **Examples:**
    - Search: "dog" → Replace with: "golden retriever sitting in grass"
    - Search: "car" → Replace with: "red sports car"
    - Search: "glasses" → Replace with: "sunglasses"

    **Tips:**
    - Use specific, descriptive prompts for better results
    - The search_prompt should be simple and clear (e.g., "dog", "car", "person")
    - The replacement prompt should be detailed and descriptive
    - Use negative prompts to avoid unwanted elements
    - Adjust grow_mask to control the replacement area size
    """
)
async def search_replace_objects_endpoint(
    image: UploadFile = File(..., description="Image file to process"),
    prompt: str = Form(..., description="Detailed description of what to replace the object with"),
    search_prompt: str = Form(..., description="Simple description of what object to find and replace (e.g., 'dog', 'car', 'glasses')"),
    negative_prompt: Optional[str] = Form(None, description="Text describing what NOT to include in the replacement"),
    grow_mask: Optional[int] = Form(3, description="Grow mask edges (0-20 pixels)"),
    seed: Optional[int] = Form(0, description="Random seed (0 = random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)"),
    style_preset: Optional[str] = Form(None, description="Style preset for the generation")
) -> FrontendSearchReplaceResponse:
    """
    Replace objects in an image using Stability AI v2beta search and replace API.
    
    This endpoint automatically identifies objects based on the search_prompt and replaces them
    with content described in the prompt, without requiring manual mask creation.
    """
    try:
        logger.info(f"Received search and replace request for search: '{search_prompt}' → replace with: '{prompt[:50]}...'")
        logger.info(f"Grow mask: {grow_mask}, Output format: {output_format}")
        
        # Validar archivo de imagen
        if not image.filename:
            raise HTTPException(status_code=400, detail="Image filename is required")

        # Validar prompts requeridos
        if not prompt or not prompt.strip():
            raise HTTPException(
                status_code=400,
                detail="Prompt is required and cannot be empty"
            )
            
        if not search_prompt or not search_prompt.strip():
            raise HTTPException(
                status_code=400,
                detail="Search prompt is required and cannot be empty"
            )

        # Validar grow_mask
        if grow_mask is not None and (grow_mask < 0 or grow_mask > 20):
            raise HTTPException(
                status_code=400,
                detail="grow_mask must be between 0 and 20"
            )

        # Validar seed
        if seed is not None and (seed < 0 or seed > 4294967294):
            raise HTTPException(
                status_code=400,
                detail="seed must be between 0 and 4294967294"
            )

        # Validar output_format
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="output_format must be 'jpeg', 'png', or 'webp'"
            )

        # Crear request object
        search_replace_request = SearchReplaceRequest(
            prompt=prompt.strip(),
            search_prompt=search_prompt.strip(),
            negative_prompt=negative_prompt.strip() if negative_prompt and negative_prompt.strip() else None,
            grow_mask=grow_mask or 3,
            seed=seed or 0,
            output_format=output_format or "png",
            style_preset=style_preset
        )

        # Llamar al servicio de Stability AI
        service_response = await search_replace_objects_stability(
            image_file=image,
            request=search_replace_request
        )

        # Crear data URL para el frontend (igual que otras implementaciones)
        mime_type = f"image/{output_format or 'png'}"
        image_data_url = f"data:{mime_type};base64,{service_response.image}"

        # Validar que el data URL se haya creado correctamente
        if not image_data_url.startswith("data:image/"):
            logger.error(f"Invalid data URL format: {image_data_url[:100]}...")
            raise HTTPException(status_code=500, detail="Error creating image data URL")

        logger.info(f"Search and replace operation completed successfully. Result size: {len(image_data_url)} chars")
        logger.info(f"Data URL format: {image_data_url[:50]}...")  # Log primeros 50 caracteres para debug

        return FrontendSearchReplaceResponse(
            success=True,
            image_url=image_data_url,
            seed=service_response.seed,
            finish_reason=service_response.finish_reason,
            metadata={
                "prompt": prompt,
                "search_prompt": search_prompt,
                "negative_prompt": negative_prompt,
                "grow_mask": grow_mask,
                "seed": seed,
                "output_format": output_format,
                "style_preset": style_preset,
                "original_filename": image.filename
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in search and replace operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during search and replace operation: {str(e)}"
        )
