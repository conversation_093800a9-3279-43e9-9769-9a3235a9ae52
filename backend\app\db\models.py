"""
Database models for storing CrewAI trace data and related information.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Index, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class ReasoningTrace(Base):
    """Model for storing complete reasoning trace data with indexed metadata."""
    
    __tablename__ = "reasoning_traces"
    
    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(String(64), unique=True, index=True, nullable=False)
    user_prompt = Column(Text, nullable=False)
    result = Column(Text, nullable=True)
    process_type = Column(String(32), index=True)
    complexity_score = Column(Float, index=True)
    request_type = Column(String(32), index=True)
    total_steps = Column(Integer)
    duration_ms = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # JSON data stored as text
    _trace_data = Column("trace_data", Text)
    
    # Relationship to individual steps
    steps = relationship("TraceStep", back_populates="trace", cascade="all, delete-orphan")
    
    def set_trace_json(self, trace_data: Dict[str, Any]) -> None:
        """Set the trace data as JSON."""
        self._trace_data = json.dumps(trace_data)
    
    def get_trace_json(self) -> Dict[str, Any]:
        """Get the trace data as Python dict."""
        if self._trace_data:
            return json.loads(self._trace_data)
        return {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "request_id": self.request_id,
            "user_prompt": self.user_prompt,
            "result": self.result,
            "process_type": self.process_type,
            "complexity_score": self.complexity_score,
            "request_type": self.request_type,
            "total_steps": self.total_steps,
            "duration_ms": self.duration_ms,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class TraceStep(Base):
    """Model for storing individual steps within a reasoning trace."""
    
    __tablename__ = "trace_steps"
    
    id = Column(Integer, primary_key=True, index=True)
    trace_id = Column(Integer, ForeignKey("reasoning_traces.id"), index=True)
    request_id = Column(String(64), index=True, nullable=False)
    step_id = Column(Integer, nullable=False)
    agent = Column(String(64), index=True)
    type = Column(String(32), index=True)  # message, tool_use, etc.
    timestamp = Column(DateTime, index=True)
    task_id = Column(String(64), index=True, nullable=True)
    
    # Content fields for different step types
    content = Column(Text, nullable=True)  # For message type
    tool_name = Column(String(128), nullable=True)  # For tool_use type
    success = Column(Boolean, nullable=True)  # For tool_use type
    
    # Relationship back to parent trace
    trace = relationship("ReasoningTrace", back_populates="steps")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "trace_id": self.trace_id,
            "request_id": self.request_id,
            "step_id": self.step_id,
            "agent": self.agent,
            "type": self.type,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "task_id": self.task_id,
            "content": self.content,
            "tool_name": self.tool_name,
            "success": self.success
        }


class SEOAnalysis(Base):
    """Model for storing persistent SEO analysis data."""

    __tablename__ = "seo_analyses"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(String(64), unique=True, index=True, nullable=False)
    user_id = Column(String(64), index=True, nullable=True)  # For future user association
    url = Column(String(512), nullable=False, index=True)
    mode = Column(String(32), nullable=False, index=True)  # "page" or "website"
    status = Column(String(32), nullable=False, index=True)  # "pending", "in_progress", "complete", "error", "cancelled"

    # Progress tracking
    current_page = Column(Integer, default=0)
    total_pages = Column(Integer, default=0)
    phase = Column(String(64), nullable=True)  # "discovery", "analysis", "recommendations", "complete"
    status_message = Column(Text, nullable=True)

    # Timing information
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    estimated_completion = Column(DateTime, nullable=True)
    processing_time = Column(Float, nullable=True)

    # Results storage (JSON)
    _progress_data = Column("progress_data", Text)  # JSON string for progress details
    _result_data = Column("result_data", Text)  # JSON string for final results
    _error_data = Column("error_data", Text)  # JSON string for error information

    # Metadata
    pages_analyzed = Column(Integer, default=0)
    total_pages_found = Column(Integer, default=0)
    failed_urls_count = Column(Integer, default=0)
    ai_enhanced = Column(Boolean, default=False)

    @property
    def progress_data(self) -> Optional[Dict[str, Any]]:
        """Get progress data as dictionary."""
        if self._progress_data:
            try:
                return json.loads(self._progress_data)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @progress_data.setter
    def progress_data(self, value: Optional[Dict[str, Any]]):
        """Set progress data from dictionary."""
        if value is not None:
            self._progress_data = json.dumps(value)
        else:
            self._progress_data = None

    @property
    def result_data(self) -> Optional[Dict[str, Any]]:
        """Get result data as dictionary."""
        if self._result_data:
            try:
                return json.loads(self._result_data)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @result_data.setter
    def result_data(self, value: Optional[Dict[str, Any]]):
        """Set result data from dictionary."""
        if value is not None:
            self._result_data = json.dumps(value)
        else:
            self._result_data = None

    @property
    def error_data(self) -> Optional[Dict[str, Any]]:
        """Get error data as dictionary."""
        if self._error_data:
            try:
                return json.loads(self._error_data)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @error_data.setter
    def error_data(self, value: Optional[Dict[str, Any]]):
        """Set error data from dictionary."""
        if value is not None:
            self._error_data = json.dumps(value)
        else:
            self._error_data = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "analysis_id": self.analysis_id,
            "user_id": self.user_id,
            "url": self.url,
            "mode": self.mode,
            "status": self.status,
            "current_page": self.current_page,
            "total_pages": self.total_pages,
            "phase": self.phase,
            "status_message": self.status_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "estimated_completion": self.estimated_completion.isoformat() if self.estimated_completion else None,
            "processing_time": self.processing_time,
            "progress_data": self.progress_data,
            "result_data": self.result_data,
            "error_data": self.error_data,
            "pages_analyzed": self.pages_analyzed,
            "total_pages_found": self.total_pages_found,
            "failed_urls_count": self.failed_urls_count,
            "ai_enhanced": self.ai_enhanced
        }

# Create indexes for common query patterns
Index("ix_trace_steps_agent_type", TraceStep.agent, TraceStep.type)
Index("ix_reasoning_traces_request_type_complexity", ReasoningTrace.request_type, ReasoningTrace.complexity_score)
Index("ix_reasoning_traces_created_at_complexity", ReasoningTrace.created_at, ReasoningTrace.complexity_score)