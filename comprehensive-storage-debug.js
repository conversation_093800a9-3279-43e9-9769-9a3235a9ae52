/**
 * Comprehensive Supabase Storage Debug Tool
 * This script provides detailed debugging for image display issues
 * Run in browser console on Visual Complexity Analyzer page
 */

console.log('🔍 Comprehensive Supabase Storage Debug Tool Loaded');

class StorageDebugger {
  constructor() {
    this.results = {};
    this.errors = [];
  }

  async init() {
    try {
      // Import required modules
      this.supabase = (await import('/src/lib/supabase.ts')).supabase;
      this.designAnalysisService = (await import('/src/services/designAnalysisService.ts')).designAnalysisService;
      console.log('✅ Modules imported successfully');
    } catch (error) {
      console.error('❌ Failed to import modules:', error);
      throw error;
    }
  }

  async checkAuthentication() {
    console.log('\n🔐 Checking Authentication...');
    
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        this.errors.push(`Auth error: ${error.message}`);
        console.error('❌ Authentication error:', error);
        return false;
      }

      if (!session) {
        this.errors.push('No active session');
        console.log('❌ No active session');
        return false;
      }

      console.log('✅ Authentication successful:', {
        userId: session.user.id,
        email: session.user.email,
        tokenExpiry: new Date(session.expires_at * 1000).toISOString()
      });

      this.results.auth = {
        authenticated: true,
        userId: session.user.id,
        email: session.user.email
      };

      return true;
    } catch (error) {
      this.errors.push(`Auth check failed: ${error.message}`);
      console.error('❌ Auth check failed:', error);
      return false;
    }
  }

  async testBucketAccess() {
    console.log('\n🪣 Testing Bucket Access...');
    
    try {
      // Test bucket listing
      const { data: buckets, error: bucketsError } = await this.supabase.storage.listBuckets();
      
      if (bucketsError) {
        this.errors.push(`Bucket listing error: ${bucketsError.message}`);
        console.error('❌ Bucket listing error:', bucketsError);
        return false;
      }

      const designBucket = buckets.find(b => b.name === 'design-analysis-images');
      if (!designBucket) {
        this.errors.push('design-analysis-images bucket not found');
        console.error('❌ design-analysis-images bucket not found');
        return false;
      }

      console.log('✅ Bucket found:', {
        name: designBucket.name,
        public: designBucket.public,
        fileSizeLimit: designBucket.file_size_limit,
        allowedMimeTypes: designBucket.allowed_mime_types
      });

      this.results.bucket = designBucket;
      return true;
    } catch (error) {
      this.errors.push(`Bucket access failed: ${error.message}`);
      console.error('❌ Bucket access failed:', error);
      return false;
    }
  }

  async testFileAccess() {
    console.log('\n📁 Testing File Access...');
    
    if (!this.results.auth?.userId) {
      console.log('❌ No user ID available for file testing');
      return false;
    }

    try {
      // List files in user's folder
      const { data: files, error: filesError } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, { limit: 5 });

      if (filesError) {
        this.errors.push(`File listing error: ${filesError.message}`);
        console.error('❌ File listing error:', filesError);
        return false;
      }

      if (!files || files.length === 0) {
        console.log('⚠️ No files found in user folder');
        this.results.files = [];
        return true;
      }

      console.log(`✅ Found ${files.length} files:`, files.map(f => ({
        name: f.name,
        size: f.metadata?.size,
        lastModified: f.updated_at
      })));

      this.results.files = files;
      return true;
    } catch (error) {
      this.errors.push(`File access failed: ${error.message}`);
      console.error('❌ File access failed:', error);
      return false;
    }
  }

  async testImageLoadingMethods() {
    console.log('\n🖼️ Testing Image Loading Methods...');
    
    if (!this.results.files || this.results.files.length === 0) {
      console.log('⚠️ No files available for testing');
      return false;
    }

    const testFile = this.results.files[0];
    const filePath = `${this.results.auth.userId}/${testFile.name}`;
    
    console.log(`Testing with file: ${filePath}`);

    // Method 1: Public URL
    await this.testPublicUrl(filePath);
    
    // Method 2: Signed URL
    await this.testSignedUrl(filePath);
    
    // Method 3: Authenticated Download
    await this.testAuthenticatedDownload(filePath);
    
    // Method 4: Service Method
    await this.testServiceMethod(filePath);

    return true;
  }

  async testPublicUrl(filePath) {
    console.log('\n📡 Method 1: Public URL');
    
    try {
      const { data: { publicUrl } } = this.supabase.storage
        .from('design-analysis-images')
        .getPublicUrl(filePath);

      console.log('🔗 Public URL generated:', publicUrl);

      // Test accessibility
      const response = await fetch(publicUrl, { method: 'HEAD' });
      console.log('📊 Public URL test:', {
        status: response.status,
        statusText: response.statusText,
        accessible: response.ok
      });

      this.results.publicUrl = {
        url: publicUrl,
        accessible: response.ok,
        status: response.status
      };

    } catch (error) {
      console.error('❌ Public URL test failed:', error);
      this.errors.push(`Public URL test failed: ${error.message}`);
    }
  }

  async testSignedUrl(filePath) {
    console.log('\n🔐 Method 2: Signed URL');
    
    try {
      const { data, error } = await this.supabase.storage
        .from('design-analysis-images')
        .createSignedUrl(filePath, 3600); // 1 hour

      if (error) {
        console.error('❌ Signed URL creation failed:', error);
        this.errors.push(`Signed URL creation failed: ${error.message}`);
        return;
      }

      console.log('🔗 Signed URL generated:', data.signedUrl);

      // Test accessibility
      const response = await fetch(data.signedUrl, { method: 'HEAD' });
      console.log('📊 Signed URL test:', {
        status: response.status,
        statusText: response.statusText,
        accessible: response.ok
      });

      this.results.signedUrl = {
        url: data.signedUrl,
        accessible: response.ok,
        status: response.status
      };

    } catch (error) {
      console.error('❌ Signed URL test failed:', error);
      this.errors.push(`Signed URL test failed: ${error.message}`);
    }
  }

  async testAuthenticatedDownload(filePath) {
    console.log('\n🔒 Method 3: Authenticated Download');
    
    try {
      const { data: fileBlob, error } = await this.supabase.storage
        .from('design-analysis-images')
        .download(filePath);

      if (error) {
        console.error('❌ Authenticated download failed:', error);
        this.errors.push(`Authenticated download failed: ${error.message}`);
        return;
      }

      if (!fileBlob) {
        console.error('❌ No blob data returned');
        this.errors.push('No blob data returned from download');
        return;
      }

      console.log('✅ Authenticated download successful:', {
        size: fileBlob.size,
        type: fileBlob.type
      });

      // Create object URL
      const objectUrl = URL.createObjectURL(fileBlob);
      console.log('🔗 Object URL created:', objectUrl.substring(0, 50) + '...');

      this.results.authenticatedDownload = {
        success: true,
        blobSize: fileBlob.size,
        blobType: fileBlob.type,
        objectUrl: objectUrl
      };

      // Test if object URL works in an image element
      await this.testImageElement(objectUrl, 'Authenticated Download');

    } catch (error) {
      console.error('❌ Authenticated download test failed:', error);
      this.errors.push(`Authenticated download test failed: ${error.message}`);
    }
  }

  async testServiceMethod(filePath) {
    console.log('\n🛠️ Method 4: Service Method');
    
    try {
      const imageUrl = await this.designAnalysisService.getImageUrl(filePath);
      
      if (!imageUrl) {
        console.error('❌ Service method returned null');
        this.errors.push('Service method returned null');
        return;
      }

      console.log('✅ Service method returned URL:', imageUrl.substring(0, 50) + '...');

      this.results.serviceMethod = {
        url: imageUrl,
        isObjectUrl: imageUrl.startsWith('blob:'),
        isHttpUrl: imageUrl.startsWith('http')
      };

      // Test if service URL works in an image element
      await this.testImageElement(imageUrl, 'Service Method');

    } catch (error) {
      console.error('❌ Service method test failed:', error);
      this.errors.push(`Service method test failed: ${error.message}`);
    }
  }

  async testImageElement(url, method) {
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        console.log(`✅ ${method} - Image loaded successfully:`, {
          width: img.naturalWidth,
          height: img.naturalHeight,
          url: url.substring(0, 50) + '...'
        });
        resolve(true);
      };

      img.onerror = (error) => {
        console.error(`❌ ${method} - Image failed to load:`, error);
        this.errors.push(`${method} image load failed`);
        resolve(false);
      };

      img.src = url;
      
      // Timeout after 10 seconds
      setTimeout(() => {
        if (!img.complete) {
          console.error(`❌ ${method} - Image load timeout`);
          this.errors.push(`${method} image load timeout`);
          resolve(false);
        }
      }, 10000);
    });
  }

  async testCurrentComponentState() {
    console.log('\n⚛️ Testing Current Component State...');
    
    try {
      // Check if we're on the Visual Complexity Analyzer page
      const analyzeTab = document.querySelector('[value="analyze"]');
      const historyTab = document.querySelector('[value="history"]');
      
      if (!analyzeTab || !historyTab) {
        console.log('⚠️ Not on Visual Complexity Analyzer page');
        return false;
      }

      // Check current preview URL state
      const previewImages = document.querySelectorAll('img[src*="blob:"], img[src*="http"]');
      console.log(`📊 Found ${previewImages.length} preview images on page`);
      
      previewImages.forEach((img, index) => {
        console.log(`Image ${index + 1}:`, {
          src: img.src.substring(0, 50) + '...',
          loaded: img.complete && img.naturalHeight !== 0,
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      });

      this.results.componentState = {
        onCorrectPage: true,
        previewImagesCount: previewImages.length,
        previewImages: Array.from(previewImages).map(img => ({
          src: img.src,
          loaded: img.complete && img.naturalHeight !== 0
        }))
      };

      return true;
    } catch (error) {
      console.error('❌ Component state test failed:', error);
      this.errors.push(`Component state test failed: ${error.message}`);
      return false;
    }
  }

  async runFullDiagnostic() {
    console.log('🚀 Starting Comprehensive Storage Diagnostic...\n');
    
    try {
      await this.init();
      
      const authOk = await this.checkAuthentication();
      if (!authOk) return this.generateReport();
      
      const bucketOk = await this.testBucketAccess();
      if (!bucketOk) return this.generateReport();
      
      await this.testFileAccess();
      await this.testImageLoadingMethods();
      await this.testCurrentComponentState();
      
      return this.generateReport();
    } catch (error) {
      console.error('💥 Diagnostic failed:', error);
      this.errors.push(`Diagnostic failed: ${error.message}`);
      return this.generateReport();
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE STORAGE DIAGNOSTIC REPORT');
    console.log('='.repeat(60));
    
    // Authentication
    console.log('\n🔐 Authentication:', this.results.auth?.authenticated ? '✅ PASSED' : '❌ FAILED');
    
    // Bucket Access
    console.log('🪣 Bucket Access:', this.results.bucket ? '✅ PASSED' : '❌ FAILED');
    if (this.results.bucket) {
      console.log(`   - Bucket is ${this.results.bucket.public ? 'PUBLIC' : 'PRIVATE'}`);
    }
    
    // File Access
    console.log('📁 File Access:', this.results.files !== undefined ? '✅ PASSED' : '❌ FAILED');
    if (this.results.files) {
      console.log(`   - Found ${this.results.files.length} files`);
    }
    
    // Image Loading Methods
    console.log('\n🖼️ Image Loading Methods:');
    console.log('   📡 Public URL:', this.results.publicUrl?.accessible ? '✅ WORKS' : '❌ FAILS');
    console.log('   🔐 Signed URL:', this.results.signedUrl?.accessible ? '✅ WORKS' : '❌ FAILS');
    console.log('   🔒 Auth Download:', this.results.authenticatedDownload?.success ? '✅ WORKS' : '❌ FAILS');
    console.log('   🛠️ Service Method:', this.results.serviceMethod?.url ? '✅ WORKS' : '❌ FAILS');
    
    // Errors
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (this.results.signedUrl?.accessible) {
      console.log('   ✅ Use SIGNED URLs - they work with private buckets');
    } else if (this.results.authenticatedDownload?.success) {
      console.log('   ✅ Use AUTHENTICATED DOWNLOAD + Object URLs');
    } else if (this.results.publicUrl?.accessible) {
      console.log('   ⚠️ Only public URLs work - consider making bucket public');
    } else {
      console.log('   ❌ No working method found - check RLS policies');
    }
    
    console.log('\n' + '='.repeat(60));
    
    return {
      results: this.results,
      errors: this.errors,
      success: this.errors.length === 0
    };
  }
}

// Create global instance
window.storageDebugger = new StorageDebugger();

console.log('🔧 Comprehensive Storage Debugger loaded');
console.log('📝 Run: storageDebugger.runFullDiagnostic() to start comprehensive testing');
