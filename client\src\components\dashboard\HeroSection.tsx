"use client"
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { <PERSON><PERSON>ir<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import EmmaProfile from "@/assets/emma-profile.png";

export function HeroSection() {
  const [, navigate] = useLocation();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="relative overflow-visible rounded-2xl backdrop-blur-xl mb-8"
    >
      {/* Gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

      {/* Floating elements with glassmorphism */}
      <motion.div
        className="absolute right-0 bottom-0 transform translate-y-1/4 -translate-x-10 hidden lg:block"
        initial={{ opacity: 0, x: 100, rotate: -10 }}
        animate={{ opacity: 1, x: 0, rotate: 0 }}
        transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
      >
        <div className="relative w-72 h-72">
          <motion.div
            className="absolute w-32 h-32 bg-white/20 backdrop-blur-md rounded-2xl -top-24 -left-16 transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
            animate={{ rotate: [12, 18, 12] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          >
            <span className="text-5xl">✨</span>
          </motion.div>
          <motion.div
            className="absolute w-36 h-36 bg-white/15 backdrop-blur-md rounded-2xl -top-8 left-16 transform -rotate-6 flex items-center justify-center shadow-2xl border border-white/20"
            animate={{ rotate: [-6, -12, -6] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          >
            <span className="text-5xl">📊</span>
          </motion.div>
          <motion.div
            className="absolute w-28 h-28 bg-white/25 backdrop-blur-md rounded-2xl top-20 -left-8 transform rotate-45 flex items-center justify-center shadow-2xl border border-white/40"
            animate={{ rotate: [45, 50, 45] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 2 }}
          >
            <span className="text-4xl">🔍</span>
          </motion.div>
        </div>
      </motion.div>

      <div className="relative px-8 py-8 md:py-12 md:px-12 overflow-visible">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-start justify-between gap-8">
            {/* Contenido principal */}
            <div className="flex-1 max-w-4xl">
              <div className="flex flex-col items-start">
                <div className="w-full">
                  <div className="flex items-center gap-6 mb-6">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                    >
                      <Avatar className="h-20 w-20 border-2 border-white/30 shadow-xl backdrop-blur-md">
                        <AvatarImage src={EmmaProfile} alt="Emma AI" />
                        <AvatarFallback className="bg-white/20 backdrop-blur-md text-white text-2xl font-bold">
                          E
                        </AvatarFallback>
                      </Avatar>
                    </motion.div>
                    <div>
                      <motion.span
                        className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-4 py-2 rounded-full mb-4 border border-white/30 text-sm"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        whileHover={{ scale: 1.05 }}
                      >
                        <Sparkles className="inline-block w-4 h-4 mr-2" />
                        Potenciado con IA
                      </motion.span>
                      <motion.h1
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        className="text-3xl lg:text-4xl font-black mb-2 leading-tight"
                      >
                        <span className="text-white">El Marketing Ya </span>
                        <span className="bg-gradient-to-r from-[#dd3a5a] via-[#f472b6] to-[#fbbf24] bg-clip-text text-transparent">
                          Cambió
                        </span>
                      </motion.h1>
                      <motion.p
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                        className="text-lg text-white/90 font-light"
                      >
                        Marketing • Diseño • Analytics • Growth
                      </motion.p>
                    </div>
                  </div>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="text-lg text-white/90 mb-6 leading-relaxed max-w-3xl font-light"
                  >
                    Soy Emma, tu agente de marketing con IA. Creo contenido viral, analizo competencia
                    y genero estrategias que funcionan. Simple, rápido, efectivo.
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="flex flex-wrap gap-4"
                  >
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        onClick={() => navigate("/emma-ai")}
                        size="lg"
                        className="bg-white/90 backdrop-blur-md hover:bg-white text-[#3018ef] font-bold px-8 py-3 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/30"
                      >
                        <MessageCircle className="mr-2 h-5 w-5" />
                        Hablar con Emma
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        onClick={() => navigate("/dashboard/herramientas-marketing")}
                        variant="outline"
                        size="lg"
                        className="border-2 border-white text-white hover:bg-white hover:text-[#3018ef] font-bold px-8 py-3 rounded-xl backdrop-blur-md transition-all duration-300 shadow-xl hover:shadow-2xl bg-white/10 hover:bg-white"
                      >
                        Ver herramientas
                      </Button>
                    </motion.div>
                  </motion.div>

                  {/* Métricas para móvil */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.0 }}
                    className="lg:hidden mt-8"
                  >
                    <div className="grid grid-cols-2 gap-3">
                      {[
                        { value: "+300%", label: "ROI Promedio", icon: "📈" },
                        { value: "24/7", label: "Disponible", icon: "⏰" },
                        { value: "10x", label: "Más Rápida", icon: "⚡" },
                        { value: "95%", label: "Precisión", icon: "🎯" }
                      ].map((stat, index) => (
                        <motion.div
                          key={index}
                          whileHover={{ scale: 1.05, y: -2 }}
                          transition={{ type: "spring", stiffness: 400, damping: 25 }}
                        >
                          <Card className="border-white/20 bg-white/20 backdrop-blur-md hover:bg-white/30 transition-all duration-300 shadow-xl">
                            <CardContent className="p-3 text-center">
                              <div className="text-lg mb-1">{stat.icon}</div>
                              <div className="text-lg font-bold text-white mb-1">{stat.value}</div>
                              <div className="text-xs text-white/80">{stat.label}</div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Métricas al lado derecho - más cerca */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.9 }}
              className="hidden lg:block w-56 flex-shrink-0 ml-4"
            >
              <div className="grid grid-cols-2 gap-2">
                {[
                  { value: "+300%", label: "ROI Promedio", icon: "📈" },
                  { value: "24/7", label: "Disponible", icon: "⏰" },
                  { value: "10x", label: "Más Rápida", icon: "⚡" },
                  { value: "95%", label: "Precisión", icon: "🎯" }
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.05, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <Card className="border-white/20 bg-white/20 backdrop-blur-md hover:bg-white/30 transition-all duration-300 shadow-xl">
                      <CardContent className="p-2.5 text-center">
                        <div className="text-base mb-1">{stat.icon}</div>
                        <div className="text-base font-bold text-white mb-1">{stat.value}</div>
                        <div className="text-xs text-white/80">{stat.label}</div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
