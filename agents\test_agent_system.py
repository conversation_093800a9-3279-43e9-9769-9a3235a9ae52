#!/usr/bin/env python
"""
Test script for the Emma Studio agent system.
This script demonstrates the basic functionality of the agent system.
"""

import os
import sys
import asyncio
import logging
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the agents package
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents import (
    AgentOrchestrator,
    TaskStatus,
    TaskPriority,
    AgentTask
)
from agents.specialized import EmmaAgent, SEOAgent, ContentAgent
from agents.llm_providers import GeminiProvider, OpenAIProvider

# Create a simple mock provider
class MockLLMProvider:
    async def generate(self, prompt: str, **kwargs) -> str:
        logger.info(f"Generating text with mock provider for prompt: {prompt[:100]}...")
        return f"This is a simulated response to: {prompt[:50]}..."

async def run_test():
    """Run a test of the agent system."""
    logger.info("Testing Emma Studio agent system...")
    
    # Initialize the LLM provider
    # Try to use Gemini first, fall back to OpenAI if Gemini is not available
    gemini_api_key = os.environ.get("GEMINI_API_KEY")
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    
    if gemini_api_key:
        llm_provider = GeminiProvider(api_key=gemini_api_key)
        logger.info("Using Gemini LLM provider")
    elif openai_api_key:
        llm_provider = OpenAIProvider(api_key=openai_api_key)
        logger.info("Using OpenAI LLM provider")
    else:
        llm_provider = MockLLMProvider()
        logger.warning("No LLM API keys found. Using mock provider.")
    
    # Initialize the agent orchestrator
    orchestrator = AgentOrchestrator("Test Orchestrator")
    
    # Create and register agents
    emma_agent = EmmaAgent("emma", "Emma", llm_provider)
    seo_agent = SEOAgent("seo", "SEO Specialist", llm_provider)
    content_agent = ContentAgent("content", "Content Creator", llm_provider)
    
    # Register all agents with the orchestrator
    orchestrator.register_agent(emma_agent)
    orchestrator.register_agent(seo_agent)
    orchestrator.register_agent(content_agent)
    
    # Create a workflow
    workflow = orchestrator.create_workflow(
        name="Test Workflow",
        description="Test workflow for the agent system",
        priority=TaskPriority.MEDIUM
    )
    
    # Create tasks
    main_task = orchestrator.create_task(
        description="Create a blog post about AI in marketing",
        priority=TaskPriority.HIGH,
        assigned_to=emma_agent.id
    )
    
    content_task = orchestrator.create_task(
        description="Write a detailed blog post about AI in marketing",
        priority=TaskPriority.MEDIUM,
        assigned_to=content_agent.id,
        dependencies=[main_task.id]
    )
    
    seo_task = orchestrator.create_task(
        description="Optimize the blog post for SEO",
        priority=TaskPriority.MEDIUM,
        assigned_to=seo_agent.id,
        dependencies=[content_task.id]
    )
    
    # Add tasks to the workflow
    orchestrator.add_task_to_workflow(workflow.id, main_task)
    orchestrator.add_task_to_workflow(workflow.id, content_task)
    orchestrator.add_task_to_workflow(workflow.id, seo_task)
    
    # Execute the workflow
    logger.info("Executing workflow...")
    result = await orchestrator.execute_workflow(workflow.id)
    
    # Check the result
    if result.status == TaskStatus.COMPLETED:
        logger.info("Workflow completed successfully!")
        for task_result in result.task_results:
            logger.info(f"Task {task_result.task_id}: {task_result.status}")
            if task_result.data:
                logger.info(f"Result preview: {str(task_result.data)[:100]}...")
    else:
        logger.error(f"Workflow failed: {result.error}")
    
    return result.status == TaskStatus.COMPLETED

if __name__ == "__main__":
    success = asyncio.run(run_test())
    sys.exit(0 if success else 1)
