# User Palette Database Integration - Testing Guide

This guide provides comprehensive testing instructions for the user-defined color palette database integration with Supabase.

## Prerequisites

1. **Database Setup**: Ensure the Supabase schema has been applied with the `user_palettes` table and RLS policies
2. **Backend Running**: Start the backend server on port 5001
3. **Frontend Running**: Start the frontend on port 3002
4. **User Accounts**: Have at least 2 test user accounts for cross-user testing

## Manual Testing Checklist

### 1. Database Schema Verification

**Check that the following exists in Supabase:**
- [ ] `user_palettes` table with correct structure
- [ ] RLS policies enabled on `user_palettes` table
- [ ] Proper indexes created
- [ ] Trigger for `updated_at` field

**SQL to verify:**
```sql
-- Check table structure
\d user_palettes;

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'user_palettes';

-- Check indexes
SELECT indexname, indexdef FROM pg_indexes WHERE tablename = 'user_palettes';
```

### 2. Authentication Testing

**Test Cases:**
- [ ] **Unauthenticated Access**: Verify that palette endpoints return 401 for unauthenticated users
- [ ] **Valid Authentication**: Verify that authenticated users can access palette endpoints
- [ ] **Invalid Token**: Verify that invalid/expired tokens are rejected

**Manual Steps:**
1. Try accessing `/api/palettes` without authentication
2. Login with valid credentials and access `/api/palettes`
3. Use an invalid token and verify rejection

### 3. Palette CRUD Operations

#### 3.1 Create Palette
**Test Cases:**
- [ ] **Valid Palette**: Create palette with valid data
- [ ] **Invalid Name**: Try creating palette with empty/too long name
- [ ] **Invalid Colors**: Try creating palette with invalid hex colors
- [ ] **Too Many Colors**: Try creating palette with >20 colors
- [ ] **No Colors**: Try creating palette with empty colors array

**Manual Steps:**
1. Open Color Palette Generator
2. Generate a palette
3. Click "Guardar en la nube"
4. Fill in valid name and description
5. Verify palette is saved successfully
6. Try edge cases with invalid data

#### 3.2 Read Palettes
**Test Cases:**
- [ ] **List Own Palettes**: User can see their own palettes
- [ ] **Empty List**: New user sees empty palette list
- [ ] **Pagination**: Test limit/offset parameters
- [ ] **Favorite Filter**: Test filtering by favorite status

**Manual Steps:**
1. Login as User A
2. Create several palettes
3. Click "Cargar paleta guardada"
4. Verify only User A's palettes are shown
5. Test favorite filtering

#### 3.3 Update Palette
**Test Cases:**
- [ ] **Valid Update**: Update palette name, description, colors
- [ ] **Partial Update**: Update only some fields
- [ ] **Invalid Data**: Try updating with invalid data
- [ ] **Non-existent Palette**: Try updating palette that doesn't exist

**Manual Steps:**
1. Create a palette
2. Modify the palette data via API
3. Verify changes are saved
4. Try updating with invalid data

#### 3.4 Delete Palette
**Test Cases:**
- [ ] **Valid Deletion**: Delete own palette
- [ ] **Non-existent Palette**: Try deleting palette that doesn't exist
- [ ] **Already Deleted**: Try deleting same palette twice

**Manual Steps:**
1. Create a palette
2. Delete it using the trash icon
3. Verify it's removed from the list
4. Try deleting again

#### 3.5 Toggle Favorite
**Test Cases:**
- [ ] **Add to Favorites**: Mark palette as favorite
- [ ] **Remove from Favorites**: Unmark palette as favorite
- [ ] **Non-existent Palette**: Try toggling favorite on non-existent palette

**Manual Steps:**
1. Create a palette
2. Click the heart icon to add to favorites
3. Verify favorite status changes
4. Click again to remove from favorites

### 4. Row Level Security (RLS) Testing

#### 4.1 Cross-User Access Prevention
**Test Cases:**
- [ ] **Cannot Read Other's Palettes**: User A cannot see User B's palettes
- [ ] **Cannot Update Other's Palettes**: User A cannot modify User B's palettes
- [ ] **Cannot Delete Other's Palettes**: User A cannot delete User B's palettes
- [ ] **Cannot Access Other's Palette by ID**: Direct access to other user's palette ID fails

**Manual Steps:**
1. Login as User A, create palette, note the palette ID
2. Login as User B
3. Try to access User A's palette via direct API call
4. Verify access is denied (404 or 403)
5. Try to modify/delete User A's palette
6. Verify all operations are blocked

#### 4.2 Automated Security Testing
**Run the security test script:**
```bash
python test_palette_security.py
```

**Expected Results:**
- All tests should pass
- No cross-user access should be possible
- Proper error codes (401, 403, 404) for unauthorized access

### 5. Frontend Integration Testing

#### 5.1 Color Palette Generator Integration
**Test Cases:**
- [ ] **Save Dialog**: Save dialog opens and works correctly
- [ ] **Load Dialog**: Load dialog shows saved palettes
- [ ] **Palette Loading**: Clicking "Cargar" loads palette into generator
- [ ] **Authentication State**: Different UI for authenticated vs unauthenticated users
- [ ] **Error Handling**: Proper error messages for failed operations

**Manual Steps:**
1. Generate a color palette
2. Save it with a name and description
3. Generate a new palette
4. Load the saved palette
5. Verify the original palette is restored
6. Test error scenarios (network issues, invalid data)

#### 5.2 User Experience Testing
**Test Cases:**
- [ ] **Loading States**: Proper loading indicators during operations
- [ ] **Success Messages**: Toast notifications for successful operations
- [ ] **Error Messages**: Clear error messages for failures
- [ ] **Responsive Design**: UI works on different screen sizes
- [ ] **Accessibility**: Keyboard navigation and screen reader support

### 6. Performance Testing

#### 6.1 Database Performance
**Test Cases:**
- [ ] **Large Palette Lists**: Test with 50+ palettes
- [ ] **Complex Colors**: Test with maximum 20 colors per palette
- [ ] **Concurrent Users**: Multiple users creating palettes simultaneously

#### 6.2 Frontend Performance
**Test Cases:**
- [ ] **Palette Loading Speed**: Large palette lists load quickly
- [ ] **Color Rendering**: Complex palettes render smoothly
- [ ] **Memory Usage**: No memory leaks during extended use

### 7. Edge Cases and Error Handling

**Test Cases:**
- [ ] **Network Failures**: Handle network disconnections gracefully
- [ ] **Server Errors**: Handle 500 errors appropriately
- [ ] **Invalid Responses**: Handle malformed API responses
- [ ] **Rate Limiting**: Handle rate limit responses
- [ ] **Large Payloads**: Handle large palette data

### 8. Data Validation Testing

#### 8.1 Backend Validation
**Test Cases:**
- [ ] **Hex Color Validation**: Only valid hex colors accepted
- [ ] **Name Length**: Names within 1-100 character limit
- [ ] **Description Length**: Descriptions within 500 character limit
- [ ] **Color Count**: 1-20 colors per palette
- [ ] **Tag Count**: Maximum 10 tags per palette

#### 8.2 Frontend Validation
**Test Cases:**
- [ ] **Real-time Validation**: Form validation before submission
- [ ] **User Feedback**: Clear validation error messages
- [ ] **Input Sanitization**: Proper handling of special characters

## Test Results Documentation

### Security Test Results
- [ ] All RLS policies working correctly
- [ ] No cross-user access possible
- [ ] Proper authentication required
- [ ] Error codes appropriate

### Functionality Test Results
- [ ] All CRUD operations working
- [ ] Frontend integration complete
- [ ] Error handling robust
- [ ] Performance acceptable

### Issues Found
Document any issues discovered during testing:

1. **Issue**: [Description]
   - **Severity**: High/Medium/Low
   - **Steps to Reproduce**: [Steps]
   - **Expected**: [Expected behavior]
   - **Actual**: [Actual behavior]
   - **Status**: Open/Fixed

## Conclusion

After completing all tests, the user palette database integration should:
- ✅ Securely store user palettes with proper RLS
- ✅ Provide full CRUD functionality
- ✅ Integrate seamlessly with the frontend
- ✅ Handle errors gracefully
- ✅ Perform well under normal load
- ✅ Validate all user input properly

**Final Verification**: The implementation is ready for production when all test cases pass and no security vulnerabilities are found.
