"""
Content Service Module for Agent System Integration

This module provides integration between FastAPI endpoints and the custom agent system.
It handles content generation requests and translates them into agent workflows.
Includes comprehensive trace collection, storage, and error handling.
"""

import logging
import uuid
import time
import os
import yaml  # Explicit import to fix mypy error
from typing import Dict, Any, Optional, List

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Depends
from sqlalchemy.orm import Session

from app.db.models import ReasoningTrace, TraceStep
from app.db.history_store import save_trace_to_history, get_trace, get_recent_traces
from app.schemas.content import (
    GenerateContentRequest,
    GenerateContentResponse,
    ContentStatus
)

# Temporarily commented out until agents module is fixed
# from agents import (
#     AgentOrchestrator,
#     TaskStatus,
#     TaskPriority,
#     AgentTask,
#     ContextType
# )
# from agents.specialized import EmmaAgent, SEOAgent, ContentAgent
# from agents.llm_providers import GeminiProvider, OpenAIProvider

# Mock classes for temporary compatibility
class TaskStatus:
    COMPLETED = "completed"
    FAILED = "failed"

class TaskPriority:
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class AgentTask:
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', 'mock-task')

class AgentOrchestrator:
    def __init__(self):
        pass

logger = logging.getLogger(__name__)

class ContentService:
    """Service for content generation using the custom agent system."""

    def __init__(self):
        """Initialize the ContentService with all required components."""
        # Temporarily disabled until agents module is fixed
        logger.warning("ContentService initialized in minimal mode - agents module disabled")
        pass

        # Initialize LLM provider
        # gemini_api_key = os.environ.get("GEMINI_API_KEY")
        # openai_api_key = os.environ.get("OPENAI_API_KEY")

        # Temporarily commented out - agents module disabled
        # Choose the appropriate provider
        # if gemini_api_key:
        #     self.llm_provider = GeminiProvider(api_key=gemini_api_key)
        #     logger.info("Using Gemini LLM provider")
        # elif openai_api_key:
        #     self.llm_provider = OpenAIProvider(api_key=openai_api_key)
        #     logger.info("Using OpenAI LLM provider")
        # else:
        #     # Create a simple mock provider
        #     class MockLLMProvider:
        #         async def generate(self, prompt: str, **kwargs) -> str:
        #             logger.info(f"Generating text with mock provider for prompt: {prompt[:100]}...")
        #             return f"This is a simulated response to: {prompt[:50]}..."

        #     self.llm_provider = MockLLMProvider()
        #     logger.warning("No LLM API keys found. Using mock provider.")

        # # Initialize the agent orchestrator
        # self.orchestrator = AgentOrchestrator("Content Orchestrator")

        # # Create and register agents
        # self.emma_agent = EmmaAgent("emma", "Emma", self.llm_provider)
        # self.seo_agent = SEOAgent("seo", "SEO Specialist", self.llm_provider)
        # self.content_agent = ContentAgent("content", "Content Creator", self.llm_provider)

        # # Register all agents with the orchestrator
        # self.orchestrator.register_agent(self.emma_agent)
        # self.orchestrator.register_agent(self.seo_agent)
        # self.orchestrator.register_agent(self.content_agent)

        # Initialize task templates
        self.task_templates = {}

        # Load configuration files on startup
        try:
            self._preload_config_files()
            logger.info("ContentService initialized with pre-loaded configurations")
        except Exception as e:
            logger.warning(f"Could not preload configuration files: {str(e)}")

    def _preload_config_files(self):
        """Preload and validate configuration files to detect issues early."""
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config")
        config_files = ["agents.yaml", "tools.yaml", "tasks.yaml", "crew.yaml"]

        self.configs = {}

        for filename in config_files:
            filepath = os.path.join(config_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, "r") as f:
                    try:
                        self.configs[filename] = yaml.safe_load(f)
                        logger.debug(f"Preloaded {filename} configuration")
                    except yaml.YAMLError as e:
                        logger.error(f"Error parsing {filename}: {str(e)}")
                        raise ValueError(f"Invalid YAML in {filename}: {str(e)}")
            else:
                logger.warning(f"Configuration file {filepath} not found")

    async def generate_content(self, request: GenerateContentRequest) -> GenerateContentResponse:
        """Generate content using Gemini AI directly (simplified implementation)."""
        import google.generativeai as genai
        import os
        import uuid
        import time

        # Get Gemini API key
        gemini_api_key = os.environ.get("GEMINI_API_KEY")
        if not gemini_api_key:
            raise HTTPException(
                status_code=503,
                detail="Gemini API key not configured"
            )

        # Configure Gemini
        genai.configure(api_key=gemini_api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')

        # Generate request ID
        request_id = request.request_id or str(uuid.uuid4())
        start_time = time.time()

        try:
            # Create prompt based on request type
            if request.type == "social_media":
                platform = getattr(request, 'platform', 'general')
                tone = request.tone or "professional"

                prompt = f"""
                Create a social media post for {platform} about: {request.prompt}

                Requirements:
                - Tone: {tone}
                - Platform: {platform}
                - Include relevant hashtags if appropriate
                - Keep it engaging and platform-appropriate
                - Write in Spanish

                Generate only the post content, no additional explanations.
                """
            else:
                prompt = f"Create content about: {request.prompt}"

            # Generate content
            response = model.generate_content(prompt)
            result = response.text

            execution_time = time.time() - start_time

            return GenerateContentResponse(
                request_id=request_id,
                status=ContentStatus.COMPLETED,
                result=result,
                metadata={
                    "execution_time_seconds": execution_time,
                    "model_used": "gemini-1.5-flash",
                    "simplified_mode": True
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error generating content: {str(e)}")

            return GenerateContentResponse(
                request_id=request_id,
                status=ContentStatus.FAILED,
                result="",
                error=str(e),
                metadata={
                    "execution_time_seconds": execution_time,
                    "simplified_mode": True
                }
            )

    async def _generate_content_original(self, request: GenerateContentRequest) -> GenerateContentResponse:
        """
        Generate content using the agent system based on the provided request.

        Args:
            request: The content generation request

        Returns:
            Response with generated content and reasoning trace
        """
        start_time = time.time()
        logger.info(f"Processing content generation request: {request.prompt[:50]}...")

        # Generate a unique request ID if not provided
        request_id = request.request_id or str(uuid.uuid4())

        try:
            # Create a workflow for this request
            workflow = self.orchestrator.create_workflow(
                name=f"Content Generation: {request.type or 'general'}",
                description=request.prompt,
                priority=TaskPriority.HIGH
            )

            # Create the main task for Emma agent
            main_task = self.orchestrator.create_task(
                description=request.prompt,
                priority=TaskPriority.HIGH,
                assigned_to=self.emma_agent.id,
                metadata={
                    "request_id": request_id,
                    "type": request.type,
                    "topic": request.topic,
                    "audience": request.audience,
                    "tone": request.tone,
                    "context": request.context,
                    "timestamp": time.time(),
                    "client_info": request.client_info if hasattr(request, "client_info") else {},
                    "priority": request.priority if hasattr(request, "priority") else "normal"
                }
            )

            # Add the main task to the workflow
            self.orchestrator.add_task_to_workflow(workflow.id, main_task)

            # Determine if we need specialized agents based on the request
            if request.type == "content" or "content" in request.prompt.lower() or "write" in request.prompt.lower():
                # Create a content task
                content_task = self.orchestrator.create_task(
                    description=f"Create content for: {request.prompt}",
                    priority=TaskPriority.MEDIUM,
                    assigned_to=self.content_agent.id,
                    dependencies=[main_task.id],
                    metadata={
                        "request_id": request_id,
                        "parent_task": main_task.id,
                        "content_type": request.type or "blog"
                    }
                )
                self.orchestrator.add_task_to_workflow(workflow.id, content_task)

            if request.type == "seo" or "seo" in request.prompt.lower() or "search" in request.prompt.lower():
                # Create an SEO task
                seo_task = self.orchestrator.create_task(
                    description=f"Analyze SEO for: {request.prompt}",
                    priority=TaskPriority.MEDIUM,
                    assigned_to=self.seo_agent.id,
                    dependencies=[main_task.id],
                    metadata={
                        "request_id": request_id,
                        "parent_task": main_task.id,
                        "content_type": request.type or "blog"
                    }
                )
                self.orchestrator.add_task_to_workflow(workflow.id, seo_task)

            # Execute the workflow
            logger.info(f"Executing agent workflow for request {request_id}")
            result = await self.orchestrator.execute_workflow(workflow.id)

            # Log execution time
            execution_time = time.time() - start_time
            logger.info(f"Content generation for request {request_id} completed in {execution_time:.2f} seconds")

            # Extract the result
            workflow_result = ""
            reasoning_trace = []

            if result.status == TaskStatus.COMPLETED:
                # Get the result from the task results
                for task_result in result.task_results:
                    if task_result.status == TaskStatus.COMPLETED and task_result.data:
                        workflow_result = task_result.data

                        # Add to reasoning trace
                        reasoning_trace.append({
                            "agent": task_result.task_id,
                            "action": "process",
                            "timestamp": task_result.timestamp,
                            "result": "success"
                        })
                        break

            # Create trace data
            trace_data = {
                "steps": reasoning_trace,
                "execution_time_seconds": execution_time,
                "request_details": {
                    "type": request.type,
                    "topic": request.topic,
                    "audience": request.audience,
                    "tone": request.tone
                }
            }

            # Save the trace to history
            trace_id = save_trace_to_history(
                request_id=request_id,
                user_prompt=request.prompt,
                result=workflow_result,
                trace=trace_data
            )

            # Prepare response with comprehensive metadata
            response = GenerateContentResponse(
                request_id=request_id,
                status=ContentStatus.COMPLETED if result.status == TaskStatus.COMPLETED else ContentStatus.FAILED,
                result=workflow_result,
                reasoning_trace=trace_data,
                metadata={
                    "execution_time_seconds": execution_time,
                    "trace_id": trace_id,
                    "agent_count": len(self.orchestrator.agents),
                    "task_count": len(result.task_results),
                    "total_steps": len(reasoning_trace)
                }
            )

            if result.status != TaskStatus.COMPLETED:
                response.error = result.error or "Unknown error"
                logger.error(f"Content generation error for request {request_id}: {response.error}")

            return response

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error generating content for request {request_id}: {str(e)}", exc_info=True)

            # Create error info
            error_info = {
                "error": str(e),
                "request_id": request_id,
                "execution_time_seconds": execution_time,
                "timestamp": time.time()
            }

            # Handle different types of errors with appropriate status codes
            if "configuration" in str(e).lower():
                status_code = 400  # Bad Request for configuration issues
            elif "timeout" in str(e).lower() or "timed out" in str(e).lower():
                status_code = 408  # Request Timeout
            else:
                status_code = 500  # Internal Server Error as default

            raise HTTPException(
                status_code=status_code,
                detail={
                    "message": f"Content generation failed: {str(e)}",
                    "error_info": error_info,
                    "partial_results": None
                }
            )

    def _configure_from_yaml(self):
        """Configure the agent system from YAML configuration files."""
        try:
            config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config")

            # Use preloaded configs if available, otherwise load from files
            agents_config = self.configs.get("agents.yaml")
            if not agents_config:
                agents_path = os.path.join(config_dir, "agents.yaml")
                if os.path.exists(agents_path):
                    with open(agents_path, "r") as f:
                        agents_config = yaml.safe_load(f)
                else:
                    logger.warning("No agents.yaml file found, using default agents")
                    agents_config = {}

            # Configure agents with additional properties if available
            if "agents" in agents_config:
                for agent_key, agent_data in agents_config.get("agents", {}).items():
                    if agent_key == "emma" and hasattr(self, "emma_agent"):
                        # Update Emma agent properties
                        if "capabilities" in agent_data:
                            self.emma_agent.agent_identity.capabilities.extend(agent_data["capabilities"])
                        if "description" in agent_data:
                            self.emma_agent.agent_identity.description = agent_data["description"]

                    elif agent_key == "seo" and hasattr(self, "seo_agent"):
                        # Update SEO agent properties
                        if "capabilities" in agent_data:
                            self.seo_agent.agent_identity.capabilities.extend(agent_data["capabilities"])
                        if "description" in agent_data:
                            self.seo_agent.agent_identity.description = agent_data["description"]

                    elif agent_key == "content" and hasattr(self, "content_agent"):
                        # Update Content agent properties
                        if "capabilities" in agent_data:
                            self.content_agent.agent_identity.capabilities.extend(agent_data["capabilities"])
                        if "description" in agent_data:
                            self.content_agent.agent_identity.description = agent_data["description"]
            else:
                logger.warning("No agents found in configuration, using default agent settings")

            # Load task templates
            tasks_config = self.configs.get("tasks.yaml")
            if not tasks_config:
                tasks_path = os.path.join(config_dir, "tasks.yaml")
                if os.path.exists(tasks_path):
                    with open(tasks_path, "r") as f:
                        tasks_config = yaml.safe_load(f)
                    self.task_templates = tasks_config.get("templates", {})
                else:
                    logger.warning("No tasks.yaml file found, using default task templates")

            logger.info("Successfully configured agent system from YAML files")

        except Exception as e:
            logger.error(f"Error loading configuration from YAML: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to configure agent system: {str(e)}")

    def _create_tasks_for_request(self, request: GenerateContentRequest) -> List[AgentTask]:
        """Temporarily disabled."""
        return []

    def _create_tasks_for_request_original(self, request: GenerateContentRequest) -> List[AgentTask]:
        """
        Create appropriate tasks based on the request type and complexity.

        Args:
            request: The content generation request

        Returns:
            List of AgentTask objects configured for the request
        """
        try:
            # Load task templates if not already loaded
            if not self.task_templates:
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config")
                task_config = self.configs.get("tasks.yaml")
                if not task_config:
                    tasks_path = os.path.join(config_dir, "tasks.yaml")
                    if os.path.exists(tasks_path):
                        with open(tasks_path, "r") as f:
                            task_config = yaml.safe_load(f)
                        self.task_templates = task_config.get("templates", {})
                    else:
                        logger.warning("No tasks.yaml file found, using default task templates")
                        self.task_templates = {}

            # Determine which template set to use based on request type
            request_type = request.type or "general_content"
            template_set = request_type.lower()

            # Prepare variables for task templates
            variables = {
                "prompt": request.prompt,
                "topic": request.topic or "",
                "audience": request.audience or "general audience",
                "tone": request.tone or "professional",
                "context": request.context or "",
                "brand_name": getattr(request, "brand", "") or "Our brand"
            }

            tasks = []

            # Create tasks based on templates for this request type
            if template_set in self.task_templates:
                for task_template in self.task_templates[template_set]:
                    # Find the specified agent
                    agent_name = task_template.get("agent", "")
                    agent_id = None

                    if agent_name.lower() == "emma":
                        agent_id = self.emma_agent.id
                    elif agent_name.lower() == "seo":
                        agent_id = self.seo_agent.id
                    elif agent_name.lower() == "content":
                        agent_id = self.content_agent.id

                    if agent_id:
                        # Create task with template and variables
                        description = task_template.get("description", "").format(**variables)
                        expected_output = task_template.get("expected_output", "").format(**variables)

                        task = self.orchestrator.create_task(
                            description=description,
                            priority=TaskPriority.MEDIUM,
                            assigned_to=agent_id,
                            metadata={
                                "expected_output": expected_output,
                                "template_name": task_template.get("name", ""),
                                "request_type": request_type
                            }
                        )
                        tasks.append(task)
                    else:
                        logger.warning(f"Agent '{agent_name}' not found for task template")
            else:
                # Fallback to default tasks if no specific templates for this request type
                logger.info(f"No task templates found for request type '{template_set}', using defaults")

                # Create a default task for Emma agent
                main_task = self.orchestrator.create_task(
                    description=f"Analyze and respond to this request: '{request.prompt}'",
                    priority=TaskPriority.HIGH,
                    assigned_to=self.emma_agent.id,
                    metadata={
                        "expected_output": "A comprehensive response addressing the user's request",
                        "request_type": request_type
                    }
                )
                tasks.append(main_task)

            return tasks

        except Exception as e:
            logger.error(f"Error creating tasks for request: {str(e)}", exc_info=True)
            # Return empty list on error to allow the system to continue with default tasks
            return []

    async def get_content_trace(self, request_id: str) -> Dict[str, Any]:
        """
        Retrieve the reasoning trace for a specific content generation request.

        Args:
            request_id: The unique identifier for the request

        Returns:
            The complete reasoning trace with structured data
        """
        trace = get_trace(request_id)
        if not trace:
            raise HTTPException(
                status_code=404,
                detail=f"Trace not found for request ID: {request_id}"
            )

        return trace

    async def get_recent_traces(self, limit: int = 10, offset: int = 0, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Retrieve recent reasoning traces with pagination.

        Args:
            limit: Maximum number of traces to retrieve
            offset: Number of traces to skip for pagination
            user_id: Optional user ID to filter results

        Returns:
            List of recent trace summaries
        """
        traces = get_recent_traces(limit=limit, offset=offset, user_id=user_id)
        return traces

    async def get_traces_count(self, user_id: Optional[str] = None) -> int:
        """
        Get the total count of traces, optionally filtered by user_id.

        Args:
            user_id: Optional user ID to filter results

        Returns:
            Total count of traces
        """
        return get_traces_count(user_id=user_id)


# Create a singleton instance of the service
content_service = ContentService()

# Convenience functions for API endpoints

async def generate_content_service(request: GenerateContentRequest) -> GenerateContentResponse:
    """
    Service function for the content generation endpoint.

    Args:
        request: The content generation request

    Returns:
        Response with generated content and reasoning trace
    """
    return await content_service.generate_content(request)

async def get_content_trace_service(request_id: str) -> Dict[str, Any]:
    """
    Service function for retrieving content generation trace.

    Args:
        request_id: The unique identifier for the request

    Returns:
        The complete reasoning trace
    """
    return await content_service.get_content_trace(request_id)

async def get_recent_traces_service(limit: int = 10, offset: int = 0, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Service function for retrieving recent content generation traces with pagination.

    Args:
        limit: Maximum number of traces to retrieve
        offset: Number of traces to skip for pagination
        user_id: Optional user ID to filter results

    Returns:
        List of recent trace summaries
    """
    return await content_service.get_recent_traces(limit=limit, offset=offset, user_id=user_id)

async def get_trace_service(trace_id: str) -> Dict[str, Any]:
    """
    Service function for retrieving a specific trace.

    Args:
        trace_id: The unique identifier for the trace

    Returns:
        The complete trace data
    """
    trace = await content_service.get_content_trace(trace_id)
    return trace

async def get_history_service(limit: int = 10, offset: int = 0, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Service function for retrieving content generation history with pagination.

    Args:
        limit: Maximum number of items to return
        offset: Number of items to skip for pagination
        user_id: Optional user ID to filter results

    Returns:
        List of content generation history items
    """
    return await content_service.get_recent_traces(limit=limit, offset=offset, user_id=user_id)

async def get_history_count_service(user_id: Optional[str] = None) -> int:
    """
    Service function for retrieving the total count of history items.

    Args:
        user_id: Optional user ID to filter results

    Returns:
        Total count of history items
    """
    return await content_service.get_traces_count(user_id=user_id)

async def get_prompt_optimization_history_service() -> List[Dict[str, Any]]:
    """
    Service function for retrieving prompt optimization history.

    Returns:
        List of prompt optimization history items
    """
    # This is a placeholder implementation
    # In a real implementation, this would retrieve prompt optimization history from a database
    return []

async def improve_prompt_service(request: Any) -> Dict[str, Any]:
    """
    Service function for improving a prompt using Gemini AI.

    Args:
        request: The prompt improvement request

    Returns:
        The improved prompt and metadata
    """
    import google.generativeai as genai
    import os
    import time

    # Get Gemini API key
    gemini_api_key = os.environ.get("GEMINI_API_KEY")
    if not gemini_api_key:
        raise HTTPException(
            status_code=503,
            detail="Gemini API key not configured"
        )

    # Configure Gemini
    genai.configure(api_key=gemini_api_key)
    model = genai.GenerativeModel('gemini-1.5-flash')

    start_time = time.time()

    try:
        context = getattr(request, 'context', '')

        improvement_prompt = f"""
        Improve the following prompt to make it more effective for content generation:

        Original prompt: "{request.prompt}"
        Context: {context}

        Make the prompt:
        - More specific and clear
        - Include relevant details for better results
        - Maintain the original intent
        - Optimize for social media content generation

        Return only the improved prompt, no additional explanations.
        """

        response = model.generate_content(improvement_prompt)
        improved_prompt = response.text.strip()

        execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds

        return {
            "improved_prompt": improved_prompt,
            "model_used": "gemini-1.5-flash",
            "latency_ms": execution_time,
            "reasoning": "Prompt improved using Gemini AI for clarity and specificity."
        }

    except Exception as e:
        logger.error(f"Error improving prompt: {str(e)}")
        # Return original prompt if improvement fails
        return {
            "improved_prompt": request.prompt,
            "model_used": "gemini-1.5-flash",
            "latency_ms": 0,
            "reasoning": f"Error improving prompt: {str(e)}"
        }
