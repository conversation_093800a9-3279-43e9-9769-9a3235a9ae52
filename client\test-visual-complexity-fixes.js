/**
 * Test script for Visual Complexity Analyzer critical fixes
 * Tests save button functionality, image display in history, and user notifications
 */

console.log('🧪 Testing Visual Complexity Analyzer Critical Fixes...');

// Test configuration
const TEST_CONFIG = {
  WAIT_TIME: 2000, // Wait time between tests
  MAX_RETRIES: 3,
  TIMEOUT: 10000
};

// Test results tracking
const testResults = {
  saveButtonFix: false,
  imageDisplayFix: false,
  userNotifications: false,
  overallSuccess: false
};

/**
 * Test 1: Save Button State Management Fix
 * Verify that the save button works correctly for first analysis
 */
async function testSaveButtonFix() {
  console.log('\n📋 Test 1: Save Button State Management');
  
  try {
    // Look for the Visual Complexity Analyzer component
    const analyzerComponent = document.querySelector('[data-testid="visual-complexity-analyzer"], .design-complexity-analyzer, [class*="complexity"]');
    
    if (!analyzerComponent) {
      console.log('❌ Visual Complexity Analyzer component not found');
      return false;
    }

    // Check if analyze tab is active
    const analyzeTab = document.querySelector('[value="analyze"]');
    if (analyzeTab) {
      analyzeTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Look for file input
    const fileInput = document.querySelector('input[type="file"]');
    if (!fileInput) {
      console.log('❌ File input not found');
      return false;
    }

    // Create a test image file
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 100, 100);
    
    // Convert canvas to blob and create file
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const testFile = new File([blob], 'test-design.png', { type: 'image/png' });

    // Simulate file selection
    Object.defineProperty(fileInput, 'files', {
      value: [testFile],
      writable: false,
    });
    
    // Trigger change event
    const changeEvent = new Event('change', { bubbles: true });
    fileInput.dispatchEvent(changeEvent);

    console.log('✅ Test file uploaded successfully');

    // Wait for file processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Look for analyze button and click it
    const analyzeButton = document.querySelector('button:contains("Analizar"), button[class*="analyze"], button:contains("Analizar Complejidad")');
    if (analyzeButton) {
      analyzeButton.click();
      console.log('✅ Analysis started');
      
      // Wait for analysis to complete (simulate)
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check if save button is available and enabled
      const saveButton = document.querySelector('button:contains("Guardar"), button[class*="save"]');
      if (saveButton && !saveButton.disabled) {
        console.log('✅ Save button is available and enabled for first analysis');
        testResults.saveButtonFix = true;
        return true;
      } else {
        console.log('❌ Save button not available or disabled for first analysis');
        return false;
      }
    } else {
      console.log('❌ Analyze button not found');
      return false;
    }

  } catch (error) {
    console.log('❌ Error in save button test:', error.message);
    return false;
  }
}

/**
 * Test 2: Image Display in History
 * Verify that saved analyses show correct images when loaded from history
 */
async function testImageDisplayFix() {
  console.log('\n📋 Test 2: Image Display in History');
  
  try {
    // Switch to history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Look for analysis cards
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], .card');
    
    if (analysisCards.length === 0) {
      console.log('⚠️ No analysis cards found in history (expected if no saved analyses)');
      testResults.imageDisplayFix = true; // Consider this a pass if no data
      return true;
    }

    // Check if analysis cards have image thumbnails
    let hasImageThumbnails = false;
    analysisCards.forEach((card, index) => {
      const thumbnail = card.querySelector('img, [class*="thumbnail"], [class*="image"]');
      if (thumbnail) {
        hasImageThumbnails = true;
        console.log(`✅ Analysis card ${index + 1} has image thumbnail`);
      }
    });

    if (hasImageThumbnails) {
      console.log('✅ Analysis cards display image thumbnails');
      testResults.imageDisplayFix = true;
      return true;
    } else {
      console.log('❌ No image thumbnails found in analysis cards');
      return false;
    }

  } catch (error) {
    console.log('❌ Error in image display test:', error.message);
    return false;
  }
}

/**
 * Test 3: User Notifications About History Limits
 * Verify that user notifications are displayed in history and favorites tabs
 */
async function testUserNotifications() {
  console.log('\n📋 Test 3: User Notifications About History Limits');
  
  try {
    let notificationsFound = 0;

    // Test history tab notification
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Look for history notification
      const historyNotification = document.querySelector('[class*="bg-blue-50"], [class*="border-blue-200"]');
      if (historyNotification && historyNotification.textContent.includes('últimos 10 análisis')) {
        console.log('✅ History tab notification found');
        notificationsFound++;
      }
    }

    // Test favorites tab notification
    const favoritesTab = document.querySelector('[value="favorites"]');
    if (favoritesTab) {
      favoritesTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Look for favorites notification
      const favoritesNotification = document.querySelector('[class*="bg-amber-50"], [class*="border-amber-200"]');
      if (favoritesNotification && favoritesNotification.textContent.includes('guardan permanentemente')) {
        console.log('✅ Favorites tab notification found');
        notificationsFound++;
      }
    }

    if (notificationsFound >= 2) {
      console.log('✅ All user notifications are displayed correctly');
      testResults.userNotifications = true;
      return true;
    } else {
      console.log(`❌ Only ${notificationsFound}/2 notifications found`);
      return false;
    }

  } catch (error) {
    console.log('❌ Error in user notifications test:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Visual Complexity Analyzer Fix Tests...\n');
  
  try {
    // Test 1: Save Button Fix
    const saveButtonResult = await testSaveButtonFix();
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.WAIT_TIME));
    
    // Test 2: Image Display Fix
    const imageDisplayResult = await testImageDisplayFix();
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.WAIT_TIME));
    
    // Test 3: User Notifications
    const notificationsResult = await testUserNotifications();
    
    // Calculate overall success
    testResults.overallSuccess = saveButtonResult && imageDisplayResult && notificationsResult;
    
    // Display final results
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Save Button Fix: ${testResults.saveButtonFix ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Image Display Fix: ${testResults.imageDisplayFix ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`User Notifications: ${testResults.userNotifications ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Overall Success: ${testResults.overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
    
    if (testResults.overallSuccess) {
      console.log('\n🎉 All critical fixes are working correctly!');
    } else {
      console.log('\n⚠️ Some fixes need attention. Check the individual test results above.');
    }
    
    return testResults;
    
  } catch (error) {
    console.log('❌ Critical error during testing:', error.message);
    return testResults;
  }
}

// Auto-run tests if this script is executed directly
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    setTimeout(runAllTests, 1000);
  }
}

// Export for manual testing
window.testVisualComplexityFixes = runAllTests;
window.testResults = testResults;

console.log('📝 Test script loaded. Run window.testVisualComplexityFixes() to start tests manually.');
