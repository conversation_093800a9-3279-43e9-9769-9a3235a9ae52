/**
 * Temporary simplified version of SketchToImagePage to fix compilation errors
 */

import React from 'react';
import DashboardLayout from '@/components/layout/dashboard-layout';
export default function SketchToImagePage() {
  return (
    <DashboardLayout pageTitle="Boceto a Imagen">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-20">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Boceto a Imagen
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Esta herramienta está temporalmente en mantenimiento.
            </p>
            <p className="text-gray-500">
              Estamos trabajando para mejorar la experiencia. Vuelve pronto.
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
