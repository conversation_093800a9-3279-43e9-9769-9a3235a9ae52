"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, CheckCircle2, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { useAuth } from "@/hooks/use-auth";

// Definición de tipos para los tweaks
interface TweakData {
  [key: string]: any;
}

interface LangFlowAstraPanelProps {
  title?: string;
  description?: string;
  agentId?: string;
  defaultLangflowId?: string; // Para compatibilidad con código anterior
  defaultFlowId?: string; // Para compatibilidad con código anterior
  defaultTweaks?: Record<string, TweakData>; // Para compatibilidad con código anterior
}

export function LangFlowAstraPanel({
  title = "LangFlow Astra Execution",
  description = "Execute a LangFlow agent via Astra DataStax",
  agentId = "",
  defaultLangflowId = "",
  defaultFlowId = "",
  defaultTweaks = {},
}: LangFlowAstraPanelProps) {
  // Mantenemos langflowId y flowId para compatibilidad con código antiguo
  const [langflowId, setLangflowId] = useState(defaultLangflowId);
  const [flowId, setFlowId] = useState(defaultFlowId);
  const [inputText, setInputText] = useState("");
  const [outputText, setOutputText] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [executionTime, setExecutionTime] = useState<number | null>(null);
  const [enableMemory, setEnableMemory] = useState(true);
  const [sessionId, setSessionId] = useState("");
  const [activeTab, setActiveTab] = useState("execution");

  const { toast } = useToast();
  const { user } = useAuth();

  // Generar un ID de sesión único para el usuario actual
  useEffect(() => {
    if (user) {
      // Para Firebase auth, usar uid, para el auth local, usar id
      const userId =
        typeof user === "object" && user && "uid" in user
          ? String(user.uid)
          : typeof user === "object" && user && "id" in user
            ? String(user.id)
            : null;

      if (userId) {
        setSessionId(userId); // Ahora userId es definitivamente un string
      } else {
        // Si no se puede obtener un ID de usuario válido, generar uno aleatorio
        const anonymousId = `anonymous-${Math.random().toString(36).substring(2, 9)}`;
        setSessionId(anonymousId);
      }
    } else {
      // Si no hay usuario, generar un ID aleatorio
      const anonymousId = `anonymous-${Math.random().toString(36).substring(2, 9)}`;
      setSessionId(anonymousId);
    }
  }, [user]);

  // Método para manejar respuestas exitosas
  const handleSuccessResponse = (data: any) => {
    setSuccess(true);

    console.log("Respuesta completa del servidor:", data);

    // Verificar si la respuesta sigue el formato de ejecución directa
    if (data.status === "success" && data.result) {
      // Nuevo formato de langflow_direct.py
      setOutputText(
        typeof data.result === "string"
          ? data.result
          : JSON.stringify(data.result, null, 2),
      );
    }
    // Soporte para respuestas antiguas
    else if (data.data) {
      // Caso 1: La respuesta está en data.data.output
      if (data.data.output) {
        setOutputText(
          typeof data.data.output === "string"
            ? data.data.output
            : JSON.stringify(data.data.output, null, 2),
        );
      }
      // Caso 2: La respuesta está en data.data.result
      else if (data.data.result) {
        setOutputText(
          typeof data.data.result === "string"
            ? data.data.result
            : JSON.stringify(data.data.result, null, 2),
        );
      }
      // Caso 3: La respuesta está en data.data directamente como un objeto
      else if (typeof data.data === "object") {
        if (data.data.response) {
          setOutputText(
            typeof data.data.response === "string"
              ? data.data.response
              : JSON.stringify(data.data.response, null, 2),
          );
        } else {
          setOutputText(JSON.stringify(data.data, null, 2));
        }
      }
      // Caso 4: La respuesta es un string
      else if (typeof data.data === "string") {
        setOutputText(data.data);
      } else {
        setOutputText(JSON.stringify(data.data, null, 2));
      }
    } else if (data.result) {
      setOutputText(
        typeof data.result === "string"
          ? data.result
          : JSON.stringify(data.result, null, 2),
      );
    } else if (data.output) {
      setOutputText(
        typeof data.output === "string"
          ? data.output
          : JSON.stringify(data.output, null, 2),
      );
    } else {
      setOutputText(JSON.stringify(data, null, 2));
    }

    // Extraer el tiempo de ejecución si está disponible
    if (data.execution_time) {
      setExecutionTime(data.execution_time);
    }

    // Cambiar a la pestaña de resultados
    setActiveTab("results");

    toast({
      title: "Agente ejecutado correctamente",
      description: `Tiempo de ejecución: ${data.execution_time ? data.execution_time.toFixed(2) + "s" : "N/A"}`,
      variant: "default",
    });
  };

  // Método para manejar errores
  const handleErrorResponse = (err: any) => {
    setSuccess(false);
    setError(err.message || "Error desconocido");

    // Cambiar a la pestaña de resultados también en caso de error
    setActiveTab("results");

    toast({
      title: "Error al ejecutar el agente",
      description: err.message || "Error desconocido",
      variant: "destructive",
    });
  };

  // Nuevo método que usa agentId para la ejecución
  const executeWithAgentId = async () => {
    setLoading(true);
    setSuccess(false);
    setError(null);
    setExecutionTime(null);

    // Cambiar a la pestaña de resultados inmediatamente para mostrar la animación de carga
    setActiveTab("results");

    try {
      // Obtener el ID de usuario para el payload
      const userId =
        user && typeof user === "object" && "uid" in user
          ? String(user.uid)
          : user && typeof user === "object" && "id" in user
            ? String(user.id)
            : "anonymous";

      // Usar 'message' como nombre del campo para adaptar a la nueva estructura
      const payload = {
        inputs: {
          message: inputText, // Cambiado de user_input a message para adaptarse a nuestra implementación
        },
        user_id: userId,
        session_id: enableMemory ? sessionId : undefined,
        tweaks: {}, // Los tweaks predeterminados están configurados en el backend
      };

      console.log(`Enviando solicitud a agente ${agentId}:`, payload);

      const response = await fetch(`/api/langflow/run/${agentId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.error_message || "Error desconocido al ejecutar el agente",
        );
      }

      handleSuccessResponse(data);
    } catch (err: any) {
      handleErrorResponse(err);
    } finally {
      setLoading(false);
    }
  };

  // Método original que usa IDs explícitos
  const executeWithDirectIds = async () => {
    setLoading(true);
    setSuccess(false);
    setError(null);
    setExecutionTime(null);

    // Cambiar a la pestaña de resultados inmediatamente para mostrar la animación de carga
    setActiveTab("results");

    try {
      // Preparar tweaks para la memoria de conversación si está habilitada
      const tweaks = { ...defaultTweaks };

      // Buscar componentes de chat en los tweaks y configurar session_id si enableMemory está activado
      if (enableMemory && sessionId) {
        // Iterar sobre los tweaks para encontrar componentes de chat
        Object.keys(tweaks).forEach((key) => {
          // Verificar si es un componente de chat por su nombre (puede variar según la configuración de LangFlow)
          if (
            key.includes("ChatInput") ||
            key.includes("HumanInput") ||
            key.includes("Chat")
          ) {
            const updatedTweak = {
              ...tweaks[key],
              session_id: sessionId,
            };
            tweaks[key] = updatedTweak;
          }
        });
      }

      // Obtener el ID de usuario para el payload
      const userId =
        user && typeof user === "object" && "uid" in user
          ? String(user.uid)
          : user && typeof user === "object" && "id" in user
            ? String(user.id)
            : "anonymous";

      const payload = {
        langflow_id: langflowId,
        flow_id: flowId,
        inputs: {
          user_input: inputText,
        },
        tweaks: Object.keys(tweaks).length > 0 ? tweaks : undefined,
        user_id: userId,
        session_id: enableMemory ? sessionId : undefined,
      };

      console.log("Enviando solicitud a LangFlow:", payload);

      const response = await fetch("/api/langflow/run-astra", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.error_message || "Error desconocido al ejecutar el flujo",
        );
      }

      handleSuccessResponse(data);
    } catch (err: any) {
      handleErrorResponse(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Si tenemos agentId, usamos el nuevo método de API
    // De lo contrario, usamos el método antiguo con IDs explícitos
    if (agentId) {
      // Método nuevo con agentId
      await executeWithAgentId();
    } else {
      // Método antiguo con IDs explícitos
      if (!langflowId.trim()) {
        toast({
          title: "LangFlow ID requerido",
          description: "Por favor ingresa el ID de LangFlow",
          variant: "destructive",
        });
        return;
      }

      if (!flowId.trim()) {
        toast({
          title: "Flow ID requerido",
          description: "Por favor ingresa el ID del flujo a ejecutar",
          variant: "destructive",
        });
        return;
      }

      await executeWithDirectIds();
    }
  };

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="execution">Ejecución</TabsTrigger>
            <TabsTrigger value="results">Resultados</TabsTrigger>
          </TabsList>

          <TabsContent value="execution">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Los IDs de LangFlow y Flow se manejan internamente en el backend por seguridad */}
              <input type="hidden" id="langflow-id" value={langflowId} />
              <input type="hidden" id="flow-id" value={flowId} />

              <div className="space-y-2">
                <Label htmlFor="input-text">Entrada</Label>
                <Textarea
                  id="input-text"
                  placeholder="Ingresa tu consulta o texto aquí..."
                  className="min-h-[100px]"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="memory-switch"
                  checked={enableMemory}
                  onCheckedChange={setEnableMemory}
                />
                <Label htmlFor="memory-switch" className="text-sm">
                  Habilitar memoria de conversación (session ID:{" "}
                  {sessionId.substring(0, 8)}...)
                </Label>
              </div>

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Ejecutando...
                  </>
                ) : (
                  "Ejecutar flujo"
                )}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="results">
            <div className="space-y-4">
              {loading && (
                <div className="flex flex-col items-center justify-center p-8 space-y-4">
                  <div className="relative">
                    <Loader2 className="h-12 w-12 animate-spin text-primary" />
                    <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                      <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-medium text-primary">
                      Procesando solicitud...
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      El modelo de IA está generando una respuesta
                    </p>
                  </div>
                </div>
              )}

              {!loading && (success || error) && (
                <div className="space-y-4">
                  <div
                    className={`p-3 rounded-md ${success ? "bg-green-50" : "bg-red-50"} flex items-start`}
                  >
                    {success ? (
                      <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    )}
                    <div>
                      <p
                        className={`font-medium ${success ? "text-green-700" : "text-red-700"}`}
                      >
                        {success ? "Ejecución exitosa" : "Error de ejecución"}
                      </p>
                      {executionTime && success && (
                        <p className="text-sm text-green-600">
                          Tiempo de ejecución: {executionTime.toFixed(2)}{" "}
                          segundos
                        </p>
                      )}
                      {error && <p className="text-sm text-red-600">{error}</p>}
                    </div>
                  </div>

                  {success && (
                    <div className="space-y-2">
                      <Label htmlFor="output-text">Resultado</Label>
                      <Textarea
                        id="output-text"
                        readOnly
                        className="min-h-[200px] font-mono text-sm"
                        value={outputText}
                      />
                    </div>
                  )}
                </div>
              )}

              {!loading && !success && !error && (
                <div className="text-center p-6 text-gray-500">
                  <p>Ejecuta un flujo para ver los resultados aquí</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between text-xs text-gray-500">
        <div>LangFlow API vía Astra DataStax</div>
      </CardFooter>
    </Card>
  );
}

export default LangFlowAstraPanel;
