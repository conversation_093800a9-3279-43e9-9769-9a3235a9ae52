"""
Service for creating advertisements using Ideogram.ai 3.0 Quality exclusively.
Optimized for professional text-heavy marketing materials at low cost.
"""

import logging
import base64
from typing import List, Dict, Any
from fastapi import UploadFile

logger = logging.getLogger(__name__)


class AdService:
    """Service for creating and editing advertisements using Ideogram.ai 3.0 Quality exclusively."""
    
    def __init__(self):
        # Import ideogram service
        from app.services.ideogram_service import ideogram_service
        self.ideogram_service = ideogram_service
        
    async def generate_ad(self, prompt: str, size: str = "auto") -> Dict[str, Any]:
        """
        Generate an advertisement using Ideogram.ai 3.0 Quality exclusively.
        
        Args:
            prompt: Description of the advertisement to create
            size: Image size (1024x1024, 1792x1024, 1024x1792)
            
        Returns:
            Dict with success status, image data, and metadata
        """
        logger.info("🎨 Generating ad with Ideogram.ai 3.0 Quality...")
        return await self.ideogram_service.generate_ad(prompt, size)

    async def generate_multiple_ads(self, prompt: str, num_images: int = 6, size: str = "auto") -> Dict[str, Any]:
        """
        Generate multiple advertisements in a single API call using Ideogram.ai 3.0 Quality.

        Args:
            prompt: Description of the advertisement to create
            num_images: Number of images to generate (1-8, default 6)
            size: Image size (1024x1024, 1792x1024, 1024x1792)

        Returns:
            Dict with success status, images list, and metadata
        """
        logger.info(f"🎨 Generating {num_images} ads with Ideogram.ai 3.0 Quality...")
        return await self.ideogram_service.generate_multiple_ads(prompt, num_images, size)

    def encode_image(self, file_content: bytes) -> str:
        """
        Encode image content to base64.

        Args:
            file_content: The image content as bytes

        Returns:
            Base64 encoded string
        """
        return base64.b64encode(file_content).decode("utf-8")

    async def edit_with_references(self, prompt: str, reference_images: List[UploadFile], size: str = "auto") -> Dict[str, Any]:
        """
        Generate advertisement using reference images with Ideogram.ai 3.0 Quality exclusively.

        Args:
            prompt: Description of the advertisement to create
            reference_images: List of reference images to use
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if len(reference_images) == 0:
            logger.error("No reference images provided")
            return {"success": False, "error": "No reference images provided"}

        if len(reference_images) > 1:
            logger.warning("Multiple reference images provided, using only the first one")

        logger.info("🖼️ Generating with reference using Ideogram.ai 3.0 Quality...")
        return await self.ideogram_service.generate_with_reference(
            prompt, reference_images[0], size
        )


# Global service instance
ad_service = AdService()
