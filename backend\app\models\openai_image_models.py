"""
Data models for OpenAI image generation API.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Any, Dict
from datetime import datetime


class OpenAIImageGenerationRequest(BaseModel):
    """Request model for OpenAI image generation."""
    prompt: str = Field(..., description="Description of the image to generate")
    size: str = Field(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)")


class OpenAIImageReferenceRequest(BaseModel):
    """Request model for OpenAI image generation with references."""
    prompt: str = Field(..., description="Description of the image to generate")
    size: str = Field(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)")
    # reference_images will be handled as UploadFile in the endpoint


class OpenAIImageMaskRequest(BaseModel):
    """Request model for OpenAI image mask editing."""
    prompt: str = Field(..., description="Description of the changes to make")
    # image and mask will be handled as UploadFile in the endpoint


class OpenAIImageMultiTurnRequest(BaseModel):
    """Request model for multi-turn OpenAI image editing."""
    previous_response_id: str = Field(..., description="ID of the previous response to build upon")
    edit_prompt: str = Field(..., description="Description of the changes to make")


class ServiceOpenAIImageResponse(BaseModel):
    """Internal service response model for OpenAI image operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    image_url: Optional[str] = Field(None, description="URL of the generated image")
    images: Optional[List[str]] = Field(None, description="List of generated image URLs")
    revised_prompt: Optional[str] = Field(None, description="Revised prompt used by OpenAI")
    response_id: Optional[str] = Field(None, description="Unique response ID for multi-turn editing")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    
    # Timing and performance metrics
    generation_time: Optional[float] = Field(None, description="Time taken to generate the image")
    model_used: Optional[str] = Field(None, description="Model used for generation")
    
    # Request tracking
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    timestamp: Optional[datetime] = Field(None, description="Timestamp of the response")


class FrontendOpenAIImageResponse(BaseModel):
    """Frontend response model for OpenAI image operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    image_url: Optional[str] = Field(None, description="URL of the generated image")
    images: Optional[List[str]] = Field(None, description="List of generated image URLs")
    revised_prompt: Optional[str] = Field(None, description="Revised prompt used by OpenAI")
    response_id: Optional[str] = Field(None, description="Unique response ID for multi-turn editing")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    
    @classmethod
    def from_service_response(cls, service_response: ServiceOpenAIImageResponse) -> "FrontendOpenAIImageResponse":
        """Convert service response to frontend response."""
        return cls(
            success=service_response.success,
            image_url=service_response.image_url,
            images=service_response.images,
            revised_prompt=service_response.revised_prompt,
            response_id=service_response.response_id,
            metadata=service_response.metadata,
            error=service_response.error
        )


class OpenAIImageGenerationOptions(BaseModel):
    """Options for OpenAI image generation."""
    prompt: str = Field(..., description="Description of the image to generate")
    size: str = Field(default="1024x1024", description="Image size")
    quality: str = Field(default="standard", description="Image quality (standard, hd)")
    style: str = Field(default="vivid", description="Image style (vivid, natural)")
    
    # Advanced options
    response_format: str = Field(default="url", description="Response format (url, b64_json)")
    user: Optional[str] = Field(None, description="User identifier for tracking")


class OpenAIImageEditOptions(BaseModel):
    """Options for OpenAI image editing."""
    prompt: str = Field(..., description="Description of the desired changes")
    size: str = Field(default="1024x1024", description="Image size")
    n: int = Field(default=1, description="Number of images to generate")
    response_format: str = Field(default="url", description="Response format (url, b64_json)")
    user: Optional[str] = Field(None, description="User identifier for tracking")


class OpenAIImageVariationOptions(BaseModel):
    """Options for OpenAI image variations."""
    size: str = Field(default="1024x1024", description="Image size")
    n: int = Field(default=1, description="Number of variations to generate")
    response_format: str = Field(default="url", description="Response format (url, b64_json)")
    user: Optional[str] = Field(None, description="User identifier for tracking")


class OpenAIImageMetadata(BaseModel):
    """Metadata for OpenAI image operations."""
    model: str = Field(default="dall-e-3", description="Model used for generation")
    size: str = Field(..., description="Image size")
    quality: str = Field(default="standard", description="Image quality")
    style: str = Field(default="vivid", description="Image style")
    generation_time: Optional[float] = Field(None, description="Time taken to generate")
    prompt_tokens: Optional[int] = Field(None, description="Number of prompt tokens used")
    
    # Request tracking
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    
    # Content safety
    content_filter_results: Optional[Dict[str, Any]] = Field(None, description="Content filter results")
    safety_ratings: Optional[Dict[str, Any]] = Field(None, description="Safety ratings")


class OpenAIImageError(BaseModel):
    """Error model for OpenAI image operations."""
    error_type: str = Field(..., description="Type of error")
    error_code: Optional[str] = Field(None, description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request ID that caused the error")


class OpenAIImageStats(BaseModel):
    """Statistics for OpenAI image operations."""
    total_requests: int = Field(default=0, description="Total number of requests")
    successful_requests: int = Field(default=0, description="Number of successful requests")
    failed_requests: int = Field(default=0, description="Number of failed requests")
    average_generation_time: Optional[float] = Field(None, description="Average generation time")
    total_images_generated: int = Field(default=0, description="Total images generated")
    
    # Usage metrics
    total_prompt_tokens: int = Field(default=0, description="Total prompt tokens used")
    total_cost: Optional[float] = Field(None, description="Total cost in USD")
    
    # Time period
    period_start: Optional[datetime] = Field(None, description="Start of the statistics period")
    period_end: Optional[datetime] = Field(None, description="End of the statistics period")
