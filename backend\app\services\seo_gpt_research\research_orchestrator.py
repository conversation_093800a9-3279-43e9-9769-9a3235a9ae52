"""
Research Orchestrator Module
Main orchestrator for SEO GPT Research Service
"""

import logging
import time
import asyncio
from typing import Dict, Any, List

from .intent_analyzer import IntentAnalyzer
from .google_scraper import GoogleScraper
from .social_insights_extractor import SocialInsightsExtractor
from .gpt_reference_generator import GPTReferenceGenerator
from .entity_extractor import EntityExtractor
from .content_opportunities_analyzer import ContentOpportunitiesAnalyzer

logger = logging.getLogger(__name__)

class SEOGPTResearchService:
    """
    Main Research Engine for SEO & GPT Optimizer™
    
    Orchestrates comprehensive research including:
    - Search intent analysis
    - Google Top 10 + Reddit + Quora scraping
    - GPT reference responses
    - Entity extraction and common questions
    - Content opportunities analysis
    """
    
    def __init__(self):
        # Initialize all components
        self.intent_analyzer = IntentAnalyzer()
        self.google_scraper = GoogleScraper()
        self.social_insights_extractor = SocialInsightsExtractor()
        self.gpt_reference_generator = GPTReferenceGenerator()
        self.entity_extractor = EntityExtractor()
        self.content_opportunities_analyzer = ContentOpportunitiesAnalyzer()
        
        logger.info("✅ SEO GPT Research Service initialized successfully")
    
    async def conduct_comprehensive_research(
        self,
        topic: str,
        target_language: str = "es",
        include_reddit: bool = True,
        include_quora: bool = True,
        target_country: str = "ES",
        include_news: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Conduct comprehensive research for a given topic.
        
        Args:
            topic: The main topic/keyword to research
            target_language: Target language for analysis (default: Spanish)
            include_reddit: Whether to include Reddit results
            include_quora: Whether to include Quora results
            
        Returns:
            Complete research analysis including intent, competitors, entities, etc.
        """
        try:
            start_time = time.time()
            logger.info(f"🔍 Starting comprehensive research for topic: '{topic}'")

            # Use timeout for the entire research process
            return await asyncio.wait_for(
                self._conduct_research_steps(topic, target_language, include_reddit, include_quora, target_country, include_news, start_time),
                timeout=120.0  # 2 minutes timeout
            )

        except asyncio.TimeoutError:
            logger.error(f"❌ Research timed out for topic '{topic}' after 2 minutes")
            return {
                "status": "timeout",
                "topic": topic,
                "error_message": "Research timed out after 2 minutes. Please try again with a simpler topic.",
                "timestamp": time.time()
            }
            
            # Step 7: Generate comprehensive research summary
            logger.info("📋 Generating research summary...")
            research_summary = await self._generate_research_summary(
                topic, intent_analysis, entities_and_questions, content_opportunities
            )
            
            processing_time = time.time() - start_time
            
            research_results = {
                "status": "success",
                "topic": topic,
                "target_language": target_language,
                "processing_time": processing_time,
                "timestamp": time.time(),
                "intent_analysis": intent_analysis,
                "google_results": google_results,
                "social_insights": social_insights,
                "gpt_reference": gpt_reference,
                "entities_and_questions": entities_and_questions,
                "content_opportunities": content_opportunities,
                "research_summary": research_summary,
                "research_quality_metrics": self._calculate_research_quality_metrics(
                    intent_analysis, google_results, social_insights, gpt_reference, 
                    entities_and_questions, content_opportunities
                )
            }
            
            logger.info(f"✅ Research completed for '{topic}' in {processing_time:.2f}s")
            return research_results
            
        except Exception as e:
            logger.error(f"❌ Research failed for topic '{topic}': {str(e)}")
            return {
                "status": "error",
                "topic": topic,
                "error_message": f"Research failed: {str(e)}",
                "timestamp": time.time()
            }

    async def _conduct_research_steps(
        self,
        topic: str,
        target_language: str,
        include_reddit: bool,
        include_quora: bool,
        target_country: str,
        include_news: bool,
        start_time: float
    ) -> Dict[str, Any]:
        """Conduct the actual research steps with individual timeouts."""

        # Step 1: Analyze search intent (timeout: 30s)
        logger.info("📊 Analyzing search intent...")
        try:
            intent_analysis = await asyncio.wait_for(
                self.intent_analyzer.analyze_search_intent(topic, target_language),
                timeout=30.0
            )
        except asyncio.TimeoutError:
            logger.warning("⚠️ Intent analysis timed out, using fallback")
            intent_analysis = {"status": "timeout", "intent_type": "informational"}

        # Step 2: Get Google Top 10 results (timeout: 45s)
        logger.info("🔍 Scraping Google Top 10 results...")
        try:
            google_results = await asyncio.wait_for(
                self.google_scraper.get_google_top_results(topic, 10, target_country, target_language),
                timeout=45.0
            )
        except asyncio.TimeoutError:
            logger.warning("⚠️ Google search timed out, using fallback")
            google_results = {"status": "timeout", "results": []}

        # Step 3: Get social media insights (timeout: 30s each)
        logger.info("📱 Extracting social media insights...")
        social_insights = {}
        if include_reddit:
            try:
                social_insights['reddit'] = await asyncio.wait_for(
                    self.social_insights_extractor.get_reddit_insights(topic, target_country, target_language),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                logger.warning("⚠️ Reddit insights timed out")
                social_insights['reddit'] = {"status": "timeout", "insights": []}

        if include_quora:
            try:
                social_insights['quora'] = await asyncio.wait_for(
                    self.social_insights_extractor.get_quora_insights(topic, target_country, target_language),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                logger.warning("⚠️ Quora insights timed out")
                social_insights['quora'] = {"status": "timeout", "insights": []}

        # Step 3.5: Get news insights if requested (timeout: 30s)
        if include_news:
            try:
                social_insights['news'] = await asyncio.wait_for(
                    self.social_insights_extractor.get_news_insights(topic, target_country, target_language),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                logger.warning("⚠️ News insights timed out")
                social_insights['news'] = {"status": "timeout", "articles": []}

        # Step 4: Get GPT reference response (timeout: 30s)
        logger.info("🤖 Generating GPT reference response...")
        try:
            gpt_reference = await asyncio.wait_for(
                self.gpt_reference_generator.get_gpt_reference_response(topic, target_language),
                timeout=30.0
            )
        except asyncio.TimeoutError:
            logger.warning("⚠️ GPT reference generation timed out")
            gpt_reference = {"status": "timeout", "response": ""}

        # Step 5: Extract entities and common questions (timeout: 20s)
        logger.info("🏷️ Extracting entities and questions...")
        try:
            entities_and_questions = await asyncio.wait_for(
                self.entity_extractor.extract_entities_and_questions(
                    topic, google_results, social_insights, gpt_reference, target_language
                ),
                timeout=20.0
            )
        except asyncio.TimeoutError:
            logger.warning("⚠️ Entity extraction timed out")
            entities_and_questions = {"status": "timeout", "entities": {}, "common_questions": []}

        # Step 6: Analyze content gaps and opportunities (timeout: 20s)
        logger.info("💡 Analyzing content opportunities...")
        try:
            content_opportunities = await asyncio.wait_for(
                self.content_opportunities_analyzer.analyze_content_opportunities(
                    topic, google_results, gpt_reference, target_language
                ),
                timeout=20.0
            )
        except asyncio.TimeoutError:
            logger.warning("⚠️ Content opportunities analysis timed out")
            content_opportunities = {"status": "timeout", "content_gaps": [], "opportunity_score": 50.0}

        # Step 7: Generate comprehensive research summary
        logger.info("📋 Generating research summary...")
        research_summary = await self._generate_research_summary(
            topic, intent_analysis, entities_and_questions, content_opportunities
        )

        processing_time = time.time() - start_time

        research_results = {
            "status": "success",
            "topic": topic,
            "target_language": target_language,
            "processing_time": processing_time,
            "timestamp": time.time(),
            "intent_analysis": intent_analysis,
            "google_results": google_results,
            "social_insights": social_insights,
            "gpt_reference": gpt_reference,
            "entities_and_questions": entities_and_questions,
            "content_opportunities": content_opportunities,
            "research_summary": research_summary,
            "research_quality_metrics": self._calculate_research_quality_metrics(
                intent_analysis, google_results, social_insights, gpt_reference,
                entities_and_questions, content_opportunities
            )
        }

        logger.info(f"✅ Research completed for '{topic}' in {processing_time:.2f}s")
        return research_results
    
    async def _generate_research_summary(
        self, 
        topic: str, 
        intent_analysis: Dict[str, Any], 
        entities_and_questions: Dict[str, Any], 
        content_opportunities: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a comprehensive research summary."""
        try:
            # Calculate research confidence
            research_confidence = self._calculate_research_confidence(
                intent_analysis, entities_and_questions, content_opportunities
            )
            
            # Extract key insights
            key_insights = []
            
            # Intent insights
            intent_type = intent_analysis.get('intent_type', 'unknown')
            key_insights.append(f"Intención principal: {intent_type}")
            
            # Audience insights
            target_audience = intent_analysis.get('target_audience', 'general')
            key_insights.append(f"Audiencia objetivo: {target_audience}")
            
            # Competition insights
            competition_level = content_opportunities.get('estimated_competition_level', 'unknown')
            key_insights.append(f"Nivel de competencia: {competition_level}")
            
            # Content opportunities insights
            content_gaps = content_opportunities.get('content_gaps', [])
            if content_gaps:
                key_insights.append(f"Principales gaps de contenido: {len(content_gaps)} identificados")
            
            # Entity insights
            total_entities = sum(
                len(entities) for entities in entities_and_questions.get('entities', {}).values()
            )
            if total_entities > 0:
                key_insights.append(f"Entidades extraídas: {total_entities}")
            
            # Generate content recommendations
            content_recommendations = self._generate_content_recommendations(
                intent_analysis, content_opportunities
            )
            
            return {
                "topic": topic,
                "research_confidence": research_confidence,
                "key_insights": key_insights,
                "recommended_approach": content_recommendations,
                "priority_questions": entities_and_questions.get('common_questions', [])[:5],
                "priority_keywords": entities_and_questions.get('semantic_keywords', [])[:10],
                "opportunity_score": content_opportunities.get('opportunity_score', 50.0),
                "next_steps": self._generate_next_steps(intent_analysis, content_opportunities)
            }
        except Exception as e:
            logger.error(f"❌ Research summary generation failed: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def _generate_content_recommendations(
        self, 
        intent_analysis: Dict[str, Any], 
        content_opportunities: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate content recommendations based on research."""
        return {
            "content_type": intent_analysis.get('content_type_preference', 'artículo'),
            "tone": intent_analysis.get('preferred_tone', 'conversacional'),
            "length": intent_analysis.get('optimal_content_length', 'medio'),
            "depth": content_opportunities.get('content_depth_recommendation', 'medio'),
            "format_suggestions": content_opportunities.get('content_format_suggestions', ['artículo'])[:3],
            "differentiation_strategy": content_opportunities.get('differentiation_opportunities', [])[:3]
        }
    
    def _generate_next_steps(
        self, 
        intent_analysis: Dict[str, Any], 
        content_opportunities: Dict[str, Any]
    ) -> List[str]:
        """Generate actionable next steps based on research."""
        next_steps = []
        
        # Content creation steps
        content_type = intent_analysis.get('content_type_preference', 'artículo')
        next_steps.append(f"Crear {content_type} optimizado para la intención de búsqueda")
        
        # Gap filling steps
        content_gaps = content_opportunities.get('content_gaps', [])
        if content_gaps:
            next_steps.append(f"Abordar gap principal: {content_gaps[0]}")
        
        # Differentiation steps
        diff_opportunities = content_opportunities.get('differentiation_opportunities', [])
        if diff_opportunities:
            next_steps.append(f"Diferenciarse mediante: {diff_opportunities[0]}")
        
        # SEO optimization steps
        target_keywords = content_opportunities.get('target_keywords', [])
        if target_keywords:
            next_steps.append(f"Optimizar para palabras clave: {', '.join(target_keywords[:3])}")
        
        # Content format steps
        format_suggestions = content_opportunities.get('content_format_suggestions', [])
        if format_suggestions:
            next_steps.append(f"Considerar formato: {format_suggestions[0]}")
        
        return next_steps[:5]  # Return top 5 next steps
    
    def _calculate_research_confidence(
        self, 
        intent_analysis: Dict[str, Any], 
        entities_and_questions: Dict[str, Any], 
        content_opportunities: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for the research results."""
        try:
            confidence_factors = []
            
            # Intent analysis confidence
            if intent_analysis.get('status') != 'error':
                confidence_factors.append(intent_analysis.get('intent_confidence', 0.5))
            
            # Entity extraction confidence
            if entities_and_questions.get('status') != 'error':
                extraction_confidence = entities_and_questions.get('extraction_confidence', 0.5)
                confidence_factors.append(extraction_confidence)
            
            # Content opportunities confidence
            if content_opportunities.get('status') != 'error':
                opportunity_score = content_opportunities.get('opportunity_score', 50.0)
                confidence_factors.append(opportunity_score / 100.0)
            
            # Calculate average confidence
            if confidence_factors:
                return sum(confidence_factors) / len(confidence_factors)
            else:
                return 0.3  # Low confidence fallback
                
        except:
            return 0.3
    
    def _calculate_research_quality_metrics(
        self,
        intent_analysis: Dict[str, Any],
        google_results: Dict[str, Any],
        social_insights: Dict[str, Any],
        gpt_reference: Dict[str, Any],
        entities_and_questions: Dict[str, Any],
        content_opportunities: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate quality metrics for the research."""
        try:
            metrics = {
                "data_sources_analyzed": 0,
                "total_search_results": 0,
                "social_insights_count": 0,
                "entities_extracted": 0,
                "questions_identified": 0,
                "content_gaps_found": 0,
                "overall_quality_score": 0.0
            }
            
            # Count data sources
            if google_results.get('status') == 'success':
                metrics["data_sources_analyzed"] += 1
                metrics["total_search_results"] = len(google_results.get('results', []))
            
            if gpt_reference.get('status') == 'success':
                metrics["data_sources_analyzed"] += 1
            
            # Count social insights
            for platform, insights in social_insights.items():
                if insights.get('status') == 'success':
                    metrics["data_sources_analyzed"] += 1
                    metrics["social_insights_count"] += len(insights.get('insights', []))
            
            # Count extracted data
            if entities_and_questions.get('status') == 'success':
                entities = entities_and_questions.get('entities', {})
                metrics["entities_extracted"] = sum(len(entity_list) for entity_list in entities.values())
                metrics["questions_identified"] = len(entities_and_questions.get('common_questions', []))
            
            # Count content opportunities
            if content_opportunities.get('status') == 'success':
                metrics["content_gaps_found"] = len(content_opportunities.get('content_gaps', []))
            
            # Calculate overall quality score
            quality_factors = [
                min(metrics["data_sources_analyzed"] / 4, 1.0),  # Max 4 sources
                min(metrics["total_search_results"] / 10, 1.0),  # Max 10 results
                min(metrics["entities_extracted"] / 20, 1.0),    # Max 20 entities
                min(metrics["questions_identified"] / 10, 1.0),  # Max 10 questions
                min(metrics["content_gaps_found"] / 5, 1.0)      # Max 5 gaps
            ]
            
            metrics["overall_quality_score"] = sum(quality_factors) / len(quality_factors)
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Quality metrics calculation failed: {str(e)}")
            return {"overall_quality_score": 0.0, "error": str(e)}
