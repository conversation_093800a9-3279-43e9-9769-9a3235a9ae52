/**
 * Real-Time Upload Diagnostic for Visual Complexity Analyzer
 * This script provides step-by-step debugging of the upload process
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🔬 Real-Time Upload Diagnostic Starting...');

class RealTimeUploadDiagnostic {
  constructor() {
    this.results = {
      auth: null,
      environment: null,
      uploadSteps: [],
      errors: [],
      success: false
    };
    this.supabase = window.supabase;
    this.service = window.designAnalysisService;
  }

  log(message, type = 'info', step = null) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    const logMessage = `[${timestamp}] ${emoji} ${message}`;
    console.log(logMessage);
    
    if (step) {
      this.results.uploadSteps.push({
        step,
        message,
        type,
        timestamp: new Date().toISOString()
      });
    }
  }

  async checkEnvironment() {
    this.log('🌍 Checking environment...', 'info', 'environment');
    
    const env = {
      supabaseAvailable: !!window.supabase,
      serviceAvailable: !!window.designAnalysisService,
      reactAvailable: !!window.React,
      location: window.location.href,
      userAgent: navigator.userAgent.substring(0, 100)
    };

    this.results.environment = env;
    
    this.log(`Supabase: ${env.supabaseAvailable ? '✅' : '❌'}`, env.supabaseAvailable ? 'success' : 'error');
    this.log(`Service: ${env.serviceAvailable ? '✅' : '❌'}`, env.serviceAvailable ? 'success' : 'error');
    this.log(`Location: ${env.location}`);
    
    return env.supabaseAvailable && env.serviceAvailable;
  }

  async checkAuthentication() {
    this.log('🔐 Checking authentication...', 'info', 'auth');
    
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();
      
      if (error) {
        this.log(`Auth error: ${error.message}`, 'error', 'auth');
        this.results.errors.push(`Auth error: ${error.message}`);
        return false;
      }
      
      if (!user) {
        this.log('No user found', 'error', 'auth');
        this.results.errors.push('No authenticated user');
        return false;
      }
      
      this.results.auth = {
        userId: user.id,
        email: user.email,
        role: user.role || 'authenticated',
        tokenExpiry: new Date(user.exp * 1000).toISOString()
      };
      
      this.log(`User: ${user.email}`, 'success', 'auth');
      this.log(`ID: ${user.id}`, 'info', 'auth');
      this.log(`Token expires: ${new Date(user.exp * 1000).toLocaleString()}`, 'info', 'auth');
      
      return true;
    } catch (error) {
      this.log(`Auth check failed: ${error.message}`, 'error', 'auth');
      this.results.errors.push(`Auth check failed: ${error.message}`);
      return false;
    }
  }

  async testStorageConnection() {
    this.log('🪣 Testing storage connection...', 'info', 'storage');
    
    try {
      // Test bucket access
      const { data: buckets, error: bucketError } = await this.supabase.storage.listBuckets();
      
      if (bucketError) {
        this.log(`Bucket list error: ${bucketError.message}`, 'error', 'storage');
        return false;
      }
      
      const designBucket = buckets.find(b => b.name === 'design-analysis-images');
      if (!designBucket) {
        this.log('design-analysis-images bucket not found', 'error', 'storage');
        return false;
      }
      
      this.log(`Bucket found: ${designBucket.name} (${designBucket.public ? 'public' : 'private'})`, 'success', 'storage');
      
      // Test user folder access
      const { data: files, error: listError } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, { limit: 1 });
      
      if (listError) {
        this.log(`User folder access error: ${listError.message}`, 'error', 'storage');
        this.results.errors.push(`Storage access error: ${listError.message}`);
        return false;
      }
      
      this.log(`User folder accessible: ${files.length} files found`, 'success', 'storage');
      return true;
      
    } catch (error) {
      this.log(`Storage connection failed: ${error.message}`, 'error', 'storage');
      this.results.errors.push(`Storage connection failed: ${error.message}`);
      return false;
    }
  }

  async testDirectUpload() {
    this.log('📤 Testing direct Supabase upload...', 'info', 'direct_upload');
    
    try {
      // Create minimal test file
      const testContent = 'test-upload-' + Date.now();
      const testBlob = new Blob([testContent], { type: 'text/plain' });
      const testFile = new File([testBlob], 'diagnostic-test.txt', { type: 'text/plain' });
      
      const fileName = `${this.results.auth.userId}/diagnostic-${Date.now()}.txt`;
      this.log(`Uploading to: ${fileName}`, 'info', 'direct_upload');
      
      const { data, error } = await this.supabase.storage
        .from('design-analysis-images')
        .upload(fileName, testFile, {
          cacheControl: '3600',
          upsert: false
        });
      
      if (error) {
        this.log(`Direct upload failed: ${error.message}`, 'error', 'direct_upload');
        this.log(`Error code: ${error.code || 'N/A'}`, 'error', 'direct_upload');
        this.log(`Error status: ${error.status || 'N/A'}`, 'error', 'direct_upload');
        
        if (error.message.includes('row-level security policy')) {
          this.log('🚨 RLS POLICY VIOLATION DETECTED', 'error', 'direct_upload');
          this.results.errors.push('RLS Policy Violation in direct upload');
        }
        
        this.results.errors.push(`Direct upload error: ${error.message}`);
        return false;
      }
      
      this.log(`Direct upload successful: ${data.path}`, 'success', 'direct_upload');
      
      // Clean up test file
      await this.supabase.storage
        .from('design-analysis-images')
        .remove([fileName]);
      
      this.log('Test file cleaned up', 'info', 'direct_upload');
      return true;
      
    } catch (error) {
      this.log(`Direct upload exception: ${error.message}`, 'error', 'direct_upload');
      this.results.errors.push(`Direct upload exception: ${error.message}`);
      return false;
    }
  }

  async testServiceUpload() {
    this.log('🔧 Testing service uploadImage method...', 'info', 'service_upload');
    
    try {
      // Create test image
      const canvas = document.createElement('canvas');
      canvas.width = 10;
      canvas.height = 10;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 10, 10);
      
      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
      const imageFile = new File([blob], 'service-test.png', { type: 'image/png' });
      
      this.log(`Created test image: ${imageFile.name} (${imageFile.size} bytes)`, 'info', 'service_upload');
      
      // Test service method with detailed logging
      this.log('Calling designAnalysisService.uploadImage...', 'info', 'service_upload');
      
      const result = await this.service.uploadImage(imageFile, this.results.auth.userId);
      
      if (!result) {
        this.log('Service returned null/undefined', 'error', 'service_upload');
        this.results.errors.push('Service upload returned null');
        return false;
      }
      
      this.log(`Service upload successful: ${result}`, 'success', 'service_upload');
      
      // Verify file exists
      const { data: downloadData, error: downloadError } = await this.supabase.storage
        .from('design-analysis-images')
        .download(result);
      
      if (downloadError) {
        this.log(`File verification failed: ${downloadError.message}`, 'error', 'service_upload');
        this.results.errors.push('Uploaded file not accessible');
        return false;
      }
      
      this.log(`File verified: ${downloadData.size} bytes`, 'success', 'service_upload');
      
      // Clean up
      await this.supabase.storage
        .from('design-analysis-images')
        .remove([result]);
      
      return true;
      
    } catch (error) {
      this.log(`Service upload failed: ${error.message}`, 'error', 'service_upload');
      this.log(`Error type: ${error.constructor.name}`, 'error', 'service_upload');
      this.log(`Stack trace: ${error.stack?.substring(0, 200)}...`, 'error', 'service_upload');
      
      this.results.errors.push(`Service upload error: ${error.message}`);
      return false;
    }
  }

  async testCompleteFlow() {
    this.log('🔄 Testing complete saveAnalysis flow...', 'info', 'complete_flow');
    
    try {
      // Create test image
      const canvas = document.createElement('canvas');
      canvas.width = 20;
      canvas.height = 20;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#00FF00';
      ctx.fillRect(0, 0, 20, 20);
      
      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
      const imageFile = new File([blob], 'complete-flow-test.png', { type: 'image/png' });
      
      const testData = {
        user_id: this.results.auth.userId,
        tool_type: 'visual-complexity',
        original_filename: imageFile.name,
        file_size: imageFile.size,
        file_type: imageFile.type,
        overall_score: 85,
        complexity_scores: { color: 8, layout: 8, typography: 8, elements: 9 },
        analysis_areas: [{ name: 'Test', score: 8, description: 'Diagnostic test' }],
        recommendations: [{ category: 'Test', issue: 'Test', importance: 'alta', recommendation: 'Test' }],
        ai_analysis_summary: 'Diagnostic test analysis',
        agent_message: 'Testing complete flow',
        visuai_insights: 'Test insights',
        tags: ['diagnostic-test']
      };
      
      this.log('Calling saveAnalysis with image...', 'info', 'complete_flow');
      
      const savedAnalysis = await this.service.saveAnalysis(testData, imageFile);
      
      this.log(`Analysis saved: ${savedAnalysis.id}`, 'success', 'complete_flow');
      this.log(`File URL: ${savedAnalysis.file_url || 'NULL'}`, savedAnalysis.file_url ? 'success' : 'error', 'complete_flow');
      
      if (!savedAnalysis.file_url) {
        this.log('🚨 CRITICAL: file_url is null in saved analysis', 'error', 'complete_flow');
        this.results.errors.push('Complete flow: file_url is null');
        return false;
      }
      
      // Test image retrieval
      const imageUrl = await this.service.getImageUrl(savedAnalysis.file_url);
      if (imageUrl) {
        this.log('Image URL retrieved successfully', 'success', 'complete_flow');
      } else {
        this.log('Image URL retrieval failed', 'error', 'complete_flow');
      }
      
      // Clean up
      await this.service.deleteAnalysis(savedAnalysis.id);
      this.log('Test analysis cleaned up', 'info', 'complete_flow');
      
      return !!savedAnalysis.file_url;
      
    } catch (error) {
      this.log(`Complete flow failed: ${error.message}`, 'error', 'complete_flow');
      this.results.errors.push(`Complete flow error: ${error.message}`);
      return false;
    }
  }

  async runDiagnostic() {
    this.log('🚀 Starting comprehensive diagnostic...', 'info');
    
    const steps = [
      { name: 'Environment Check', method: 'checkEnvironment' },
      { name: 'Authentication', method: 'checkAuthentication' },
      { name: 'Storage Connection', method: 'testStorageConnection' },
      { name: 'Direct Upload', method: 'testDirectUpload' },
      { name: 'Service Upload', method: 'testServiceUpload' },
      { name: 'Complete Flow', method: 'testCompleteFlow' }
    ];
    
    let allPassed = true;
    
    for (const step of steps) {
      this.log(`\n--- ${step.name} ---`, 'info');
      const result = await this[step.method]();
      
      if (!result) {
        allPassed = false;
        this.log(`❌ ${step.name} FAILED`, 'error');
        break; // Stop on first failure for clearer debugging
      } else {
        this.log(`✅ ${step.name} PASSED`, 'success');
      }
    }
    
    this.results.success = allPassed;
    this.generateReport();
    
    return this.results;
  }

  generateReport() {
    this.log('\n📊 DIAGNOSTIC REPORT', 'info');
    this.log(`Overall Result: ${this.results.success ? '✅ SUCCESS' : '❌ FAILURE'}`, this.results.success ? 'success' : 'error');
    
    if (this.results.errors.length > 0) {
      this.log('\n🚨 ERRORS FOUND:');
      this.results.errors.forEach((error, index) => {
        this.log(`  ${index + 1}. ${error}`, 'error');
      });
    }
    
    this.log('\n📋 STEP SUMMARY:');
    const stepSummary = {};
    this.results.uploadSteps.forEach(step => {
      if (!stepSummary[step.step]) {
        stepSummary[step.step] = { success: 0, error: 0, total: 0 };
      }
      stepSummary[step.step].total++;
      if (step.type === 'success') stepSummary[step.step].success++;
      if (step.type === 'error') stepSummary[step.step].error++;
    });
    
    Object.entries(stepSummary).forEach(([step, counts]) => {
      const status = counts.error > 0 ? '❌' : counts.success > 0 ? '✅' : 'ℹ️';
      this.log(`  ${status} ${step}: ${counts.success} success, ${counts.error} errors`);
    });
  }
}

// Create and run diagnostic
const diagnostic = new RealTimeUploadDiagnostic();
window.uploadDiagnostic = diagnostic;

// Auto-run
diagnostic.runDiagnostic().then(results => {
  console.log('\n🎯 Diagnostic complete. Results:', results);
  console.log('💡 Use window.uploadDiagnostic to access detailed results');
}).catch(error => {
  console.error('❌ Diagnostic failed:', error);
});
