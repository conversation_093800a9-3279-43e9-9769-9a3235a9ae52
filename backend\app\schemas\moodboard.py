"""
Pydantic schemas for moodboard operations.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class MoodboardCreateRequest(BaseModel):
    """Request schema for creating a new moodboard."""
    title: str = Field(default="Untitled Moodboard", description="Title of the moodboard")
    description: Optional[str] = Field(None, description="Description of the moodboard")
    tldraw_data: Optional[Dict[str, Any]] = Field(None, description="Tldraw canvas data")
    canvas_snapshot: Optional[str] = Field(None, description="URL of canvas snapshot image")
    tags: List[str] = Field(default_factory=list, description="Tags for categorization")
    is_public: bool = Field(default=False, description="Whether the moodboard is public")
    is_favorite: bool = Field(default=False, description="Whether the moodboard is marked as favorite")
    collaboration_enabled: bool = Field(default=False, description="Whether collaboration is enabled")
    shared_with: List[str] = Field(default_factory=list, description="List of user IDs with access")
    notes: Optional[str] = Field(None, description="Additional notes")


class MoodboardUpdateRequest(BaseModel):
    """Request schema for updating an existing moodboard."""
    title: Optional[str] = Field(None, description="Updated title")
    description: Optional[str] = Field(None, description="Updated description")
    tldraw_data: Optional[Dict[str, Any]] = Field(None, description="Updated Tldraw canvas data")
    canvas_snapshot: Optional[str] = Field(None, description="Updated canvas snapshot URL")
    tags: Optional[List[str]] = Field(None, description="Updated tags")
    is_public: Optional[bool] = Field(None, description="Updated public status")
    is_favorite: Optional[bool] = Field(None, description="Updated favorite status")
    collaboration_enabled: Optional[bool] = Field(None, description="Updated collaboration setting")
    shared_with: Optional[List[str]] = Field(None, description="Updated list of shared users")
    status: Optional[str] = Field(None, description="Updated status")
    notes: Optional[str] = Field(None, description="Updated notes")


class MoodboardResponse(BaseModel):
    """Response schema for moodboard operations."""
    id: str
    created_at: datetime
    updated_at: datetime
    user_id: str
    title: str
    description: Optional[str]
    tool_type: str
    version: Optional[str]
    tldraw_data: Optional[Dict[str, Any]]
    canvas_snapshot: Optional[str]
    tags: List[str]
    is_public: bool
    is_favorite: bool
    collaboration_enabled: bool
    shared_with: List[str]
    view_count: int
    last_viewed_at: Optional[datetime]
    status: str
    notes: Optional[str]


class MoodboardListResponse(BaseModel):
    """Response schema for listing moodboards."""
    moodboards: List[MoodboardResponse]
    total_count: int
    page: int
    limit: int


class MoodboardHistoryCreateRequest(BaseModel):
    """Request schema for creating moodboard history entry."""
    change_type: str = Field(..., description="Type of change (create, update, snapshot)")
    change_description: Optional[str] = Field(None, description="Description of the change")
    tldraw_data_snapshot: Dict[str, Any] = Field(..., description="Snapshot of tldraw data")
    is_auto_save: bool = Field(default=True, description="Whether this is an auto-save")


class MoodboardHistoryResponse(BaseModel):
    """Response schema for moodboard history operations."""
    id: str
    created_at: datetime
    moodboard_id: str
    user_id: str
    change_type: str
    change_description: Optional[str]
    tldraw_data_snapshot: Dict[str, Any]
    version_number: int
    is_auto_save: bool


class MoodboardOperationResponse(BaseModel):
    """Generic response schema for moodboard operations."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class MoodboardStatsResponse(BaseModel):
    """Response schema for moodboard statistics."""
    total_moodboards: int
    active_moodboards: int
    favorite_moodboards: int
    public_moodboards: int
    total_views: int
    recent_activity: List[Dict[str, Any]]
