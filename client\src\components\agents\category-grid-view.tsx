import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ChevronRight, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { categoriesData } from "@/data/agents-data";

interface CategoryGridViewProps {
  onCategorySelect: (categoryId: string) => void;
  onViewAll: () => void;
}

export default function CategoryGridView({
  onCategorySelect,
  onViewAll,
}: CategoryGridViewProps) {
  const [hasCategories, setHasCategories] = useState<boolean>(true);

  useEffect(() => {
    try {
      // Comprobar si tenemos categorías disponibles
      if (!categoriesData || categoriesData.length === 0) {
        console.error("No hay categorías disponibles");
        setHasCategories(false);
      } else {
        console.log(`Se encontraron ${categoriesData.length} categorías`);
        setHasCategories(true);
      }
    } catch (error) {
      console.error("Error al acceder a las categorías:", error);
      setHasCategories(false);
    }
  }, []);
  return (
    <div>
      <motion.div
        className="mb-6 flex justify-between items-center"
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <h2 className="text-2xl font-black">Categorías de Agentes</h2>
        <Button
          variant="link"
          onClick={onViewAll}
          className="text-blue-600 hover:text-blue-800 flex items-center font-bold"
        >
          Ver todos los agentes <ChevronRight size={16} />
        </Button>
      </motion.div>

      {/* Mensaje de error si no hay categorías */}
      {!hasCategories && (
        <motion.div
          className="bg-amber-100 border-2 border-amber-400 text-amber-700 px-6 py-4 rounded-lg mb-6 flex items-start"
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AlertCircle className="mr-3 h-6 w-6 flex-shrink-0" />
          <div>
            <h3 className="text-lg font-bold mb-2">
              No se pueden cargar las categorías
            </h3>
            <p>
              Estamos experimentando problemas para mostrar las categorías de
              agentes. Por favor, intenta recargar la página.
            </p>
            <Button
              className="mt-3 bg-amber-600 hover:bg-amber-700 text-white"
              onClick={() => window.location.reload()}
            >
              Recargar página
            </Button>
          </div>
        </motion.div>
      )}

      {/* Grid de categorías */}
      {hasCategories && (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {categoriesData.map((category) => (
            <motion.div
              key={category.id}
              className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden"
              whileHover={{
                y: -8,
                scale: 1.03,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
              }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div
                className="h-3 w-full"
                style={{ backgroundColor: category.color }}
              ></div>
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-4xl">{category.icon}</div>
                  <div className="bg-gray-100 px-2 py-1 rounded-full text-sm font-bold text-gray-700">
                    {category.agentCount} Agentes
                  </div>
                </div>
                <h3 className="text-xl font-black mb-2">{category.name}</h3>
                <p className="text-gray-600 mb-4 text-sm">
                  {category.description}
                </p>
                <Button
                  className="w-full bg-white hover:bg-gray-50 text-black border-2 border-black rounded-lg shadow-[4px_4px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all"
                  onClick={() => onCategorySelect(category.id)}
                >
                  Explorar Agentes <ChevronRight size={16} className="ml-1" />
                </Button>
              </div>
            </motion.div>
          ))}
        </motion.div>
      )}
    </div>
  );
}
