"""
Service for inpaint (fill areas) functionality using Stability AI v2beta API.
"""
import httpx
import io
import logging
from typing import Optional
from fastapi import HTTPEx<PERSON>, UploadFile

from app.core.config import settings
from app.schemas.inpaint import InpaintRequest, InpaintResponse

logger = logging.getLogger(__name__)


async def inpaint_areas_stability(
    image_file: UploadFile,
    mask_file: Optional[UploadFile],
    request: InpaintRequest
) -> InpaintResponse:
    """
    Fill areas in an image using Stability AI v2beta inpaint API.

    Args:
        image_file: The original image file
        mask_file: Optional mask file (black=preserve, white=fill)
        request: Inpaint request parameters

    Returns:
        InpaintResponse with the processed image

    Raises:
        HTTPException: If the API call fails
    """
    logger.info(f"Starting inpaint operation with prompt: '{request.prompt[:50]}...'")

    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para inpaint
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/edit/inpaint"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        # Leer y validar contenido de la imagen
        image_content = await image_file.read()
        await image_file.seek(0)  # Reset file pointer

        # Validar tamaño del archivo (máximo 10MB según documentación)
        max_size = 10 * 1024 * 1024  # 10MB
        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"Image file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )

        # Preparar archivos para el request usando io.BytesIO (como en erase_service)
        files = {
            "image": ("image.jpg", io.BytesIO(image_content), image_file.content_type or "image/jpeg")
        }

        # Agregar máscara si se proporciona
        if mask_file:
            mask_content = await mask_file.read()
            await mask_file.seek(0)  # Reset file pointer
            files["mask"] = ("mask.png", io.BytesIO(mask_content), mask_file.content_type or "image/png")

        # Preparar datos del formulario
        form_data = {
            "prompt": request.prompt,
            "output_format": request.output_format,
            "grow_mask": str(request.grow_mask),
            "seed": str(request.seed)
        }

        # Agregar negative_prompt si se proporciona
        if request.negative_prompt:
            form_data["negative_prompt"] = request.negative_prompt

        # Agregar style_preset si se proporciona
        if request.style_preset:
            form_data["style_preset"] = request.style_preset

        logger.info(f"Inpainting image using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Prompt: {request.prompt[:100]}...")
        logger.info(f"Grow mask: {request.grow_mask}")
        logger.info(f"Output format: {request.output_format}")
        logger.info(f"Style preset: {request.style_preset}")
        logger.info(f"Has mask: {mask_file is not None}")
        logger.info(f"Form data: {form_data}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            logger.info("Inpaint operation completed successfully")

            return InpaintResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in inpaint operation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process inpaint request: {str(e)}"
        )
