#!/usr/bin/env python3
"""
Test script para verificar que el Creative Genius genera IMÁGENES ESTÁTICAS chingones.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.creative_genius_service import CreativeGeniusService


async def test_static_image_concepts():
    """Test que el Creative Genius genera conceptos de IMÁGENES ESTÁTICAS."""
    print("🎨 Probando Creative Genius - IMÁGENES ESTÁTICAS CHINGONES...")
    
    # Initialize Creative Genius
    creative_genius = CreativeGeniusService()
    
    # Test cases para diferentes industrias
    test_cases = [
        {
            "businessName": "Wouf Suplementos",
            "industry": "suplementos para mascotas",
            "target_audience": "dueños de perros",
            "content_type": "educational"
        },
        {
            "businessName": "FitMax Gym", 
            "industry": "fitness",
            "target_audience": "personas que quieren ponerse en forma",
            "content_type": "promotional"
        },
        {
            "businessName": "TechStart",
            "industry": "tecnología", 
            "target_audience": "emprendedores",
            "content_type": "inspirational"
        }
    ]
    
    print("\n🔍 Verificando que NO genere conceptos de VIDEO...")
    
    for i, context in enumerate(test_cases):
        print(f"\n🎯 Test {i+1}: {context['businessName']} - {context['content_type']}")
        
        try:
            breakthrough = await creative_genius.create_breakthrough_content(
                user_context=context,
                content_type=context['content_type']
            )
            
            visual_concept = breakthrough.visual_concept.lower()
            hook = breakthrough.hook
            
            # Verificar que NO contenga palabras de video
            video_words = [
                "cortometraje", "video", "secuencia", "60 segundos", "30 segundos",
                "clip", "animación", "movimiento", "transición", "frames"
            ]
            
            has_video_words = any(word in visual_concept for word in video_words)
            
            # Verificar que SÍ contenga palabras de imagen estática
            static_words = [
                "imagen", "fotografía", "composición", "render", "ilustración",
                "diseño", "arte", "poster", "split screen", "contraste"
            ]
            
            has_static_words = any(word in visual_concept for word in static_words)
            
            print(f"   🎨 Concepto visual: {breakthrough.visual_concept[:100]}...")
            print(f"   🎯 Hook: '{hook}'")
            print(f"   📊 Viral score: {breakthrough.viral_score}/10")
            
            # Validaciones
            if has_video_words:
                print(f"   ❌ PROBLEMA: Contiene palabras de VIDEO: {[w for w in video_words if w in visual_concept]}")
            else:
                print(f"   ✅ CORRECTO: No contiene palabras de video")
            
            if has_static_words:
                print(f"   ✅ CORRECTO: Contiene palabras de imagen estática")
            else:
                print(f"   ⚠️ ADVERTENCIA: No contiene palabras específicas de imagen estática")
            
            # Verificar que el prompt de Ideogram sea para imagen estática
            ideogram_prompt = breakthrough.ideogram_prompt.lower()
            if any(word in ideogram_prompt for word in video_words):
                print(f"   ❌ PROBLEMA: Prompt de Ideogram contiene palabras de video")
            else:
                print(f"   ✅ CORRECTO: Prompt de Ideogram es para imagen estática")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n🎨 Test específico: Generando 5 conceptos para verificar consistencia...")
    
    context = {
        "businessName": "Wouf Suplementos",
        "industry": "suplementos para mascotas", 
        "target_audience": "dueños de perros"
    }
    
    static_concepts = []
    video_concepts = []
    
    for i in range(5):
        try:
            breakthrough = await creative_genius.create_breakthrough_content(
                user_context=context,
                content_type="educational"
            )
            
            visual_concept = breakthrough.visual_concept.lower()
            
            # Clasificar concepto
            video_words = ["cortometraje", "video", "secuencia", "segundos", "clip"]
            has_video = any(word in visual_concept for word in video_words)
            
            if has_video:
                video_concepts.append(breakthrough.visual_concept)
            else:
                static_concepts.append(breakthrough.visual_concept)
                
            print(f"   Concepto {i+1}: {'📹 VIDEO' if has_video else '🖼️ ESTÁTICO'} - {breakthrough.visual_concept[:80]}...")
            
        except Exception as e:
            print(f"   Error en concepto {i+1}: {e}")
    
    print(f"\n📊 RESULTADOS:")
    print(f"   🖼️ Conceptos ESTÁTICOS: {len(static_concepts)}/5")
    print(f"   📹 Conceptos de VIDEO: {len(video_concepts)}/5")
    
    if len(static_concepts) >= 4:
        print(f"   ✅ EXCELENTE: Mayoría de conceptos son estáticos")
    elif len(static_concepts) >= 2:
        print(f"   ⚠️ REGULAR: Algunos conceptos aún son de video")
    else:
        print(f"   ❌ PROBLEMA: Mayoría de conceptos siguen siendo de video")
    
    print("\n🎯 EJEMPLOS DE CONCEPTOS ESTÁTICOS IDEALES:")
    print("   ✅ 'Split screen dramático: perro triste vs energético, lighting cinematográfico'")
    print("   ✅ 'Render 3D: suplemento flotando en ambiente dorado con partículas'")
    print("   ✅ 'Fotografía conceptual estilo poster: transformación antes/después'")
    print("   ✅ 'Arte digital minimalista con contraste emocional potente'")
    print("   ✅ 'Ilustración épica estilo cómic con efectos visuales estáticos'")
    
    print("\n❌ CONCEPTOS A EVITAR:")
    print("   ❌ 'Cortometraje de 60 segundos donde...'")
    print("   ❌ 'Secuencia de video en estilo...'")
    print("   ❌ 'Animación que muestra...'")
    print("   ❌ 'Clip cinematográfico...'")
    
    print("\n🚀 CONCLUSIÓN:")
    if len(static_concepts) >= 4:
        print("✅ Creative Genius ARREGLADO - Genera imágenes estáticas chingones")
    else:
        print("⚠️ Creative Genius necesita más ajustes para evitar conceptos de video")


if __name__ == "__main__":
    asyncio.run(test_static_image_concepts())
