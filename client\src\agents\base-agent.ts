/**
 * Base Agent Abstract Class
 * Provides a common interface for all agent implementations
 */
import { AgentTask } from '../types/core/agent-types';
import { AgentMessage } from '../types/core/protocol-types';

export abstract class BaseAgent {
  // Abstract methods to be implemented by concrete agent classes
  abstract initialize(): Promise<void>;
  abstract execute(task: AgentTask): Promise<AgentMessage>;
  abstract cleanup(): Promise<void>;
}
