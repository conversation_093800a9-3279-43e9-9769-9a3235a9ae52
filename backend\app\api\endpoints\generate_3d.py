"""
API endpoints for 3D generation functionality.
"""

import logging
from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from typing import Optional

from app.schemas.generate_3d import (
    Generate3DRequest,
    FrontendGenerate3DResponse,
    TextureResolution,
    RemeshType,
    TargetType,
    Model3DType
)
from app.services.generate_3d_service import generate_3d_model_stability

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate-3d", response_model=FrontendGenerate3DResponse)
async def generate_3d_model(
    image: UploadFile = File(..., description="Input image to convert to 3D"),
    model_type: Optional[str] = Form("fast", description="Model type (fast, point_aware)"),
    texture_resolution: Optional[str] = Form("1024", description="Texture resolution (512, 1024, 2048)"),
    foreground_ratio: Optional[float] = Form(None, description="Foreground ratio (varies by model)"),
    remesh: Optional[str] = Form("none", description="Remesh type (none, triangle, quad)"),
    vertex_count: Optional[int] = Form(-1, description="Target vertex count (Fast 3D only)"),
    target_type: Optional[str] = Form("none", description="Target type (Point Aware 3D only)"),
    target_count: Optional[int] = Form(1000, description="Target count (Point Aware 3D only)"),
    guidance_scale: Optional[float] = Form(3.0, description="Guidance scale (Point Aware 3D only)"),
    seed: Optional[int] = Form(0, description="Seed for randomness (Point Aware 3D only)")
) -> FrontendGenerate3DResponse:
    """
    Generate a 3D model from an input image using Stability AI's 3D generation models.

    This endpoint supports two models:
    - **Stable Fast 3D**: Fast generation (2 credits, ~30-60 seconds)
    - **Stable Point Aware 3D**: Advanced generation with more control (4 credits, ~2-5 minutes)

    **Common Parameters:**
    - **image**: Input image file (JPEG, PNG, WebP)
    - **model_type**: Model to use (fast, point_aware) - default: fast
    - **texture_resolution**: Resolution of textures (512, 1024, 2048) - default: 1024
    - **foreground_ratio**: Controls padding around object (varies by model)
    - **remesh**: Remeshing algorithm (none, triangle, quad) - default: none

    **Fast 3D Specific:**
    - **vertex_count**: Target vertex count (-1 for no limit) - default: -1

    **Point Aware 3D Specific:**
    - **target_type**: Simplification type (none, vertex, face) - default: none
    - **target_count**: Target vertex/face count (100-20000) - default: 1000
    - **guidance_scale**: Guidance scale for detail (1.0-10.0) - default: 3.0
    - **seed**: Seed for randomness (0 for random) - default: 0

    **Returns:**
    - **model_url**: Data URL containing the GLB file
    - **filename**: Suggested filename for download
    - **size_mb**: Size of the model in megabytes
    - **metadata**: Generation parameters and model info
    """
    logger.info(f"Received 3D generation request for image: {image.filename}")
    
    try:
        # Validar archivo de imagen
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="El archivo debe ser una imagen (JPEG, PNG, WebP)"
            )
        
        # Validar tamaño del archivo (máximo 10MB)
        if image.size and image.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="El archivo de imagen no puede ser mayor a 10MB"
            )
        
        # Validar y convertir parámetros
        try:
            model_type_enum = Model3DType(model_type)
        except ValueError:
            model_type_enum = Model3DType.FAST

        try:
            texture_res = TextureResolution(texture_resolution)
        except ValueError:
            texture_res = TextureResolution.MEDIUM

        try:
            remesh_type = RemeshType(remesh)
        except ValueError:
            remesh_type = RemeshType.NONE

        try:
            target_type_enum = TargetType(target_type)
        except ValueError:
            target_type_enum = TargetType.NONE

        # Validar foreground_ratio según el modelo
        if foreground_ratio is None:
            if model_type_enum == Model3DType.POINT_AWARE:
                foreground_ratio = 1.3  # Default para Point Aware
            else:
                foreground_ratio = 0.85  # Default para Fast 3D

        # Validar vertex_count
        if vertex_count < -1 or vertex_count > 20000:
            vertex_count = -1

        # Validar target_count
        if target_count < 100 or target_count > 20000:
            target_count = 1000

        # Validar guidance_scale
        if guidance_scale < 1.0 or guidance_scale > 10.0:
            guidance_scale = 3.0

        # Validar seed
        if seed < 0 or seed > 4294967294:
            seed = 0

        # Crear request object
        request = Generate3DRequest(
            model_type=model_type_enum,
            texture_resolution=texture_res,
            foreground_ratio=foreground_ratio,
            remesh=remesh_type,
            vertex_count=vertex_count,
            target_type=target_type_enum,
            target_count=target_count,
            guidance_scale=guidance_scale,
            seed=seed
        )

        # Llamar al servicio de Stability AI
        service_response = await generate_3d_model_stability(image, request)
        
        if not service_response.success:
            raise HTTPException(
                status_code=500,
                detail=service_response.error or "Error generating 3D model"
            )

        # Crear data URL para el frontend
        model_data_url = f"data:model/gltf-binary;base64,{service_response.model_data}"
        
        # Calcular tamaño en MB
        size_mb = service_response.size_bytes / (1024 * 1024) if service_response.size_bytes else 0

        logger.info(f"3D model generated successfully. Size: {size_mb:.2f}MB")

        return FrontendGenerate3DResponse(
            success=True,
            model_url=model_data_url,
            filename=service_response.filename,
            size_mb=round(size_mb, 2),
            metadata={
                **service_response.metadata,
                "generation_time": "~30-60 seconds" if model_type_enum == Model3DType.FAST else "~2-5 minutes"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in 3D generation endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
