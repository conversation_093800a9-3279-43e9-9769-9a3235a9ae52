"""
Tests for the external services API endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app
from app.core.config import settings

client = TestClient(app)


@pytest.fixture
def mock_api_key():
    """Fixture to mock API key verification."""
    with patch("app.core.dependencies.verify_api_key", return_value="test_api_key"):
        yield


@pytest.fixture
def mock_external_api_key():
    """Fixture to mock external API key retrieval."""
    with patch("app.core.dependencies.get_external_api_key", return_value="test_external_key"):
        yield


def test_check_api_keys_status():
    """Test the API key status endpoint."""
    response = client.get("/api/v1/external/status")
    assert response.status_code == 200
    
    data = response.json()
    assert "status" in data
    assert "services" in data
    assert isinstance(data["services"], dict)
    
    # Check that the expected services are in the response
    for service in ["gemini", "stability", "elevenlabs"]:
        assert service in data["services"]
        assert isinstance(data["services"][service], bool)


def test_get_service_api_key_unauthorized():
    """Test that API key endpoint requires authentication."""
    response = client.get("/api/v1/external/gemini/key")
    assert response.status_code == 401
    
    data = response.json()
    assert "detail" in data
    assert "code" in data["detail"]
    assert data["detail"]["code"] == "missing_api_key"


def test_get_service_api_key_invalid_service():
    """Test that API key endpoint validates service names."""
    with patch("app.core.dependencies.verify_api_key", return_value="test_api_key"):
        response = client.get("/api/v1/external/invalid_service/key")
        assert response.status_code == 400
        
        data = response.json()
        assert "detail" in data
        assert "code" in data["detail"]
        assert data["detail"]["code"] == "invalid_service"


def test_get_service_api_key_success(mock_api_key, mock_external_api_key):
    """Test successful API key retrieval."""
    response = client.get(
        "/api/v1/external/gemini/key",
        headers={"X-API-Key": "test_api_key"}
    )
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "success"
    assert data["service"] == "gemini"
    assert data["configured"] is True
    assert "key_preview" in data


def test_get_service_api_key_not_configured(mock_api_key):
    """Test API key retrieval when key is not configured."""
    with patch("app.core.dependencies.get_external_api_key", side_effect=Exception("API key not configured")):
        response = client.get(
            "/api/v1/external/gemini/key",
            headers={"X-API-Key": "test_api_key"}
        )
        assert response.status_code == 500
        
        data = response.json()
        assert "detail" in data
        assert "code" in data["detail"]
        assert data["detail"]["code"] == "api_key_retrieval_error"
