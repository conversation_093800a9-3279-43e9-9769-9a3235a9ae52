"""
Input validation utilities for API endpoints.

This module provides functions for validating and sanitizing input data
to prevent security vulnerabilities and ensure data integrity.
"""

import re
import logging
import html
from typing import Any, Dict, List, Optional, Union, Callable

logger = logging.getLogger(__name__)


def sanitize_string(value: str) -> str:
    """
    Sanitize a string by escaping HTML entities and removing control characters.
    
    Args:
        value: The string to sanitize
        
    Returns:
        The sanitized string
    """
    if not value:
        return value
        
    # Escape HTML entities
    sanitized = html.escape(value)
    
    # Remove control characters
    sanitized = re.sub(r'[\x00-\x1F\x7F]', '', sanitized)
    
    return sanitized


def validate_string(
    value: str,
    min_length: int = 0,
    max_length: int = 10000,
    pattern: Optional[str] = None,
    sanitize: bool = True
) -> str:
    """
    Validate and optionally sanitize a string.
    
    Args:
        value: The string to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        pattern: Optional regex pattern to match
        sanitize: Whether to sanitize the string
        
    Returns:
        The validated and optionally sanitized string
        
    Raises:
        ValueError: If the string is invalid
    """
    if not isinstance(value, str):
        raise ValueError(f"Expected string, got {type(value).__name__}")
        
    if len(value) < min_length:
        raise ValueError(f"String too short (minimum {min_length} characters)")
        
    if len(value) > max_length:
        raise ValueError(f"String too long (maximum {max_length} characters)")
        
    if pattern and not re.match(pattern, value):
        raise ValueError(f"String does not match required pattern: {pattern}")
        
    return sanitize_string(value) if sanitize else value


def validate_dict(
    data: Dict[str, Any],
    required_keys: List[str] = None,
    optional_keys: List[str] = None,
    validators: Dict[str, Callable] = None,
    allow_extra_keys: bool = False
) -> Dict[str, Any]:
    """
    Validate a dictionary against a schema.
    
    Args:
        data: The dictionary to validate
        required_keys: List of required keys
        optional_keys: List of optional keys
        validators: Dictionary of validation functions for specific keys
        allow_extra_keys: Whether to allow keys not in required or optional lists
        
    Returns:
        The validated dictionary
        
    Raises:
        ValueError: If the dictionary is invalid
    """
    if not isinstance(data, dict):
        raise ValueError(f"Expected dictionary, got {type(data).__name__}")
        
    required_keys = required_keys or []
    optional_keys = optional_keys or []
    validators = validators or {}
    
    # Check for required keys
    for key in required_keys:
        if key not in data:
            raise ValueError(f"Missing required key: {key}")
            
    # Check for unexpected keys
    if not allow_extra_keys:
        allowed_keys = set(required_keys + optional_keys)
        for key in data:
            if key not in allowed_keys:
                raise ValueError(f"Unexpected key: {key}")
                
    # Apply validators
    validated_data = {}
    for key, value in data.items():
        if key in validators:
            try:
                validated_data[key] = validators[key](value)
            except ValueError as e:
                raise ValueError(f"Invalid value for {key}: {str(e)}")
        else:
            validated_data[key] = value
            
    return validated_data


def validate_list(
    data: List[Any],
    min_length: int = 0,
    max_length: int = 1000,
    item_validator: Optional[Callable] = None
) -> List[Any]:
    """
    Validate a list.
    
    Args:
        data: The list to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        item_validator: Optional function to validate each item
        
    Returns:
        The validated list
        
    Raises:
        ValueError: If the list is invalid
    """
    if not isinstance(data, list):
        raise ValueError(f"Expected list, got {type(data).__name__}")
        
    if len(data) < min_length:
        raise ValueError(f"List too short (minimum {min_length} items)")
        
    if len(data) > max_length:
        raise ValueError(f"List too long (maximum {max_length} items)")
        
    if item_validator:
        validated_items = []
        for i, item in enumerate(data):
            try:
                validated_items.append(item_validator(item))
            except ValueError as e:
                raise ValueError(f"Invalid item at index {i}: {str(e)}")
        return validated_items
        
    return data
