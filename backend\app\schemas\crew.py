"""
Schemas for crew and agent-related operations.

This module defines the Pydantic models for crew and agent-related operations,
including request and response schemas for the crew API endpoints.
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field


class ReasoningTraceItem(BaseModel):
    """A single item in the reasoning trace."""
    type: str = Field(..., description="Type of trace item (prompt, asset, error, info)")
    content: str = Field(..., description="Content of the trace item")
    agent: Optional[str] = Field(None, description="Agent that generated this trace item")
    timestamp: str = Field(..., description="Timestamp of the trace item")
    trace_snippet: Optional[List[str]] = Field(None, description="Optional code or trace snippet")


class CrewRunRequest(BaseModel):
    """Request schema for running a crew of agents."""
    crew_id: str = Field(..., description="Unique identifier for the crew run")
    prompt: str = Field(..., description="The prompt to process")
    inputs: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Input parameters for the crew execution")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context for the crew run")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Configuration for the crew run")


class CrewRunResponse(BaseModel):
    """Response schema for running a crew of agents."""
    request_id: str = Field(..., description="Unique identifier for the request")
    status: str = Field(..., description="Status of the crew run (success, error)")
    result: str = Field(..., description="Result of the crew run")
    reasoning_trace: List[ReasoningTraceItem] = Field(default_factory=list, description="Reasoning trace of the crew run")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the crew run")
    error: Optional[str] = Field(None, description="Error message if the crew run failed")


class AgentChatRequest(BaseModel):
    """Request schema for chatting with a specific agent."""
    agent_id: str = Field(..., description="ID of the agent to chat with")
    message: str = Field(..., description="Message to send to the agent")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context for the chat")


class AgentChatResponse(BaseModel):
    """Response schema for chatting with a specific agent."""
    response: Union[str, Dict[str, Any]] = Field(..., description="Response from the agent - can be string or structured object")
    reasoning_trace: List[ReasoningTraceItem] = Field(default_factory=list, description="Reasoning trace of the agent")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata about the response")
    error: Optional[str] = Field(None, description="Error message if the chat failed")
