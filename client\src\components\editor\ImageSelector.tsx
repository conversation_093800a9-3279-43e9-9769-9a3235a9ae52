import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useEditor } from "./EditorContext";
import { Upload } from "lucide-react";

// Datos de ejemplo para las imágenes
const sampleImages = [
  {
    id: "img1",
    url: "https://images.unsplash.com/photo-1580927752452-89d86da3fa0a?w=500&auto=format&fit=crop&q=60",
    alt: "Tecnología",
  },
  {
    id: "img2",
    url: "https://images.unsplash.com/photo-1492684223066-81342ee5ff30?w=500&auto=format&fit=crop&q=60",
    alt: "Negocios",
  },
  {
    id: "img3",
    url: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500&auto=format&fit=crop&q=60",
    alt: "Marketing",
  },
  {
    id: "img4",
    url: "https://images.unsplash.com/photo-1562577309-4932fdd64cd1?w=500&auto=format&fit=crop&q=60",
    alt: "Redes sociales",
  },
  {
    id: "img5",
    url: "https://images.unsplash.com/photo-1546198632-9ef6368bef12?w=500&auto=format&fit=crop&q=60",
    alt: "Creatividad",
  },
  {
    id: "img6",
    url: "https://images.unsplash.com/photo-1581093588401-fbb62a02f120?w=500&auto=format&fit=crop&q=60",
    alt: "Datos",
  },
  {
    id: "img7",
    url: "https://images.unsplash.com/photo-1533750349088-cd871a92f312?w=500&auto=format&fit=crop&q=60",
    alt: "Trabajo",
  },
  {
    id: "img8",
    url: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=500&auto=format&fit=crop&q=60",
    alt: "Galaxia",
  },
];

interface ImageSelectorProps {
  onClose?: () => void;
}

const ImageSelector: React.FC<ImageSelectorProps> = ({ onClose }) => {
  const { addElement } = useEditor();

  const handleSelectImage = (imageUrl: string) => {
    // Añadir imagen al centro del canvas
    const newElement = {
      type: "image" as const,
      content: imageUrl,
      position: { x: 150, y: 150 },
      style: { maxWidth: "300px" },
    };

    addElement(newElement);
    if (onClose) onClose();
  };

  const handleUploadClick = () => {
    // En una implementación real, esto abriría un selector de archivos
    alert(
      "Esta funcionalidad permitiría subir imágenes propias. En una implementación completa, se conectaría con el sistema de archivos o servicios como Cloudinary/Firebase Storage.",
    );
  };

  return (
    <div className="image-selector p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-medium">Seleccionar imagen</h3>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            ✕
          </Button>
        )}
      </div>

      <div className="mb-4">
        <div
          className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={handleUploadClick}
        >
          <Upload className="w-6 h-6 mx-auto text-gray-400" />
          <p className="mt-1 text-xs text-gray-500">Sube tu propia imagen</p>
        </div>
      </div>

      <div className="mb-3">
        <h4 className="text-xs font-medium text-gray-500">Imágenes de stock</h4>
      </div>

      <ScrollArea className="h-72">
        <div className="grid grid-cols-2 gap-2">
          {sampleImages.map((image) => (
            <Card
              key={image.id}
              className="cursor-pointer hover:border-blue-300 transition-colors"
              onClick={() => handleSelectImage(image.url)}
            >
              <CardContent className="p-2">
                <img
                  src={image.url}
                  alt={image.alt}
                  className="w-full h-auto object-cover aspect-square rounded"
                />
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default ImageSelector;
