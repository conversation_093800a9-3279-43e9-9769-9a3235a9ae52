"""
Main post generation service that orchestrates all components.
"""

import logging
import uuid
from typing import Dict, Any, List

from app.models.post_models import GeneratedPost, PostGenerationResponse
from app.services.ai_content_intelligence import AIContentIntelligence
from app.services.content_generation_service import ContentGenerationService
from app.services.image_generation_service import ImageGenerationService
from app.services.ideogram_service import IdeogramService
from app.services.watermark_service import watermark_service
from app.services.creative_genius_service import CreativeGeniusService
from app.utils.platform_utils import get_platform_dimensions

logger = logging.getLogger(__name__)


class PostGenerationService:
    """Main service for orchestrating post generation with all components."""
    
    def __init__(self):
        self.ai_intelligence = AIContentIntelligence()
        self.content_service = ContentGenerationService()
        self.image_service = ImageGenerationService()
        self.ideogram_service = IdeogramService()
        self.creative_genius = CreativeGeniusService()  # 🧠 EL CEREBRO CREATIVO

        logger.info("✅ Post Generation Service initialized with Creative Genius")
    
    async def generate_posts_batch(self, brand_info: Dict[str, Any], design_config: Dict[str, Any], generation_config: Dict[str, Any]) -> PostGenerationResponse:
        """
        Generate multiple posts using the intelligent content system.
        
        Args:
            brand_info: Brand information from steps 1-3
            design_config: Design configuration from step 4
            generation_config: Generation settings
            
        Returns:
            PostGenerationResponse with generated posts
        """
        try:
            # Extract configuration
            template = design_config.get("selectedTheme", "Balance")
            platform = design_config.get("platform", "Instagram")
            post_count = generation_config.get("count", 3)
            
            logger.info(f"🚀 Starting batch generation: {post_count} posts, template: {template}, platform: {platform}")
            
            # Get user topic from brand info
            user_topic = None
            if brand_info.get("topics") and len(brand_info.get("topics", [])) > 0:
                user_topic = brand_info.get("topics")[0]
            
            # Generate intelligent content plan with context analysis
            content_strategy = await self.ai_intelligence.generate_strategic_content_plan(template, brand_info, platform, user_topic)
            logger.info(f"📋 Content strategy for topic '{content_strategy.get('topic', 'unknown')}': {content_strategy.get('context_analysis', {}).get('nicho', 'general')}")
            
            # Get platform-specific dimensions
            context_analysis = content_strategy.get("context_analysis", {})
            content_type = context_analysis.get("tipo_contenido", "educational")
            image_dimensions = get_platform_dimensions(platform, content_type)
            logger.info(f"🖼️ Using platform-specific dimensions: {image_dimensions} for {platform}")
            
            # 🧠 CREATIVE GENIUS: Generate breakthrough concepts for each post
            creative_concepts = []
            for i in range(post_count):
                try:
                    logger.info(f"🎨 Creative Genius creating breakthrough concept {i+1}...")

                    # Use Creative Genius to create revolutionary concept
                    breakthrough = await self.creative_genius.create_breakthrough_content(
                        user_context=brand_info,
                        content_type=content_type
                    )

                    creative_concepts.append(breakthrough)
                    logger.info(f"🚀 Breakthrough concept {i+1} created! Viral score: {breakthrough.viral_score}")
                    logger.info(f"💡 Why it's brilliant: {breakthrough.why_its_brilliant}")

                except Exception as e:
                    logger.error(f"❌ Creative Genius failed for concept {i+1}: {e}")
                    # Fallback to traditional method
                    visual_hook = await self.content_service.generate_visual_hook_content(content_strategy, brand_info)
                    from app.services.creative_genius_service import CreativeBreakthrough
                    fallback_concept = CreativeBreakthrough(
                        visual_concept="Professional social media design",
                        hook=visual_hook,
                        content_angle="traditional_approach",
                        ideogram_prompt=f'A professional social media graphic with text that reads: "{visual_hook}"',
                        emotional_journey={"hook_emotion": "interest", "middle_emotion": "engagement", "end_emotion": "action"},
                        viral_score=6.0,
                        why_its_brilliant="Solid traditional approach with clear messaging",
                        psychology_target="general_interest",
                        art_direction="professional"
                    )
                    creative_concepts.append(fallback_concept)

            logger.info(f"🧠 Creative Genius generated {len(creative_concepts)} breakthrough concepts")
            
            # Generate images using Creative Genius concepts
            image_urls = []
            image_metadata_list = []  # Store metadata for each image
            for i, concept in enumerate(creative_concepts):
                try:
                    # Use the Creative Genius masterpiece prompt
                    masterpiece_prompt = concept.ideogram_prompt

                    logger.info(f"🎨 Generating MASTERPIECE image {i+1} with Creative Genius concept...")
                    logger.info(f"🧠 Creative concept: {concept.visual_concept[:100]}...")
                    logger.info(f"🎯 Hook: '{concept.hook}'")
                    logger.info(f"🖼️ Masterpiece prompt: {masterpiece_prompt[:200]}...")
                    logger.info(f"📊 Viral potential: {concept.viral_score}/10")

                    # Generate image with Ideogram using Creative Genius prompt
                    image_result = await self.ideogram_service.generate_image(
                        prompt=masterpiece_prompt,
                        dimensions=image_dimensions
                    )

                    logger.info(f"📊 Ideogram response for image {i+1}: success={image_result.get('success') if image_result else False}")
                    
                    if image_result and image_result.get("success") and image_result.get("image_url"):
                        image_url = image_result["image_url"]

                        # Store image locally for future reference use
                        from app.services.image_storage_service import image_storage_service
                        stored_path = await image_storage_service.store_image(image_url)
                        if stored_path:
                            logger.info(f"📁 Stored image {i+1} locally for future reference")

                        # Apply watermark if enabled
                        if watermark_service.is_enabled():
                            try:
                                watermarked_url = await watermark_service.add_watermark(image_url)
                                if watermarked_url:
                                    image_url = watermarked_url
                                    logger.info(f"✅ Applied watermark to image {i+1}")
                            except Exception as e:
                                logger.warning(f"⚠️ Watermark failed for image {i+1}: {e}")

                        image_urls.append(image_url)
                        image_metadata_list.append(image_result.get("metadata", {}))
                        logger.info(f"✅ Generated strategic image {i+1} successfully!")
                    else:
                        image_urls.append(None)
                        image_metadata_list.append({})
                        logger.warning(f"❌ Failed to generate image {i+1}")
                        
                except Exception as e:
                    logger.error(f"❌ Error generating image {i+1}: {e}")
                    image_urls.append(None)
                    image_metadata_list.append({})
            
            # Generate post content using Creative Genius concepts
            generated_posts = []
            for i in range(post_count):
                try:
                    concept = creative_concepts[i]

                    # Generate content based on Creative Genius concept
                    post_content = await self.content_service.generate_creative_genius_content(
                        creative_concept=concept,
                        brand_info=brand_info,
                        platform=platform
                    )
                    
                    # Create post object
                    post = GeneratedPost(
                        id=str(uuid.uuid4()),
                        text=post_content,
                        image_url=image_urls[i] if i < len(image_urls) else None,
                        template=template,
                        platform=platform,
                        metadata={
                            "visual_hook": concept.hook,  # Creative Genius hook
                            "creative_concept": concept.visual_concept,
                            "content_angle": concept.content_angle,
                            "viral_score": concept.viral_score,
                            "psychology_target": concept.psychology_target,
                            "art_direction": concept.art_direction,
                            "why_brilliant": concept.why_its_brilliant,
                            "brand_name": brand_info.get("businessName", "Unknown"),
                            "generation_index": i+1,
                            "provider": "ideogram",
                            "image_generated": image_urls[i] is not None if i < len(image_urls) else False,
                            "dimensions": image_dimensions,
                            "content_type": content_strategy.get("context_analysis", {}).get("tipo_contenido", "educational"),
                            "batch_generated": True,
                            "generation_method": "creative_genius",
                            "creative_engine": "breakthrough_content_generation",
                            # Add Ideogram metadata for similarity generation
                            "seed": image_metadata_list[i].get("seed") if i < len(image_metadata_list) else None,
                            "style_type": image_metadata_list[i].get("style_type", "DESIGN") if i < len(image_metadata_list) else "DESIGN"
                        }
                    )
                    
                    generated_posts.append(post)
                    logger.info(f"✅ Generated post {i+1}/{post_count}")
                    
                except Exception as e:
                    logger.error(f"❌ Error generating post {i+1}: {e}")
                    # Continue with other posts even if one fails
            
            # Create response
            response = PostGenerationResponse(
                success=len(generated_posts) > 0,
                posts=generated_posts,
                total_generated=len(generated_posts),
                error=None if len(generated_posts) > 0 else "Failed to generate any posts",
                metadata={
                    "template": template,
                    "platform": platform,
                    "content_type": content_strategy.get("context_analysis", {}).get("tipo_contenido", "educational"),
                    "content_strategy": content_strategy.get("context_analysis", {}).get("tipo_contenido", "educational"),
                    "images_generated": sum(1 for post in generated_posts if post.image_url is not None),
                    "provider": "ideogram",
                    "dimensions": image_dimensions,
                    "platform_optimized": True,
                    "batch_generated": True,
                    "generation_method": "strategic_separation",
                    "content_quality": "professional_ai_generated",
                    "visual_textual_separation": True
                }
            )
            
            logger.info(f"🎉 Batch generation completed: {len(generated_posts)}/{post_count} posts generated successfully")
            return response
            
        except Exception as e:
            logger.error(f"❌ Critical error in batch generation: {e}")
            return PostGenerationResponse(
                success=False,
                posts=[],
                total_generated=0,
                error=f"Failed to generate posts: {str(e)}",
                metadata={}
            )
    
    async def generate_single_post(self, brand_info: Dict[str, Any], design_config: Dict[str, Any]) -> PostGenerationResponse:
        """
        Generate a single post (legacy endpoint support).

        Args:
            brand_info: Brand information
            design_config: Design configuration

        Returns:
            PostGenerationResponse with single generated post
        """
        generation_config = {"count": 1}
        return await self.generate_posts_batch(brand_info, design_config, generation_config)

    async def generate_similar_posts(self, brand_info: Dict[str, Any], reference_post: Dict[str, Any], generation_config: Dict[str, Any]) -> PostGenerationResponse:
        """
        Generate posts similar to a reference post using the dedicated SimilarPostsService.

        Args:
            brand_info: Brand information
            reference_post: The post to use as reference
            generation_config: Generation settings

        Returns:
            PostGenerationResponse with similar generated posts
        """
        try:
            from app.services.similar_posts_service import similar_posts_service

            count = generation_config.get("count", 3)
            logger.info(f"🎯 Starting similar post generation: {count} posts based on reference")

            # Extract reference post details
            reference_content = reference_post.get("content", "")
            reference_template = reference_post.get("template", "Balance")
            reference_platform = reference_post.get("platform", "Instagram")

            logger.info(f"📋 Reference post template: {reference_template}, platform: {reference_platform}")
            logger.info(f"📝 Reference content: {reference_content[:100]}...")

            # Create user context for SimilarPostsService
            user_context = {
                "businessName": brand_info.get("businessName", "Business"),
                "industry": brand_info.get("industry", "general"),
                "target_audience": brand_info.get("target_audience", "general audience"),
                "brandColor": brand_info.get("brandColor") or brand_info.get("brand_color"),
                "voice": brand_info.get("voice", "professional")
            }

            # Use the dedicated SimilarPostsService
            logger.info(f"🔄 Using dedicated SimilarPostsService for similarity")
            similar_posts_data = await similar_posts_service.generate_similar_posts(
                reference_post, user_context, count
            )

            if not similar_posts_data:
                logger.error("❌ SimilarPostsService returned no similar posts")
                return PostGenerationResponse(
                    success=False,
                    error="Failed to generate similar posts",
                    posts=[],
                    total_generated=0
                )

            # Convert to GeneratedPost objects
            posts = []
            for i, post_data in enumerate(similar_posts_data):
                try:
                    # Get image URL from SimilarPostsService (already generated)
                    image_url = post_data.get("image_url")

                    # Apply watermark if enabled and image exists
                    if image_url and watermark_service.is_enabled():
                        try:
                            watermarked_url = await watermark_service.add_watermark(image_url)
                            if watermarked_url:
                                image_url = watermarked_url
                        except Exception as e:
                            logger.warning(f"⚠️ Watermark failed for similar image {i+1}: {e}")

                    # Create post object
                    post = GeneratedPost(
                        id=str(uuid.uuid4()),
                        text=post_data.get("content", ""),
                        image_url=image_url,
                        template=reference_template,
                        platform=reference_platform,
                        metadata={
                            "visual_hook": post_data.get("visual_hook", ""),
                            "content_strategy": "dedicated_similarity_service",
                            "brand_name": brand_info.get("businessName", "Unknown"),
                            "generation_index": i+1,
                            "provider": "ideogram",
                            "image_generated": image_url is not None,
                            "dimensions": get_platform_dimensions(reference_platform, "educational"),
                            "content_type": post_data.get("content_type", "similar"),
                            "similar_to_reference": True,
                            "reference_template": reference_template,
                            "generation_method": "dedicated_similar_posts_service",
                            "similarity_analysis": post_data.get("similarity_analysis", {}),
                            "reference_used": post_data.get("reference_used", False)
                        }
                    )

                    posts.append(post)
                    logger.info(f"✅ Generated similar post {i+1}/{count}")

                except Exception as e:
                    logger.error(f"❌ Error processing similar post {i+1}: {e}")
                    continue

            if not posts:
                logger.error("❌ No similar posts were generated successfully")
                return PostGenerationResponse(
                    success=False,
                    error="Failed to generate any similar posts",
                    posts=[],
                    total_generated=0
                )

            logger.info(f"🎉 Similar post generation completed: {len(posts)}/{count} posts generated successfully")

            return PostGenerationResponse(
                success=True,
                posts=posts,
                total_generated=len(posts),
                metadata={
                    "generation_type": "similar_posts",
                    "reference_template": reference_template,
                    "reference_platform": reference_platform,
                    "similarity_approach": "improved_content_generation",
                    "brand_name": brand_info.get("businessName", "Unknown")
                }
            )

        except Exception as e:
            logger.error(f"❌ Critical error in similar post generation: {e}")
            return PostGenerationResponse(
                success=False,
                error=f"Similar post generation failed: {str(e)}",
                posts=[],
                total_generated=0
            )
