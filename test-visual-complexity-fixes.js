/**
 * Test script to verify Visual Complexity Analyzer fixes
 * 
 * This script tests:
 * 1. History auto-refresh after analysis completion
 * 2. Image display when loading saved analyses
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🧪 Starting Visual Complexity Analyzer fixes test...');

// Test configuration
const TEST_CONFIG = {
  waitTime: 2000, // Wait time between actions
  maxRetries: 3,
  testImageUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==' // 1x1 pixel PNG
};

// Helper function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to find element with retry
const findElement = async (selector, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    const element = document.querySelector(selector);
    if (element) return element;
    await wait(1000);
  }
  throw new Error(`Element not found: ${selector}`);
};

// Test 1: History Auto-Refresh After Analysis
async function testHistoryAutoRefresh() {
  console.log('📋 Test 1: History Auto-Refresh After Analysis');
  
  try {
    // Navigate to analyze tab
    const analyzeTab = await findElement('[value="analyze"]');
    analyzeTab.click();
    await wait(TEST_CONFIG.waitTime);
    
    // Check if we can find the file input
    const fileInput = await findElement('input[type="file"]');
    console.log('✅ Found file input');
    
    // Create a test image file
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 100, 100);
    
    canvas.toBlob(async (blob) => {
      const file = new File([blob], 'test-image.png', { type: 'image/png' });
      
      // Simulate file selection
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(file);
      fileInput.files = dataTransfer.files;
      
      // Trigger change event
      const event = new Event('change', { bubbles: true });
      fileInput.dispatchEvent(event);
      
      console.log('📁 Test file uploaded');
      await wait(TEST_CONFIG.waitTime);
      
      // Check if analyze button is available
      const analyzeButton = await findElement('button:contains("Analizar")');
      if (analyzeButton) {
        console.log('🔍 Found analyze button, clicking...');
        analyzeButton.click();
        
        // Wait for analysis to complete
        await wait(10000); // Wait longer for analysis
        
        // Check if history tab shows new analysis
        const historyTab = await findElement('[value="history"]');
        historyTab.click();
        await wait(TEST_CONFIG.waitTime);
        
        // Look for analysis cards
        const analysisCards = document.querySelectorAll('[class*="analysis-card"], [class*="AnalysisCard"]');
        console.log(`📊 Found ${analysisCards.length} analysis cards in history`);
        
        if (analysisCards.length > 0) {
          console.log('✅ Test 1 PASSED: History shows analyses');
          return true;
        } else {
          console.log('❌ Test 1 FAILED: No analyses found in history');
          return false;
        }
      }
    }, 'image/png');
    
  } catch (error) {
    console.error('❌ Test 1 ERROR:', error);
    return false;
  }
}

// Test 2: Image Display When Loading Saved Analysis
async function testImageDisplayOnLoad() {
  console.log('🖼️ Test 2: Image Display When Loading Saved Analysis');
  
  try {
    // Navigate to history tab
    const historyTab = await findElement('[value="history"]');
    historyTab.click();
    await wait(TEST_CONFIG.waitTime);
    
    // Find first analysis card
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], [class*="AnalysisCard"]');
    if (analysisCards.length === 0) {
      console.log('⚠️ No saved analyses found to test');
      return false;
    }
    
    const firstCard = analysisCards[0];
    console.log('📋 Found analysis card, checking for load button...');
    
    // Find and click load button
    const loadButton = firstCard.querySelector('button:contains("Cargar"), button[class*="load"]');
    if (!loadButton) {
      console.log('❌ Load button not found');
      return false;
    }
    
    loadButton.click();
    console.log('🔄 Clicked load button');
    await wait(TEST_CONFIG.waitTime * 2); // Wait longer for loading
    
    // Check if we're on analyze tab
    const analyzeTab = document.querySelector('[value="analyze"]');
    if (analyzeTab && analyzeTab.getAttribute('data-state') === 'active') {
      console.log('✅ Switched to analyze tab');
      
      // Check if image preview is displayed
      const imagePreview = document.querySelector('img[src*="http"], img[src*="blob:"], img[src*="data:"]');
      if (imagePreview && imagePreview.src) {
        console.log('✅ Test 2 PASSED: Image preview is displayed');
        console.log('🖼️ Image URL:', imagePreview.src.substring(0, 100) + '...');
        return true;
      } else {
        console.log('❌ Test 2 FAILED: No image preview found');
        return false;
      }
    } else {
      console.log('❌ Test 2 FAILED: Did not switch to analyze tab');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test 2 ERROR:', error);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Visual Complexity Analyzer fixes verification...');
  
  const results = {
    historyAutoRefresh: false,
    imageDisplayOnLoad: false
  };
  
  // Run Test 1: History Auto-Refresh
  console.log('\n' + '='.repeat(50));
  results.historyAutoRefresh = await testHistoryAutoRefresh();
  
  // Run Test 2: Image Display on Load
  console.log('\n' + '='.repeat(50));
  results.imageDisplayOnLoad = await testImageDisplayOnLoad();
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log('='.repeat(50));
  console.log(`📋 History Auto-Refresh: ${results.historyAutoRefresh ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🖼️ Image Display on Load: ${results.imageDisplayOnLoad ? '✅ PASSED' : '❌ FAILED'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! The fixes are working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
  
  return results;
}

// Auto-run if script is executed directly
if (typeof window !== 'undefined') {
  console.log('🔧 Visual Complexity Analyzer Test Script Loaded');
  console.log('📝 Run runTests() to start the verification process');
  
  // Make functions available globally for manual testing
  window.testVCA = {
    runTests,
    testHistoryAutoRefresh,
    testImageDisplayOnLoad
  };
}
