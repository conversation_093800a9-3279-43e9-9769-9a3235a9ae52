/**
 * Custom hook for user palette operations with Supabase integration
 * Follows the same pattern as other tool hooks in the application
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/use-auth'
import { api } from '@/lib/api'
import type { 
  UserPalette, 
  CreateUserPaletteData, 
  UpdateUserPaletteData,
  PaletteApiResponse,
  PalettesListApiResponse
} from '@/lib/supabase'

export interface UsePalettesOptions {
  limit?: number
  offset?: number
  is_favorite?: boolean
}

export function usePalettes(options: UsePalettesOptions = {}) {
  const { toast } = useToast()
  const { user } = useAuth()
  const queryClient = useQueryClient()
  
  const { limit = 50, offset = 0, is_favorite } = options

  // Query for listing user palettes
  const {
    data: palettesData,
    isLoading: isLoadingPalettes,
    error: palettesError,
    refetch: refetchPalettes
  } = useQuery({
    queryKey: ['palettes', 'list', { limit, offset, is_favorite }],
    queryFn: async () => {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      })
      
      if (is_favorite !== undefined) {
        params.append('is_favorite', is_favorite.toString())
      }
      
      const response = await api.get<PalettesListApiResponse>(`/api/palettes?${params}`)
      return response.data
    },
    enabled: !!user,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  // Mutation for creating a palette
  const createPaletteMutation = useMutation({
    mutationFn: async (data: CreateUserPaletteData) => {
      const response = await api.post<PaletteApiResponse>('/api/palettes', data)
      return response.data
    },
    onSuccess: (response) => {
      toast({
        title: "Paleta creada",
        description: `La paleta "${response.palette?.name}" ha sido creada exitosamente.`,
      })
      
      // Invalidate and refetch queries
      queryClient.invalidateQueries({ queryKey: ['palettes'] })
      
      return response.palette
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || error.message || "Ocurrió un error inesperado."
      toast({
        title: "Error al crear paleta",
        description: errorMessage,
        variant: "destructive",
      })
    },
  })

  // Mutation for updating a palette
  const updatePaletteMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUserPaletteData }) => {
      const response = await api.put<PaletteApiResponse>(`/api/palettes/${id}`, data)
      return response.data
    },
    onSuccess: (response, variables) => {
      toast({
        title: "Paleta actualizada",
        description: `La paleta "${response.palette?.name}" ha sido actualizada exitosamente.`,
      })
      
      // Invalidate and refetch queries
      queryClient.invalidateQueries({ queryKey: ['palette', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['palettes'] })
      
      return response.palette
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || error.message || "Ocurrió un error inesperado."
      toast({
        title: "Error al actualizar paleta",
        description: errorMessage,
        variant: "destructive",
      })
    },
  })

  // Mutation for deleting a palette
  const deletePaletteMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete<PaletteApiResponse>(`/api/palettes/${id}`)
      return response.data
    },
    onSuccess: () => {
      toast({
        title: "Paleta eliminada",
        description: "La paleta ha sido eliminada exitosamente.",
      })
      
      // Invalidate and refetch queries
      queryClient.invalidateQueries({ queryKey: ['palettes'] })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || error.message || "Ocurrió un error inesperado."
      toast({
        title: "Error al eliminar paleta",
        description: errorMessage,
        variant: "destructive",
      })
    },
  })

  // Mutation for toggling favorite status
  const toggleFavoriteMutation = useMutation({
    mutationFn: async ({ id, is_favorite }: { id: string; is_favorite: boolean }) => {
      const response = await api.patch<PaletteApiResponse>(`/api/palettes/${id}/favorite?is_favorite=${is_favorite}`)
      return response.data
    },
    onSuccess: (response, variables) => {
      const action = variables.is_favorite ? "agregada a" : "removida de"
      toast({
        title: `Paleta ${action} favoritos`,
        description: `La paleta ha sido ${action} tus favoritos.`,
      })
      
      // Invalidate and refetch queries
      queryClient.invalidateQueries({ queryKey: ['palette', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['palettes'] })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || error.message || "Ocurrió un error inesperado."
      toast({
        title: "Error al actualizar favoritos",
        description: errorMessage,
        variant: "destructive",
      })
    },
  })

  return {
    // Data
    palettes: palettesData?.palettes || [],
    palettesCount: palettesData?.count || 0,
    
    // Loading states
    isLoadingPalettes,
    isCreating: createPaletteMutation.isPending,
    isUpdating: updatePaletteMutation.isPending,
    isDeleting: deletePaletteMutation.isPending,
    isTogglingFavorite: toggleFavoriteMutation.isPending,
    
    // Error states
    palettesError,
    
    // Actions
    createPalette: createPaletteMutation.mutateAsync,
    updatePalette: updatePaletteMutation.mutateAsync,
    deletePalette: deletePaletteMutation.mutateAsync,
    toggleFavorite: toggleFavoriteMutation.mutateAsync,
    
    // Refetch functions
    refetchPalettes,
  }
}

export function usePalette(paletteId?: string) {
  const { toast } = useToast()
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Query for getting a specific palette
  const {
    data: paletteData,
    isLoading: isLoadingPalette,
    error: paletteError,
    refetch: refetchPalette
  } = useQuery({
    queryKey: ['palette', paletteId],
    queryFn: async () => {
      if (!paletteId) return null
      const response = await api.get<PaletteApiResponse>(`/api/palettes/${paletteId}`)
      return response.data.palette
    },
    enabled: !!paletteId && !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  return {
    // Data
    palette: paletteData,
    
    // Loading states
    isLoadingPalette,
    
    // Error states
    paletteError,
    
    // Refetch functions
    refetchPalette,
  }
}

// Helper function to validate hex colors
export function validateHexColor(color: string): boolean {
  const hex = color.replace('#', '')
  return /^[0-9A-F]{6}$/i.test(hex) || /^[0-9A-F]{3}$/i.test(hex)
}

// Helper function to ensure color has # prefix
export function normalizeHexColor(color: string): string {
  return color.startsWith('#') ? color : `#${color}`
}

// Helper function to validate palette data
export function validatePaletteData(data: CreateUserPaletteData | UpdateUserPaletteData): string[] {
  const errors: string[] = []
  
  if ('name' in data && data.name !== undefined) {
    if (!data.name || data.name.trim().length === 0) {
      errors.push('El nombre de la paleta es requerido')
    }
    if (data.name.length > 100) {
      errors.push('El nombre de la paleta no puede exceder 100 caracteres')
    }
  }
  
  if ('colors' in data && data.colors !== undefined) {
    if (!data.colors || data.colors.length === 0) {
      errors.push('La paleta debe tener al menos un color')
    }
    if (data.colors && data.colors.length > 20) {
      errors.push('La paleta no puede tener más de 20 colores')
    }
    if (data.colors) {
      data.colors.forEach((color, index) => {
        if (!validateHexColor(color)) {
          errors.push(`Color ${index + 1} no es un código hex válido: ${color}`)
        }
      })
    }
  }
  
  if ('description' in data && data.description !== undefined && data.description && data.description.length > 500) {
    errors.push('La descripción no puede exceder 500 caracteres')
  }
  
  if ('tags' in data && data.tags !== undefined && data.tags && data.tags.length > 10) {
    errors.push('No se pueden tener más de 10 etiquetas')
  }
  
  return errors
}
