"""
Web Analyzer Service
Analyzes websites using Jina AI Reader + Gemini for brand information extraction
"""

import logging
import asyncio
import json
import aiohttp
from typing import Dict, Any, Optional
import google.generativeai as genai
from app.core.config import get_settings

logger = logging.getLogger(__name__)

class WebAnalyzer:
    """Service for analyzing websites and extracting brand information"""
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = getattr(self.settings, 'GEMINI_API_KEY', None)
        
        if not self.api_key:
            raise Exception("GEMINI_API_KEY not configured. Please set your Gemini API key.")

        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        self.jina_base_url = "https://r.jina.ai/"
    
    async def analyze_website(self, url: str) -> Dict[str, Any]:
        """
        Analyze a website and extract brand information
        
        Args:
            url: Website URL to analyze
            
        Returns:
            Brand information extracted from the website
        """
        try:
            # Step 1: Extract content using Jina AI Reader
            logger.info(f"Extracting content from URL: {url}")
            content = await self._extract_content_with_jina(url)
            
            if not content:
                raise Exception("Failed to extract content from the website")
            
            # Step 2: Analyze content with Gemini
            logger.info("Analyzing content with Gemini")
            brand_info = await self._analyze_content_with_gemini(content, url)
            
            return {
                "status": "success",
                "url": url,
                "brand_info": brand_info,
                "extracted_content_length": len(content)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing website {url}: {str(e)}")
            return {
                "status": "error",
                "url": url,
                "error": str(e),
                "brand_info": None
            }
    
    async def _extract_content_with_jina(self, url: str) -> Optional[str]:
        """Extract clean content from URL using Jina AI Reader"""
        try:
            jina_url = f"{self.jina_base_url}{url}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(jina_url, timeout=30) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.info(f"Successfully extracted {len(content)} characters from {url}")
                        return content
                    else:
                        logger.error(f"Jina AI Reader returned status {response.status} for {url}")
                        return None
                        
        except asyncio.TimeoutError:
            logger.error(f"Timeout extracting content from {url}")
            return None
        except Exception as e:
            logger.error(f"Error extracting content with Jina: {str(e)}")
            return None
    
    async def _analyze_content_with_gemini(self, content: str, url: str) -> Dict[str, Any]:
        """Analyze extracted content with Gemini to extract brand information"""
        try:
            prompt = self._build_analysis_prompt(content, url)
            
            response = await asyncio.to_thread(
                self.model.generate_content, 
                prompt
            )
            
            # Parse the JSON response
            analysis_text = response.text.strip()
            
            # Extract JSON from response (in case there's extra text)
            json_start = analysis_text.find('{')
            json_end = analysis_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = analysis_text[json_start:json_end]
                brand_info = json.loads(json_text)
            else:
                # Fallback: try to parse the entire response
                brand_info = json.loads(analysis_text)
            
            return brand_info
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing Gemini JSON response: {str(e)}")
            # Return a default structure if JSON parsing fails
            return self._get_default_brand_info()
        except Exception as e:
            logger.error(f"Error analyzing content with Gemini: {str(e)}")
            return self._get_default_brand_info()
    
    def _build_analysis_prompt(self, content: str, url: str) -> str:
        """Build the analysis prompt for Gemini"""
        return f"""
Analiza el siguiente contenido web y extrae información de marca. Responde ÚNICAMENTE con un JSON válido sin texto adicional.

URL: {url}

CONTENIDO:
{content[:8000]}  # Limit content to avoid token limits

Extrae la siguiente información y responde en formato JSON:

{{
    "business_name": "Nombre del negocio/empresa",
    "industry": "Industria o sector",
    "value_proposition": "Propuesta de valor principal",
    "voice_tone": "Tono de voz (profesional, casual, amigable, etc.)",
    "target_audience": "Audiencia objetivo",
    "brand_colors": ["#color1", "#color2"],
    "services_products": ["servicio1", "servicio2"],
    "key_messages": ["mensaje1", "mensaje2"],
    "confidence_score": 0.85
}}

INSTRUCCIONES:
- Si no encuentras información específica, usa "No detectado" 
- Para brand_colors, sugiere colores basados en el contenido si no están explícitos
- confidence_score debe ser entre 0.0 y 1.0 basado en qué tan clara es la información
- Mantén las respuestas concisas pero informativas
- Responde SOLO con el JSON, sin explicaciones adicionales
"""
    
    def _get_default_brand_info(self) -> Dict[str, Any]:
        """Return default brand info structure when analysis fails"""
        return {
            "business_name": "No detectado",
            "industry": "No detectado", 
            "value_proposition": "No detectado",
            "voice_tone": "profesional",
            "target_audience": "No detectado",
            "brand_colors": ["#3018ef", "#dd3a5a"],  # Emma's default colors
            "services_products": [],
            "key_messages": [],
            "confidence_score": 0.0
        }

# Singleton instance
web_analyzer = WebAnalyzer()
