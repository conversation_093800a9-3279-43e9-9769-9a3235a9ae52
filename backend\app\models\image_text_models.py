"""
Data models for image generation with text using OpenAI gpt-image-1.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Any, Dict
from datetime import datetime


class ImageTextGenerationRequest(BaseModel):
    """Request model for image generation with text."""
    prompt: str = Field(..., description="Description of the image to generate")


class ImageTextReferenceRequest(BaseModel):
    """Request model for image generation with references."""
    prompt: str = Field(..., description="Description of the image to generate")
    # reference_images will be handled as UploadFile in the endpoint


class ImageTextMaskRequest(BaseModel):
    """Request model for image mask editing."""
    prompt: str = Field(..., description="Description of the changes to make")
    # image and mask will be handled as UploadFile in the endpoint


class ImageTextMultiTurnRequest(BaseModel):
    """Request model for multi-turn image editing."""
    previous_response_id: str = Field(..., description="ID of the previous response to build upon")
    edit_prompt: str = Field(..., description="Description of the changes to make")


class ServiceImageTextResponse(BaseModel):
    """Internal service response model for image text operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    image_url: Optional[str] = Field(None, description="URL of the generated image")
    images: Optional[List[str]] = Field(None, description="List of generated image URLs")
    response_id: Optional[str] = Field(None, description="Unique response ID for multi-turn editing")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    
    # Timing and performance metrics
    generation_time: Optional[float] = Field(None, description="Time taken to generate the image")
    model_used: Optional[str] = Field(None, description="Model used for generation")
    
    # Request tracking
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    timestamp: Optional[datetime] = Field(None, description="Timestamp of the response")


class FrontendImageTextResponse(BaseModel):
    """Frontend response model for image text operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    image_url: Optional[str] = Field(None, description="URL of the generated image")
    images: Optional[List[str]] = Field(None, description="List of generated image URLs")
    response_id: Optional[str] = Field(None, description="Unique response ID for multi-turn editing")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    
    @classmethod
    def from_service_response(cls, service_response: ServiceImageTextResponse) -> "FrontendImageTextResponse":
        """Convert service response to frontend response."""
        return cls(
            success=service_response.success,
            image_url=service_response.image_url,
            images=service_response.images,
            response_id=service_response.response_id,
            metadata=service_response.metadata,
            error=service_response.error
        )


class ImageTextGenerationOptions(BaseModel):
    """Options for image generation with text."""
    prompt: str = Field(..., description="Description of the image to generate")
    
    # Advanced options
    response_format: str = Field(default="b64_json", description="Response format (url, b64_json)")
    user: Optional[str] = Field(None, description="User identifier for tracking")


class ImageTextEditOptions(BaseModel):
    """Options for image editing with text."""
    prompt: str = Field(..., description="Description of the desired changes")
    response_format: str = Field(default="b64_json", description="Response format (url, b64_json)")
    user: Optional[str] = Field(None, description="User identifier for tracking")


class ImageTextMetadata(BaseModel):
    """Metadata for image text operations."""
    model: str = Field(default="gpt-image-1", description="Model used for generation")
    generation_time: Optional[float] = Field(None, description="Time taken to generate")
    
    # Request tracking
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    
    # Content safety
    content_filter_results: Optional[Dict[str, Any]] = Field(None, description="Content filter results")
    safety_ratings: Optional[Dict[str, Any]] = Field(None, description="Safety ratings")


class ImageTextError(BaseModel):
    """Error model for image text operations."""
    error_type: str = Field(..., description="Type of error")
    error_code: Optional[str] = Field(None, description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request ID that caused the error")


class ImageTextStats(BaseModel):
    """Statistics for image text operations."""
    total_requests: int = Field(default=0, description="Total number of requests")
    successful_requests: int = Field(default=0, description="Number of successful requests")
    failed_requests: int = Field(default=0, description="Number of failed requests")
    average_generation_time: Optional[float] = Field(None, description="Average generation time")
    total_images_generated: int = Field(default=0, description="Total images generated")
    
    # Usage metrics
    total_cost: Optional[float] = Field(None, description="Total cost in USD")
    
    # Time period
    period_start: Optional[datetime] = Field(None, description="Start of the statistics period")
    period_end: Optional[datetime] = Field(None, description="End of the statistics period")


class ImageTextSessionData(BaseModel):
    """Session data for multi-turn editing."""
    original_prompt: str = Field(..., description="Original prompt used")
    image_b64: str = Field(..., description="Base64 encoded image data")
    image_generation_call_id: Optional[str] = Field(None, description="ID for multi-turn editing")
    timestamp: datetime = Field(..., description="Session creation timestamp")
    request_id: str = Field(..., description="Original request ID")
    edit_history: List[str] = Field(default_factory=list, description="History of edit prompts")


class ImageTextValidation(BaseModel):
    """Validation results for image text operations."""
    is_valid: bool = Field(..., description="Whether the input is valid")
    errors: List[str] = Field(default_factory=list, description="List of validation errors")
    warnings: List[str] = Field(default_factory=list, description="List of validation warnings")
    
    # File validation
    file_size_valid: Optional[bool] = Field(None, description="Whether file size is valid")
    file_type_valid: Optional[bool] = Field(None, description="Whether file type is valid")
    image_dimensions_valid: Optional[bool] = Field(None, description="Whether image dimensions are valid")
    
    # Prompt validation
    prompt_length_valid: Optional[bool] = Field(None, description="Whether prompt length is valid")
    prompt_content_safe: Optional[bool] = Field(None, description="Whether prompt content is safe")


class ImageTextUsage(BaseModel):
    """Usage tracking for image text operations."""
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    operation_type: str = Field(..., description="Type of operation (generate, edit, multi_turn)")
    model_used: str = Field(..., description="Model used for the operation")
    
    # Timing
    start_time: datetime = Field(..., description="Operation start time")
    end_time: Optional[datetime] = Field(None, description="Operation end time")
    duration: Optional[float] = Field(None, description="Operation duration in seconds")
    
    # Resources
    prompt_tokens: Optional[int] = Field(None, description="Number of prompt tokens used")
    image_count: int = Field(default=1, description="Number of images generated")
    
    # Status
    success: bool = Field(..., description="Whether the operation was successful")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class ImageTextConfig(BaseModel):
    """Configuration for image text operations."""
    max_prompt_length: int = Field(default=4000, description="Maximum prompt length")
    max_file_size: int = Field(default=10 * 1024 * 1024, description="Maximum file size in bytes")
    allowed_file_types: List[str] = Field(
        default=["image/jpeg", "image/jpg", "image/png", "image/webp"],
        description="Allowed file types"
    )
    max_reference_images: int = Field(default=4, description="Maximum number of reference images")
    
    # API settings
    request_timeout: int = Field(default=60, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum number of retries")
    
    # Safety settings
    content_filter_enabled: bool = Field(default=True, description="Whether content filtering is enabled")
    safety_check_enabled: bool = Field(default=True, description="Whether safety checking is enabled")


class ImageTextBatch(BaseModel):
    """Batch processing for multiple image text operations."""
    batch_id: str = Field(..., description="Unique batch identifier")
    requests: List[ImageTextGenerationRequest] = Field(..., description="List of generation requests")
    
    # Status
    status: str = Field(default="pending", description="Batch status (pending, processing, completed, failed)")
    created_at: datetime = Field(default_factory=datetime.now, description="Batch creation time")
    started_at: Optional[datetime] = Field(None, description="Batch start time")
    completed_at: Optional[datetime] = Field(None, description="Batch completion time")
    
    # Results
    total_requests: int = Field(..., description="Total number of requests in batch")
    completed_requests: int = Field(default=0, description="Number of completed requests")
    failed_requests: int = Field(default=0, description="Number of failed requests")
    results: List[FrontendImageTextResponse] = Field(default_factory=list, description="Batch results")
