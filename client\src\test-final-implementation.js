// Final implementation test for Visual Complexity Analyzer
// Run this script in the browser console to verify all functionality

console.log('🚀 Testing Visual Complexity Analyzer - Final Implementation');

// Test 1: Verify component is loaded
function testComponentLoaded() {
  console.log('\n📋 Test 1: Component Loading');
  
  const debugIndicator = document.querySelector('.fixed.top-4.right-4.bg-green-500');
  const title = document.querySelector('h1');
  
  if (debugIndicator) {
    console.log('✅ Debug indicator found:', debugIndicator.textContent);
  } else {
    console.log('❌ Debug indicator not found');
  }
  
  if (title && title.textContent.includes('Analizador de Complejidad Visual')) {
    console.log('✅ Main title found');
  } else {
    console.log('❌ Main title not found');
  }
  
  return debugIndicator && title;
}

// Test 2: Verify all 4 tabs are present
function testTabsPresent() {
  console.log('\n📋 Test 2: Tab Structure');
  
  const tabs = document.querySelectorAll('[role="tablist"] button');
  const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
  
  console.log('Tabs found:', tabTexts);
  
  const expectedTabs = [
    'Análisis de Complejidad',
    'Resultados Detallados', 
    'Historial',
    'Favoritos'
  ];
  
  const allTabsPresent = expectedTabs.every(expectedTab => 
    tabTexts.some(tabText => tabText.includes(expectedTab.split(' ')[0]))
  );
  
  if (allTabsPresent) {
    console.log('✅ All 4 tabs are present');
  } else {
    console.log('❌ Some tabs are missing');
    console.log('Expected:', expectedTabs);
    console.log('Found:', tabTexts);
  }
  
  return allTabsPresent;
}

// Test 3: Verify tab counters
function testTabCounters() {
  console.log('\n📋 Test 3: Tab Counters');
  
  const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
    .find(tab => tab.textContent.includes('Historial'));
  const favoritesTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
    .find(tab => tab.textContent.includes('Favoritos'));
  
  if (historyTab) {
    const historyCount = historyTab.textContent.match(/\((\d+)\)/);
    console.log('✅ History tab counter:', historyCount ? historyCount[1] : 'not found');
  } else {
    console.log('❌ History tab not found');
  }
  
  if (favoritesTab) {
    const favoritesCount = favoritesTab.textContent.match(/\((\d+)\)/);
    console.log('✅ Favorites tab counter:', favoritesCount ? favoritesCount[1] : 'not found');
  } else {
    console.log('❌ Favorites tab not found');
  }
  
  return historyTab && favoritesTab;
}

// Test 4: Test tab navigation
function testTabNavigation() {
  console.log('\n📋 Test 4: Tab Navigation');
  
  const tabs = {
    analyze: document.querySelector('[value="analyze"]'),
    history: document.querySelector('[value="history"]'),
    favorites: document.querySelector('[value="favorites"]'),
    theory: document.querySelector('[value="theory"]')
  };
  
  console.log('Tab elements found:', Object.keys(tabs).filter(key => tabs[key]));
  
  // Test navigation to history tab
  if (tabs.history && !tabs.history.disabled) {
    console.log('🔄 Navigating to History tab...');
    tabs.history.click();
    
    setTimeout(() => {
      const historyContent = document.querySelector('[value="history"]');
      if (historyContent) {
        console.log('✅ History tab navigation successful');
      } else {
        console.log('❌ History tab navigation failed');
      }
    }, 500);
  } else {
    console.log('❌ History tab is disabled or not found');
  }
  
  // Test navigation to favorites tab
  setTimeout(() => {
    if (tabs.favorites && !tabs.favorites.disabled) {
      console.log('🔄 Navigating to Favorites tab...');
      tabs.favorites.click();
      
      setTimeout(() => {
        const favoritesContent = document.querySelector('[value="favorites"]');
        if (favoritesContent) {
          console.log('✅ Favorites tab navigation successful');
        } else {
          console.log('❌ Favorites tab navigation failed');
        }
      }, 500);
    } else {
      console.log('❌ Favorites tab is disabled or not found');
    }
  }, 1000);
  
  // Return to analyze tab
  setTimeout(() => {
    if (tabs.analyze) {
      console.log('🔄 Returning to Analyze tab...');
      tabs.analyze.click();
      console.log('✅ Returned to main tab');
    }
  }, 2000);
  
  return true;
}

// Test 5: Test mock data loading
function testMockDataLoading() {
  console.log('\n📋 Test 5: Mock Data Loading');
  
  const historyTab = document.querySelector('[value="history"]');
  if (historyTab && !historyTab.disabled) {
    historyTab.click();
    
    setTimeout(() => {
      const analysisCards = document.querySelectorAll('.grid.gap-4 > *');
      console.log('Analysis cards found:', analysisCards.length);
      
      if (analysisCards.length > 0) {
        console.log('✅ Mock data is loading correctly');
        
        // Check card content
        analysisCards.forEach((card, index) => {
          const title = card.querySelector('h3');
          const filename = card.querySelector('.text-gray-600');
          const favoriteButton = card.querySelector('button[class*="Heart"], button svg[class*="heart"]');
          const loadButton = card.querySelector('button:contains("Cargar"), button[class*="Cargar"]');
          
          console.log(`📄 Card ${index + 1}:`, {
            title: title?.textContent?.trim(),
            filename: filename?.textContent?.trim(),
            hasFavoriteButton: !!favoriteButton,
            hasLoadButton: !!loadButton
          });
        });
      } else {
        console.log('❌ No mock data found');
      }
    }, 1000);
  } else {
    console.log('❌ Cannot test mock data - history tab disabled');
  }
  
  return true;
}

// Test 6: Test button functionality
function testButtonFunctionality() {
  console.log('\n📋 Test 6: Button Functionality');
  
  const historyTab = document.querySelector('[value="history"]');
  if (historyTab && !historyTab.disabled) {
    historyTab.click();
    
    setTimeout(() => {
      const favoriteButtons = document.querySelectorAll('button');
      const heartButtons = Array.from(favoriteButtons).filter(btn => 
        btn.innerHTML.includes('Heart') || btn.innerHTML.includes('heart')
      );
      
      console.log('Heart/Favorite buttons found:', heartButtons.length);
      
      if (heartButtons.length > 0) {
        console.log('🔄 Testing favorite button click...');
        heartButtons[0].click();
        console.log('✅ Favorite button clicked successfully');
      }
      
      const loadButtons = Array.from(favoriteButtons).filter(btn => 
        btn.textContent.includes('Cargar')
      );
      
      console.log('Load buttons found:', loadButtons.length);
      
      if (loadButtons.length > 0) {
        console.log('🔄 Testing load button click...');
        loadButtons[0].click();
        console.log('✅ Load button clicked successfully');
      }
    }, 1000);
  }
  
  return true;
}

// Run all tests
function runAllTests() {
  console.log('🧪 Running comprehensive test suite...\n');
  
  const results = {
    componentLoaded: testComponentLoaded(),
    tabsPresent: false,
    tabCounters: false,
    tabNavigation: false,
    mockData: false,
    buttonFunctionality: false
  };
  
  setTimeout(() => {
    results.tabsPresent = testTabsPresent();
    results.tabCounters = testTabCounters();
  }, 500);
  
  setTimeout(() => {
    results.tabNavigation = testTabNavigation();
  }, 1000);
  
  setTimeout(() => {
    results.mockData = testMockDataLoading();
  }, 3000);
  
  setTimeout(() => {
    results.buttonFunctionality = testButtonFunctionality();
  }, 5000);
  
  // Final summary
  setTimeout(() => {
    console.log('\n📊 Test Results Summary:');
    console.log('✅ Component Loaded:', results.componentLoaded);
    console.log('✅ Tabs Present:', results.tabsPresent);
    console.log('✅ Tab Counters:', results.tabCounters);
    console.log('✅ Tab Navigation:', results.tabNavigation);
    console.log('✅ Mock Data:', results.mockData);
    console.log('✅ Button Functionality:', results.buttonFunctionality);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! Visual Complexity Analyzer is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Check the details above.');
    }
  }, 7000);
}

// Auto-run tests
runAllTests();

// Export functions for manual testing
window.visualComplexityTests = {
  runAllTests,
  testComponentLoaded,
  testTabsPresent,
  testTabCounters,
  testTabNavigation,
  testMockDataLoading,
  testButtonFunctionality
};

console.log('\n📝 Manual test functions available in window.visualComplexityTests');
console.log('Use window.visualComplexityTests.runAllTests() to run all tests again');
