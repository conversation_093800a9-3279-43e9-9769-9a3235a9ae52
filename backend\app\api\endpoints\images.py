import logging
from typing import Optional

from app.schemas.image import ImageGenerationRequest as GenerateImageRequest, FrontendImageResponse, FrontendBackgroundRemovalResponse, BackgroundRemovalResponse, StyleReferenceRequest, StyleReferenceResponse, FrontendStyleReferenceResponse, GPTImageGenerationRequest, GPTImageGenerationResponse
from app.schemas.style_transfer import StyleTransferRequest, StyleTransferResponse
from app.services.image_service import generate_image
from app.services.style_transfer_service import call_stability_style_transfer
from app.services.gpt_image_service import generate_gpt_image
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File
from typing import Optional
import httpx
import io
from app.core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

# Router separado para background removal (compatibilidad con frontend)
bg_removal_router = APIRouter()


async def call_stability_remove_bg(image_file_content: bytes, output_format: str = "webp") -> BackgroundRemovalResponse:
    """
    Remove background from an image using Stability AI v2beta API.
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para background removal
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/edit/remove-background"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        # Preparar FormData
        form_data = {
            "output_format": output_format
        }

        # Preparar el archivo para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_file_content), "image/jpeg")
        }

        logger.info(f"Removing background from image using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Output format: {output_format}")

        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return BackgroundRemovalResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for background removal: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error removing background: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error removing background: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e


async def call_stability_upscale_conservative(image_file_content: bytes, prompt: str, creativity: float = 0.3, output_format: str = "webp") -> BackgroundRemovalResponse:
    """
    Upscale image using Stability AI v2beta Conservative Upscale API.
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para conservative upscale
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/conservative"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        # Preparar FormData
        form_data = {
            "prompt": prompt,
            "creativity": str(creativity),
            "output_format": output_format
        }

        # Preparar el archivo para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_file_content), "image/jpeg")
        }

        logger.info(f"Upscaling image using Stability AI v2beta Conservative Upscale")
        logger.info(f"URL: {url}")
        logger.info(f"Prompt: {prompt}")
        logger.info(f"Creativity: {creativity}")
        logger.info(f"Output format: {output_format}")

        async with httpx.AsyncClient(timeout=180.0) as client:  # 3 minutos timeout para upscale
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return BackgroundRemovalResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for upscale: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error upscaling image: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error upscaling image: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e


async def call_stability_upscale_fast(image_file_content: bytes, output_format: str = "webp") -> BackgroundRemovalResponse:
    """
    Fast upscale image using Stability AI v2beta Fast Upscale API.
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para fast upscale
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/fast"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        # Preparar FormData (Fast upscale solo necesita imagen y formato)
        form_data = {
            "output_format": output_format
        }

        # Preparar el archivo para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_file_content), "image/jpeg")
        }

        logger.info(f"Fast upscaling image using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Output format: {output_format}")

        async with httpx.AsyncClient(timeout=60.0) as client:  # 1 minuto timeout para fast upscale
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return BackgroundRemovalResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for fast upscale: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error fast upscaling image: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error fast upscaling image: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e


async def call_stability_upscale_creative_start(image_file_content: bytes, prompt: str, creativity: float = 0.3, output_format: str = "webp", style_preset: str = None) -> dict:
    """
    Start creative upscale using Stability AI v2beta Creative Upscale API (async).
    Returns generation ID for polling.
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para creative upscale
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/creative"

        # Headers
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}"
        }

        # Preparar FormData
        form_data = {
            "prompt": prompt,
            "output_format": output_format,
            "creativity": str(creativity)
        }

        if style_preset:
            form_data["style_preset"] = style_preset

        # Preparar el archivo para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_file_content), "image/jpeg")
        }

        logger.info(f"Starting creative upscale using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Prompt: {prompt}")
        logger.info(f"Creativity: {creativity}")

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON que contiene el generation ID
            result = response.json()
            generation_id = result.get("id")

            if not generation_id:
                raise ValueError("No generation ID in response")

            return {"generation_id": generation_id}

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for creative upscale: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error starting creative upscale: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error starting creative upscale: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e


async def call_stability_upscale_creative_result(generation_id: str) -> BackgroundRemovalResponse:
    """
    Get result of creative upscale using generation ID.
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para obtener resultado
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/creative/result/{generation_id}"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        logger.info(f"Getting creative upscale result for ID: {generation_id}")
        logger.info(f"URL: {url}")

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(url, headers=headers)

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code == 202:
                # Still processing
                raise HTTPException(status_code=202, detail="Generation still in progress")
            elif response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return BackgroundRemovalResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except HTTPException:
        # Re-raise HTTP exceptions (including 202)
        raise
    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for creative upscale result: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error getting creative upscale result: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error getting creative upscale result: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e


def translate_prompt_to_english(spanish_prompt: str) -> str:
    """
    Traduce prompts del español al inglés para Stability AI.
    Stability AI solo acepta prompts en inglés.
    """
    if not spanish_prompt or spanish_prompt.strip() == '':
        return spanish_prompt

    # Diccionario básico de traducciones comunes para prompts de IA
    translations = {
        # Personas y características
        'mujer': 'woman',
        'hombre': 'man',
        'niño': 'child',
        'niña': 'girl',
        'persona': 'person',
        'retrato': 'portrait',
        'cara': 'face',
        'cabello': 'hair',
        'ojos': 'eyes',
        'sonrisa': 'smile',
        'joven': 'young',
        'viejo': 'old',
        'hermoso': 'beautiful',
        'hermosa': 'beautiful',
        'elegante': 'elegant',

        # Lugares y ambientes
        'playa': 'beach',
        'montaña': 'mountain',
        'bosque': 'forest',
        'ciudad': 'city',
        'campo': 'countryside',
        'jardín': 'garden',
        'parque': 'park',
        'casa': 'house',
        'oficina': 'office',
        'estudio': 'studio',
        'calle': 'street',
        'noche': 'night',
        'día': 'day',

        # Colores
        'rojo': 'red',
        'azul': 'blue',
        'verde': 'green',
        'amarillo': 'yellow',
        'negro': 'black',
        'blanco': 'white',
        'gris': 'gray',
        'rosa': 'pink',
        'morado': 'purple',
        'naranja': 'orange',
        'marrón': 'brown',

        # Estilos artísticos
        'steampunk': 'steampunk',
        'cyberpunk': 'cyberpunk',
        'vintage': 'vintage',
        'moderno': 'modern',
        'clásico': 'classic',
        'artístico': 'artistic',
        'realista': 'realistic',
        'abstracto': 'abstract',
        'minimalista': 'minimalist',

        # Palabras de conexión
        'con': 'with',
        'sin': 'without',
        'en': 'in',
        'de': 'of',
        'del': 'of the',
        'la': 'the',
        'el': 'the',
        'una': 'a',
        'un': 'a',
        'y': 'and',
        'o': 'or',
        'pero': 'but',
        'estilo': 'style',
        'imagen': 'image',
    }

    translated_prompt = spanish_prompt.lower()

    # Aplicar traducciones palabra por palabra
    import re
    for spanish_word, english_word in translations.items():
        # Usar word boundaries para evitar traducciones parciales
        pattern = r'\b' + re.escape(spanish_word) + r'\b'
        translated_prompt = re.sub(pattern, english_word, translated_prompt, flags=re.IGNORECASE)

    logger.info(f"🌐 Prompt traducido: '{spanish_prompt}' → '{translated_prompt}'")
    return translated_prompt


async def call_stability_style_reference(image_file_content: bytes, prompt: str, negative_prompt: str = None, aspect_ratio: str = "1:1", fidelity: float = 0.5, seed: int = 0, output_format: str = "png", style_preset: str = None) -> StyleReferenceResponse:
    """
    Apply style reference using Stability AI v2beta API.
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Traducir prompts al inglés (Stability AI solo acepta inglés)
        translated_prompt = translate_prompt_to_english(prompt)
        translated_negative_prompt = translate_prompt_to_english(negative_prompt) if negative_prompt else None

        # URL de la API v2beta para style reference
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/control/style"

        # Headers para recibir respuesta JSON con base64
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
            "Accept": "application/json"
        }

        # Preparar FormData
        form_data = {
            "prompt": translated_prompt,
            "aspect_ratio": aspect_ratio,
            "fidelity": str(fidelity),
            "seed": str(seed),
            "output_format": output_format
        }

        # Agregar campos opcionales
        if translated_negative_prompt:
            form_data["negative_prompt"] = translated_negative_prompt
        if style_preset:
            form_data["style_preset"] = style_preset

        # Preparar el archivo para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_file_content), "image/jpeg")
        }

        logger.info(f"🎨 NUEVA VERSIÓN: Applying style reference using Stability AI v2beta")
        logger.info(f"🌐 URL: {url}")
        logger.info(f"🇪🇸 Original prompt: {prompt}")
        logger.info(f"🇺🇸 Translated prompt: {translated_prompt}")
        logger.info(f"🎯 Fidelity: {fidelity}")
        logger.info(f"📐 Aspect ratio: {aspect_ratio}")

        async with httpx.AsyncClient(timeout=180.0) as client:  # 3 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return StyleReferenceResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for style reference: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error applying style reference: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error applying style reference: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e


async def verify_api_key():
    # This logic should be moved to a reusable dependency in core/dependencies.py
    # For now, keeping it simple for refactoring focus
    # In development mode, skip API key verification
    logger.debug("API Key verification skipped in development mode")
    return  # Allow access in development mode


@router.post(
    "/generate",
    response_model=FrontendImageResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_image_endpoint(
    prompt: str = Form(..., description="The prompt to generate the image from"),
    negative_prompt: Optional[str] = Form(None, description="What you do NOT want to see"),
    aspect_ratio: Optional[str] = Form("1:1", description="Aspect ratio of the image"),
    output_format: Optional[str] = Form("webp", description="Output format"),
    style_preset: Optional[str] = Form(None, description="Style preset"),
    seed: Optional[int] = Form(None, description="Seed for reproducibility"),
    model: Optional[str] = Form("ultra", description="Model to use"),
    # Legacy parameters for backward compatibility
    width: Optional[int] = Form(1024, description="Width (legacy)"),
    height: Optional[int] = Form(1024, description="Height (legacy)"),
    cfg_scale: Optional[float] = Form(7.0, description="CFG scale (legacy)"),
    steps: Optional[int] = Form(50, description="Steps (legacy)"),
    samples: Optional[int] = Form(1, description="Samples (legacy)")
) -> FrontendImageResponse:
    """Generates an image based on a prompt using the Stability AI v2beta service."""
    logger.info(f"Received image generation request for prompt: '{prompt[:50]}...'")

    try:
        # Create request object from form data
        req = GenerateImageRequest(
            prompt=prompt,
            negative_prompt=negative_prompt,
            aspect_ratio=aspect_ratio,
            output_format=output_format,
            style_preset=style_preset,
            seed=seed,
            model=model,
            width=width,
            height=height,
            cfg_scale=cfg_scale,
            steps=steps,
            samples=samples
        )

        # Call the service function
        service_response = await generate_image(req)

        # The service should ideally return data matching GenerateImageResponse
        # or raise specific exceptions handled below or by global handlers.
        # If the service indicates an error condition not raised as an exception:
        if isinstance(service_response, dict) and service_response.get("error"):
            logger.error(f"Error from image service: {service_response['error']}")
            raise HTTPException(status_code=500, detail=service_response["error"])

        # Convert ImageGenerationResponse to the format expected by frontend
        logger.info(f"Service response type: {type(service_response)}")
        logger.info(f"Service response has image: {hasattr(service_response, 'image')}")

        if hasattr(service_response, 'image') and service_response.image:
            # Convert base64 image to data URL for frontend
            image_data_url = f"data:image/webp;base64,{service_response.image}"
            logger.info(f"Generated data URL length: {len(image_data_url)}")

            # Return in the format expected by frontend using Pydantic model
            response = FrontendImageResponse(
                success=True,
                images=[image_data_url],
                tipo="sincrono",
                metadata=service_response.metadata if hasattr(service_response, 'metadata') else {}
            )
            logger.info("Returning successful response to frontend")
            return response
        else:
            # If no image, return error
            error_msg = service_response.error if hasattr(service_response, 'error') else "No image generated"
            logger.error(f"No image in service response: {error_msg}")
            return FrontendImageResponse(
                success=False,
                error=error_msg
            )

    except HTTPException as http_exc:  # Re-raise HTTP exceptions from service or here
        raise http_exc
    except Exception as e:
        logger.error(
            f"Unexpected error in /images/generate endpoint: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image generation: {e}",
        )


@router.post(
    "/remove-background",
    response_model=FrontendBackgroundRemovalResponse,
    dependencies=[Depends(verify_api_key)],
)
async def remove_background_endpoint(
    image: UploadFile = File(..., description="Image file to remove background from"),
    output_format: Optional[str] = Form("webp", description="Output format (png or webp)")
) -> FrontendBackgroundRemovalResponse:
    """Remove background from an image using Stability AI v2beta API."""
    logger.info(f"Received background removal request for file: {image.filename}")

    try:
        # Validar formato de archivo
        if not image.content_type or not image.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail="File must be an image (JPEG, PNG, or WebP)"
            )

        # Validar formatos soportados
        supported_formats = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
        if image.content_type not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported image format: {image.content_type}. Supported: JPEG, PNG, WebP"
            )

        # Validar tamaño de archivo (máximo 10MB según documentación de Stability AI)
        max_size = 10 * 1024 * 1024  # 10MB
        image_content = await image.read()

        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: 10MB, received: {len(image_content) / 1024 / 1024:.2f}MB"
            )

        # Validar formato de salida
        if output_format not in ["png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="Output format must be 'png' or 'webp'"
            )

        logger.info(f"Processing image: {image.filename}, size: {len(image_content)} bytes, output: {output_format}")

        # Llamar directamente a la API de Stability AI
        service_response = await call_stability_remove_bg(image_content, output_format)

        if service_response.image:
            # Crear data URL para el frontend
            mime_type = f"image/{output_format}"
            image_data_url = f"data:{mime_type};base64,{service_response.image}"

            logger.info(f"Background removal successful, result size: {len(image_data_url)} chars")

            return FrontendBackgroundRemovalResponse(
                success=True,
                url=image_data_url,
                metadata={
                    "original_filename": image.filename,
                    "output_format": output_format,
                    "finish_reason": service_response.finish_reason,
                    "seed": service_response.seed
                }
            )
        else:
            logger.error("No image data in service response")
            return FrontendBackgroundRemovalResponse(
                success=False,
                error="No image data received from Stability AI"
            )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error in background removal: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during background removal: {str(e)}"
        )


# Endpoint para background removal (compatibilidad con frontend)
@bg_removal_router.post(
    "/remove",
    response_model=FrontendBackgroundRemovalResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Remove background from image",
    description="Remove background from an uploaded image using Stability AI's v2beta API."
)
async def remove_background_endpoint_legacy(
    image: UploadFile = File(..., description="Image file to remove background from"),
    output_format: Optional[str] = Form("webp", description="Output format (png or webp)")
) -> FrontendBackgroundRemovalResponse:
    """
    Remove background from an image using Stability AI v2beta API.

    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Output Formats:** PNG, WebP
    **Cost:** 2 credits per successful generation

    **Parameters:**
    - **image**: The image file to process
    - **output_format**: Output format ("png" or "webp", default: "webp")
    """
    return await remove_background_endpoint(image, output_format)


@router.post(
    "/enhance-image",
    response_model=FrontendBackgroundRemovalResponse,
    dependencies=[Depends(verify_api_key)],
)
async def enhance_image_endpoint(
    image: UploadFile = File(..., description="Image file to enhance quality"),
    upscale_type: str = Form("conservative", description="Type of upscale: fast, conservative, creative"),
    prompt: Optional[str] = Form(None, description="Description of the image (required for conservative/creative)"),
    creativity: float = Form(0.3, description="Creativity level (0.1-0.5 for creative, 0.0-0.35 for conservative)"),
    style_preset: Optional[str] = Form(None, description="Style preset for creative upscale"),
    output_format: Optional[str] = Form("webp", description="Output format (jpeg, png, webp)")
) -> FrontendBackgroundRemovalResponse:
    """Enhance image quality using Stability AI v2beta Upscale APIs."""
    logger.info(f"Received image enhancement request for file: {image.filename}, type: {upscale_type}")

    try:
        # Validar tipo de upscale
        if upscale_type not in ["fast", "conservative", "creative"]:
            raise HTTPException(
                status_code=400,
                detail="upscale_type must be 'fast', 'conservative', or 'creative'"
            )

        # Validar formato de archivo
        if not image.content_type or not image.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail="File must be an image (JPEG, PNG, or WebP)"
            )

        # Validar formatos soportados
        supported_formats = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
        if image.content_type not in supported_formats:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported image format: {image.content_type}. Supported: JPEG, PNG, WebP"
            )

        # Validar tamaño de archivo (máximo 10MB según documentación de Stability AI)
        max_size = 10 * 1024 * 1024  # 10MB
        image_content = await image.read()

        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: 10MB, received: {len(image_content) / 1024 / 1024:.2f}MB"
            )

        # Validar formato de salida
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="Output format must be 'jpeg', 'png' or 'webp'"
            )

        # Validaciones específicas por tipo
        if upscale_type in ["conservative", "creative"]:
            if not prompt:
                raise HTTPException(
                    status_code=400,
                    detail=f"Prompt is required for {upscale_type} upscale"
                )

        if upscale_type == "conservative":
            if not (0.2 <= creativity <= 0.5):
                raise HTTPException(
                    status_code=400,
                    detail="Creativity for conservative upscale must be between 0.2 and 0.5"
                )
        elif upscale_type == "creative":
            if not (0.1 <= creativity <= 0.5):
                raise HTTPException(
                    status_code=400,
                    detail="Creativity for creative upscale must be between 0.1 and 0.5"
                )

        logger.info(f"Processing image: {image.filename}, size: {len(image_content)} bytes")
        logger.info(f"Type: {upscale_type}, Prompt: {prompt}, Creativity: {creativity}, Output: {output_format}")

        # Llamar a la API correspondiente según el tipo
        if upscale_type == "fast":
            service_response = await call_stability_upscale_fast(image_content, output_format)
        elif upscale_type == "conservative":
            service_response = await call_stability_upscale_conservative(image_content, prompt, creativity, output_format)
        elif upscale_type == "creative":
            # Para creative, devolvemos el generation_id para polling
            creative_result = await call_stability_upscale_creative_start(image_content, prompt, creativity, output_format, style_preset)
            return FrontendBackgroundRemovalResponse(
                success=True,
                url=None,  # No hay URL todavía
                metadata={
                    "generation_id": creative_result["generation_id"],
                    "upscale_type": "creative",
                    "status": "processing",
                    "original_filename": image.filename,
                    "prompt": prompt,
                    "creativity": creativity,
                    "style_preset": style_preset
                }
            )

        if service_response.image:
            # Crear data URL para el frontend
            mime_type = f"image/{output_format}"
            image_data_url = f"data:{mime_type};base64,{service_response.image}"

            logger.info(f"Image enhancement successful, result size: {len(image_data_url)} chars")

            return FrontendBackgroundRemovalResponse(
                success=True,
                url=image_data_url,
                metadata={
                    "original_filename": image.filename,
                    "output_format": output_format,
                    "finish_reason": service_response.finish_reason,
                    "seed": service_response.seed,
                    "prompt": prompt,
                    "creativity": creativity
                }
            )
        else:
            logger.error("No image data in service response")
            return FrontendBackgroundRemovalResponse(
                success=False,
                error="No image data received from Stability AI"
            )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error in image enhancement: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image enhancement: {str(e)}"
        )


@router.get(
    "/enhance-image/creative/result/{generation_id}",
    response_model=FrontendBackgroundRemovalResponse,
    dependencies=[Depends(verify_api_key)],
)
async def get_creative_upscale_result(generation_id: str) -> FrontendBackgroundRemovalResponse:
    """Get the result of a creative upscale operation."""
    logger.info(f"Getting creative upscale result for ID: {generation_id}")

    try:
        service_response = await call_stability_upscale_creative_result(generation_id)

        if service_response.image:
            # Crear data URL para el frontend
            mime_type = "image/webp"  # Default format for creative upscale
            image_data_url = f"data:{mime_type};base64,{service_response.image}"

            logger.info(f"Creative upscale completed successfully, result size: {len(image_data_url)} chars")

            return FrontendBackgroundRemovalResponse(
                success=True,
                url=image_data_url,
                metadata={
                    "generation_id": generation_id,
                    "upscale_type": "creative",
                    "status": "completed",
                    "finish_reason": service_response.finish_reason,
                    "seed": service_response.seed
                }
            )
        else:
            logger.error("No image data in service response")
            return FrontendBackgroundRemovalResponse(
                success=False,
                error="No image data received from Stability AI"
            )

    except HTTPException as http_exc:
        if http_exc.status_code == 202:
            # Still processing
            return FrontendBackgroundRemovalResponse(
                success=False,
                error="Generation still in progress",
                metadata={
                    "generation_id": generation_id,
                    "upscale_type": "creative",
                    "status": "processing"
                }
            )
        else:
            # Re-raise other HTTP exceptions
            raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error getting creative upscale result: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error getting creative upscale result: {str(e)}"
        )


@router.post(
    "/style-reference",
    response_model=FrontendStyleReferenceResponse,
    dependencies=[Depends(verify_api_key)],
)
async def apply_style_reference(
    image: UploadFile = File(..., description="Reference image file (JPEG, PNG, or WebP)"),
    prompt: str = Form(..., description="Description of what you want to see in the output image"),
    negative_prompt: Optional[str] = Form(None, description="What you do not want to see in the output image"),
    aspect_ratio: Optional[str] = Form("1:1", description="Aspect ratio of the generated image"),
    fidelity: Optional[float] = Form(0.5, description="How closely the output resembles the input style (0.0-1.0)"),
    seed: Optional[int] = Form(0, description="Random seed for generation (0 for random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)"),
    style_preset: Optional[str] = Form(None, description="Style preset to guide the generation")
) -> FrontendStyleReferenceResponse:
    """
    Apply style reference to generate a new image using Stability AI v2beta API.
    Extracts stylistic elements from the reference image and applies them to a new generation.
    """
    logger.info(f"Received style reference request for prompt: '{prompt[:50]}...'")

    try:
        # Validar archivo de imagen
        if not image.content_type or not image.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail="File must be an image (JPEG, PNG, or WebP)"
            )

        # Validar tamaño de archivo (máximo 10MB según documentación de Stability AI)
        max_size = 10 * 1024 * 1024  # 10MB
        image_content = await image.read()

        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: 10MB, received: {len(image_content) / 1024 / 1024:.2f}MB"
            )

        # Validar formato de salida
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="Output format must be 'jpeg', 'png' or 'webp'"
            )

        # Validar fidelity
        if fidelity < 0.0 or fidelity > 1.0:
            raise HTTPException(
                status_code=400,
                detail="Fidelity must be between 0.0 and 1.0"
            )

        logger.info(f"Processing style reference: {image.filename}, size: {len(image_content)} bytes")
        logger.info(f"Parameters: fidelity={fidelity}, aspect_ratio={aspect_ratio}, output_format={output_format}")

        # Llamar al servicio de Stability AI
        service_response = await call_stability_style_reference(
            image_content,
            prompt,
            negative_prompt,
            aspect_ratio,
            fidelity,
            seed,
            output_format,
            style_preset
        )

        # Convertir respuesta a formato frontend
        image_data_url = f"data:image/{output_format};base64,{service_response.image}"

        logger.info("Style reference generation completed successfully")

        return FrontendStyleReferenceResponse(
            success=True,
            image_url=image_data_url,
            seed=service_response.seed,
            finish_reason=service_response.finish_reason,
            metadata={
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "aspect_ratio": aspect_ratio,
                "fidelity": fidelity,
                "style_preset": style_preset,
                "output_format": output_format,
                "original_filename": image.filename
            }
        )

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error in style reference: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during style reference: {str(e)}"
        )


@router.post("/style-transfer")
async def style_transfer(
    init_image: UploadFile = File(..., description="The original image to be restyled"),
    style_image: UploadFile = File(..., description="The style reference image"),
    prompt: str = Form("", description="Optional text prompt to guide the generation"),
    negative_prompt: Optional[str] = Form(None, description="What you don't want to see"),
    style_strength: float = Form(1.0, description="How much influence the style image has (0.0-1.0)"),
    composition_fidelity: float = Form(0.9, description="How closely output resembles input style (0.0-1.0)"),
    change_strength: float = Form(0.9, description="How much the original image should change (0.1-1.0)"),
    seed: int = Form(0, description="Random seed for reproducible results"),
    output_format: str = Form("png", description="Output image format")
):
    """
    Apply style transfer using Stability AI.
    Transfers the style from a reference image to an original image.
    """
    try:
        # Validar archivos de imagen
        if not init_image.content_type or not init_image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="init_image must be an image file")

        if not style_image.content_type or not style_image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="style_image must be an image file")

        # Leer contenido de las imágenes
        init_image_content = await init_image.read()
        style_image_content = await style_image.read()

        # Validar tamaño de archivos (máx 10MB cada uno)
        max_size = 10 * 1024 * 1024  # 10MB
        if len(init_image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"init_image file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )

        if len(style_image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"style_image file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )

        # Validar parámetros
        if style_strength < 0.0 or style_strength > 1.0:
            raise HTTPException(
                status_code=400,
                detail="style_strength must be between 0.0 and 1.0"
            )

        if composition_fidelity < 0.0 or composition_fidelity > 1.0:
            raise HTTPException(
                status_code=400,
                detail="composition_fidelity must be between 0.0 and 1.0"
            )

        if change_strength < 0.1 or change_strength > 1.0:
            raise HTTPException(
                status_code=400,
                detail="change_strength must be between 0.1 and 1.0"
            )

        logger.info(f"Processing style transfer: init={init_image.filename}, style={style_image.filename}")
        logger.info(f"Parameters: style_strength={style_strength}, composition_fidelity={composition_fidelity}, change_strength={change_strength}")

        # Llamar al servicio de Stability AI
        service_response = await call_stability_style_transfer(
            init_image_content,
            style_image_content,
            prompt,
            negative_prompt,
            style_strength,
            composition_fidelity,
            change_strength,
            seed,
            output_format
        )

        # Retornar respuesta en formato esperado por el frontend
        return {
            "success": True,
            "image": service_response.image,
            "seed": service_response.seed,
            "finish_reason": service_response.finish_reason,
            "metadata": {
                "init_image_filename": init_image.filename,
                "style_image_filename": style_image.filename,
                "style_strength": style_strength,
                "composition_fidelity": composition_fidelity,
                "change_strength": change_strength
            }
        }

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions
        raise http_exc
    except Exception as e:
        logger.error(f"Unexpected error in style transfer: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during style transfer: {str(e)}"
        )


# GPT-4 Image Generation Endpoint
@router.post("/generate-gpt", response_model=GPTImageGenerationResponse)
async def generate_gpt_image_endpoint(
    prompt: str = Form(..., description="The prompt to generate the image from"),
    size: Optional[str] = Form("1024x1024", description="Size of the generated image"),
    quality: Optional[str] = Form("standard", description="Quality of the generated image"),
    style: Optional[str] = Form("vivid", description="Style of the generated image"),
    response_format: Optional[str] = Form("b64_json", description="Format of the response")
) -> GPTImageGenerationResponse:
    """
    Generate an image using GPT-4 gpt-image-1 model.

    This endpoint is optimized for:
    - Text-based images (memes, quotes, inspirational posts)
    - Conceptual illustrations
    - Creative artwork with text elements
    - Social media posts with typography
    """
    logger.info(f"🎨 GPT-4 Image generation request: '{prompt[:50]}...'")

    try:
        # Create request object
        request = GPTImageGenerationRequest(
            prompt=prompt,
            size=size,
            quality=quality,
            style=style,
            response_format=response_format
        )

        # Generate image using GPT-4
        result = await generate_gpt_image(request)

        logger.info(f"✅ GPT-4 Image generation completed: success={result.success}")
        return result

    except Exception as e:
        logger.error(f"❌ Error in GPT-4 image generation: {e}")
        return GPTImageGenerationResponse(
            success=False,
            error=f"Failed to generate image: {str(e)}"
        )
