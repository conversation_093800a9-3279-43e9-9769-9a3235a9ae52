// Final Verification Test for Visual Complexity Analyzer
// Run this in browser console to verify all fixes are working

console.log('🎯 Final Verification Test - Visual Complexity Analyzer');

function testPageLoading() {
  console.log('\n📋 Test 1: Page Loading');
  
  const currentUrl = window.location.href;
  console.log('Current URL:', currentUrl);
  
  if (currentUrl.includes('design-complexity-analyzer')) {
    console.log('✅ Correct URL path');
  } else {
    console.log('❌ Incorrect URL path');
    return false;
  }
  
  // Check for 404 error
  const errorMessage = document.querySelector('h1, h2, h3');
  if (errorMessage && errorMessage.textContent.includes('404')) {
    console.log('❌ 404 error detected');
    return false;
  } else {
    console.log('✅ No 404 error');
  }
  
  return true;
}

function testDebugIndicatorRemoved() {
  console.log('\n📋 Test 2: Debug Indicator Removal');
  
  const debugIndicator = document.querySelector('.fixed.top-4.right-4.bg-green-500');
  
  if (debugIndicator) {
    console.log('❌ Debug indicator still present:', debugIndicator.textContent);
    return false;
  } else {
    console.log('✅ Debug indicator successfully removed');
    return true;
  }
}

function testComponentStructure() {
  console.log('\n📋 Test 3: Component Structure');
  
  const title = document.querySelector('h1');
  const tabs = document.querySelectorAll('[role="tablist"] button');
  
  if (title && title.textContent.includes('Analizador de Complejidad Visual')) {
    console.log('✅ Main title found');
  } else {
    console.log('❌ Main title not found');
    return false;
  }
  
  if (tabs.length >= 4) {
    console.log('✅ All tabs present:', tabs.length);
    
    const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
    console.log('Tab texts:', tabTexts);
    
    const expectedTabs = ['Análisis', 'Resultados', 'Historial', 'Favoritos'];
    const hasRequiredTabs = expectedTabs.every(expectedTab => 
      tabTexts.some(tabText => tabText.includes(expectedTab))
    );
    
    if (hasRequiredTabs) {
      console.log('✅ All required tabs found');
    } else {
      console.log('❌ Some required tabs missing');
      return false;
    }
  } else {
    console.log('❌ Not enough tabs found:', tabs.length);
    return false;
  }
  
  return true;
}

function testTabNavigation() {
  console.log('\n📋 Test 4: Tab Navigation');
  
  const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
    .find(tab => tab.textContent.includes('Historial'));
  
  if (historyTab && !historyTab.disabled) {
    console.log('✅ History tab is clickable');
    
    // Test clicking
    historyTab.click();
    
    setTimeout(() => {
      const historyContent = document.querySelector('[value="history"]');
      if (historyContent) {
        console.log('✅ History tab navigation works');
      } else {
        console.log('❌ History tab navigation failed');
      }
    }, 500);
    
    return true;
  } else {
    console.log('❌ History tab not found or disabled');
    return false;
  }
}

function testMockData() {
  console.log('\n📋 Test 5: Mock Data');
  
  const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
    .find(tab => tab.textContent.includes('Historial'));
  
  if (historyTab) {
    historyTab.click();
    
    setTimeout(() => {
      const analysisCards = document.querySelectorAll('.grid.gap-4 > *');
      
      if (analysisCards.length > 0) {
        console.log('✅ Mock data loaded:', analysisCards.length, 'cards');
        
        // Test card content
        const firstCard = analysisCards[0];
        const cardTitle = firstCard.querySelector('h3');
        const favoriteButton = firstCard.querySelector('button');
        
        if (cardTitle) {
          console.log('✅ Card has title:', cardTitle.textContent.trim());
        }
        
        if (favoriteButton) {
          console.log('✅ Card has interactive buttons');
        }
        
        return true;
      } else {
        console.log('❌ No mock data found');
        return false;
      }
    }, 1000);
  }
  
  return true;
}

function testConsoleErrors() {
  console.log('\n📋 Test 6: Console Errors');
  
  // Capture console errors
  const originalError = console.error;
  const errors = [];
  
  console.error = function(...args) {
    // Filter out browser extension errors
    const errorMessage = args.join(' ');
    if (!errorMessage.includes('runtime.lastError') && 
        !errorMessage.includes('extension') &&
        !errorMessage.includes('chrome-extension')) {
      errors.push(args);
    }
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    if (errors.length === 0) {
      console.log('✅ No relevant console errors detected');
    } else {
      console.log('❌ Console errors found:', errors);
    }
    
    // Restore original console.error
    console.error = originalError;
  }, 2000);
  
  return true;
}

function runAllTests() {
  console.log('🚀 Running Final Verification Tests...\n');
  
  const results = {
    pageLoading: testPageLoading(),
    debugRemoved: testDebugIndicatorRemoved(),
    componentStructure: testComponentStructure(),
    tabNavigation: false,
    mockData: false,
    consoleErrors: false
  };
  
  setTimeout(() => {
    results.tabNavigation = testTabNavigation();
  }, 1000);
  
  setTimeout(() => {
    results.mockData = testMockData();
  }, 2000);
  
  setTimeout(() => {
    results.consoleErrors = testConsoleErrors();
  }, 3000);
  
  // Final summary
  setTimeout(() => {
    console.log('\n🎯 Final Verification Results:');
    console.log('✅ Page Loading:', results.pageLoading);
    console.log('✅ Debug Indicator Removed:', results.debugRemoved);
    console.log('✅ Component Structure:', results.componentStructure);
    console.log('✅ Tab Navigation:', results.tabNavigation);
    console.log('✅ Mock Data:', results.mockData);
    console.log('✅ Console Errors:', results.consoleErrors);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n📊 Overall Score: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests >= 5) {
      console.log('🎉 Visual Complexity Analyzer is working correctly!');
      console.log('✅ All major issues have been resolved:');
      console.log('  - Debug indicator removed');
      console.log('  - 404 routing error fixed');
      console.log('  - Component renders properly');
      console.log('  - All 4 tabs are functional');
      console.log('  - History and favorites management working');
    } else {
      console.log('⚠️ Some issues may still exist. Check the details above.');
    }
  }, 5000);
}

// Auto-run tests
runAllTests();

// Export for manual testing
window.finalVerificationTest = {
  runAllTests,
  testPageLoading,
  testDebugIndicatorRemoved,
  testComponentStructure,
  testTabNavigation,
  testMockData,
  testConsoleErrors
};

console.log('\n📝 Manual test functions available in window.finalVerificationTest');
