#!/usr/bin/env python3
"""
Test script for SEO & GPT Optimizer™
Tests the complete functionality of the new SEO GPT Optimizer system
"""

import asyncio
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.seo_gpt_research import SEOGPTResearchService
from app.services.gpt_rank_engine import GPTRankEngineService

async def test_research_service():
    """Test the Research Engine Service."""
    print("🔍 Testing SEO GPT Research Service...")
    
    research_service = SEOGPTResearchService()
    
    # Test topic
    topic = "beneficios del magnesio para la salud"
    
    try:
        print(f"📊 Conducting research for: '{topic}'")
        
        results = await research_service.conduct_comprehensive_research(
            topic=topic,
            target_language="es",
            include_reddit=True,
            include_quora=True
        )
        
        if results.get("status") == "success":
            print("✅ Research completed successfully!")
            print(f"   Processing time: {results.get('processing_time', 0):.2f}s")
            print(f"   Intent type: {results.get('intent_analysis', {}).get('intent_type', 'unknown')}")
            print(f"   Google results: {len(results.get('google_results', {}).get('results', []))}")
            print(f"   Research confidence: {results.get('research_summary', {}).get('research_confidence', 0):.2f}")
            
            # Show some key insights
            entities = results.get('entities_and_questions', {})
            if entities.get('common_questions'):
                print("   Top questions:")
                for i, question in enumerate(entities['common_questions'][:3], 1):
                    print(f"     {i}. {question}")
            
            return True
        else:
            print(f"❌ Research failed: {results.get('error_message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Research service test failed: {str(e)}")
        return False

async def test_gpt_rank_service():
    """Test the GPT Rank Engine Service."""
    print("\n🧮 Testing GPT Rank Engine Service...")
    
    gpt_rank_service = GPTRankEngineService()
    
    # Test content
    test_content = """
    El magnesio es un mineral esencial que desempeña un papel crucial en más de 300 reacciones enzimáticas en el cuerpo humano. 
    
    Este nutriente es fundamental para el funcionamiento adecuado del sistema nervioso, la función muscular y el mantenimiento de un ritmo cardíaco saludable. Según estudios científicos, la deficiencia de magnesio puede estar relacionada con diversos problemas de salud.
    
    Los beneficios del magnesio incluyen:
    
    1. Mejora de la calidad del sueño
    2. Reducción del estrés y la ansiedad
    3. Fortalecimiento de los huesos
    4. Regulación de la presión arterial
    5. Apoyo a la función cardiovascular
    
    Las fuentes alimentarias ricas en magnesio incluyen vegetales de hoja verde, frutos secos, semillas, legumbres y cereales integrales. La investigación muestra que muchas personas no consumen suficiente magnesio en su dieta diaria.
    
    Es importante consultar con un profesional de la salud antes de tomar suplementos de magnesio, especialmente si se tienen condiciones médicas preexistentes.
    """
    
    try:
        print("📈 Calculating GPT Rank Score...")
        
        results = await gpt_rank_service.calculate_gpt_rank_score(
            content=test_content,
            topic="beneficios del magnesio"
        )
        
        if results.get("status") == "success":
            print("✅ GPT Rank calculation completed successfully!")
            print(f"   GPT Rank Score: {results.get('gpt_rank_score', 0):.1f}/100")
            print(f"   Score Grade: {results.get('score_grade', 'N/A')}")
            print(f"   Confidence Level: {results.get('confidence_level', 'unknown')}")
            
            # Show component scores
            component_scores = results.get('component_scores', {})
            print("   Component Scores:")
            for component, score in component_scores.items():
                print(f"     {component}: {score:.1f}")
            
            # Show content stats
            content_stats = results.get('content_stats', {})
            print("   Content Statistics:")
            print(f"     Word count: {content_stats.get('word_count', 0)}")
            print(f"     Character count: {content_stats.get('character_count', 0)}")
            print(f"     Paragraph count: {content_stats.get('paragraph_count', 0)}")
            
            # Show improvement suggestions
            suggestions = results.get('improvement_suggestions', [])
            if suggestions:
                print("   Top Improvement Suggestions:")
                for i, suggestion in enumerate(suggestions[:3], 1):
                    print(f"     {i}. {suggestion.get('title', 'Unknown')} (Score: {suggestion.get('current_score', 0):.1f})")
            
            return True
        else:
            print(f"❌ GPT Rank calculation failed: {results.get('error_message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ GPT Rank service test failed: {str(e)}")
        return False

async def test_integration():
    """Test integration between services."""
    print("\n🔗 Testing Service Integration...")
    
    try:
        # First, conduct research
        research_service = SEOGPTResearchService()
        topic = "cómo mejorar el sueño naturalmente"
        
        print(f"🔍 Researching topic: '{topic}'")
        research_results = await research_service.conduct_comprehensive_research(
            topic=topic,
            target_language="es"
        )
        
        if research_results.get("status") != "success":
            print("❌ Research failed, cannot test integration")
            return False
        
        # Extract GPT reference content for analysis
        gpt_reference = research_results.get('gpt_reference', {})
        reference_content = gpt_reference.get('response_text', '')
        
        if not reference_content:
            print("❌ No GPT reference content available for analysis")
            return False
        
        # Analyze the reference content with GPT Rank Engine
        gpt_rank_service = GPTRankEngineService()
        
        print("🧮 Analyzing GPT reference content...")
        rank_results = await gpt_rank_service.calculate_gpt_rank_score(
            content=reference_content,
            topic=topic
        )
        
        if rank_results.get("status") == "success":
            print("✅ Integration test completed successfully!")
            print(f"   Research processing time: {research_results.get('processing_time', 0):.2f}s")
            print(f"   GPT Rank Score: {rank_results.get('gpt_rank_score', 0):.1f}/100")
            print(f"   Content analyzed: {len(reference_content)} characters")
            
            return True
        else:
            print(f"❌ GPT Rank analysis failed: {rank_results.get('error_message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting SEO & GPT Optimizer™ Tests")
    print("=" * 50)
    
    # Test individual services
    research_success = await test_research_service()
    gpt_rank_success = await test_gpt_rank_service()
    integration_success = await test_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Research Service: {'✅ PASS' if research_success else '❌ FAIL'}")
    print(f"   GPT Rank Service: {'✅ PASS' if gpt_rank_success else '❌ FAIL'}")
    print(f"   Integration Test: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    all_passed = research_success and gpt_rank_success and integration_success
    
    if all_passed:
        print("\n🎉 All tests passed! SEO & GPT Optimizer™ is ready to use.")
        print("\nNext steps:")
        print("1. Run the backend server: python -m uvicorn app.main:app --reload")
        print("2. Test the API endpoints at: http://localhost:8000/api/v1/docs")
        print("3. Create the frontend components")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration and try again.")
        print("\nTroubleshooting:")
        print("1. Ensure GEMINI_API_KEY is set in your environment")
        print("2. Ensure SERPER_API_KEY is set for Google search functionality")
        print("3. Check your internet connection")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
