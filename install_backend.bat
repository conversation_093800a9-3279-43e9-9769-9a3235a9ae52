@echo off
echo ========================================
echo Emma Studio Backend Installation Script
echo ========================================
echo.

REM Set Python path
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

echo Checking Python installation...
%PYTHON_PATH% --version
if errorlevel 1 (
    echo ERROR: Python not found at expected location
    echo Please check if Python is installed correctly
    pause
    exit /b 1
)

echo.
echo Installing Poetry...
%PYTHON_PATH% -m pip install poetry
if errorlevel 1 (
    echo ERROR: Failed to install Poetry
    pause
    exit /b 1
)

echo.
echo Adding Poetry to PATH...
set PATH=%PATH%;%USERPROFILE%\AppData\Roaming\Python\Scripts

echo.
echo Installing backend dependencies...
cd backend
%PYTHON_PATH% -m poetry install
if errorlevel 1 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To start the backend server, run:
echo cd backend
echo poetry run uvicorn app.main:app --reload --port 8001
echo.
echo Or use the start_backend.bat script
pause
