"""API endpoints for headline analysis."""

import logging
import time
from typing import Dict, Any, Optional
import re

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field, validator
import google.generativeai as genai

from app.core.config import settings

# Setup logging
logger = logging.getLogger(__name__)

router = APIRouter()

# Configure Gemini
if settings.GEMINI_API_KEY:
    genai.configure(api_key=settings.GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-1.5-flash')
else:
    model = None
    logger.warning("GEMINI_API_KEY not configured. Headline analyzer will not work.")


class HeadlineAnalysisRequest(BaseModel):
    """Request model for headline analysis."""
    headline: str = Field(..., min_length=1, max_length=500, description="Headline to analyze")
    audience_context: Optional[str] = Field(None, max_length=200, description="Target audience context")
    content_type: Optional[str] = Field("blog", description="Type of content (blog, facebook_ad, etc.)")
    content_type_context: Optional[str] = Field(None, max_length=200, description="Content type description")

    @validator('headline')
    def validate_headline(cls, v):
        """Validate headline content."""
        if not v or not v.strip():
            raise ValueError("Headline cannot be empty")

        # Remove excessive whitespace
        v = re.sub(r'\s+', ' ', v.strip())

        # Check for minimum meaningful content
        if len(v.split()) < 2:
            raise ValueError("Headline must contain at least 2 words")

        # Check for suspicious patterns (spam, nonsense)
        if re.match(r'^[^a-zA-Z0-9\s]*$', v):
            raise ValueError("Headline must contain meaningful text")

        return v

    @validator('audience_context')
    def validate_audience_context(cls, v):
        """Validate audience context."""
        if v:
            v = v.strip()
            if len(v) == 0:
                return None
        return v


class HeadlineAnalysisResponse(BaseModel):
    """Response model for headline analysis."""
    status: str
    message: str
    basic_analysis: Dict[str, Any]
    advanced_analysis: Dict[str, Any]
    recommendations: Dict[str, Any]
    overall_score: int
    processing_time: float


def analyze_headline_with_gemini(
    headline: str,
    audience_context: Optional[str] = None,
    content_type: Optional[str] = "blog",
    content_type_context: Optional[str] = None
) -> Dict[str, Any]:
    """Analyze headline using Gemini AI."""

    # Construir contexto más específico
    context_parts = []

    if content_type_context:
        context_parts.append(f"This is for {content_type_context}")

    if audience_context:
        context_parts.append(f"targeting {audience_context}")

    context_prompt = f" ({', '.join(context_parts)})" if context_parts else ""

    # Criterios específicos por tipo de contenido
    content_specific_criteria = {
        "blog": "Focus on SEO optimization, readability, and click-through rates from search results",
        "facebook_ad": "Emphasize social engagement, emotional triggers, and Facebook ad best practices",
        "instagram_ad": "Consider visual appeal, hashtag potential, and Instagram user behavior",
        "google_ad": "Optimize for search intent, character limits, and Google Ads quality score",
        "email": "Focus on open rates, spam filter avoidance, and email marketing best practices",
        "youtube": "Consider YouTube algorithm, thumbnail compatibility, and video discovery",
        "landing_page": "Emphasize conversion optimization, value proposition clarity, and user intent",
        "social_post": "Focus on social media engagement, shareability, and platform-specific best practices",
        "product": "Consider product positioning, competitive differentiation, and purchase intent",
        "newsletter": "Focus on subscriber engagement, content preview, and newsletter best practices"
    }

    specific_criteria = content_specific_criteria.get(content_type, content_specific_criteria["blog"])

    # Detectar idioma del título
    is_spanish = any(word in headline.lower() for word in ['cómo', 'como', 'qué', 'que', 'por', 'para', 'con', 'sin', 'más', 'mas', 'mejor', 'peor', 'tu', 'tus', 'su', 'sus', 'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas', 'de', 'del', 'en', 'y', 'o', 'pero', 'si', 'no', 'sí', 'también', 'tambien', 'muy', 'mucho', 'poco', 'todo', 'nada', 'algo', 'alguien', 'nadie', 'donde', 'cuando', 'porque', 'aunque', 'mientras', 'durante', 'después', 'antes', 'ahora', 'hoy', 'mañana', 'ayer', 'siempre', 'nunca', 'vez', 'veces', 'tiempo', 'año', 'años', 'día', 'días', 'mes', 'meses', 'semana', 'semanas', 'hora', 'horas', 'minuto', 'minutos', 'segundo', 'segundos', 'dinero', 'euros', 'dólares', 'pesos', 'negocio', 'empresa', 'trabajo', 'empleo', 'casa', 'hogar', 'familia', 'hijo', 'hijos', 'padre', 'madre', 'hermano', 'hermana', 'amigo', 'amigos', 'persona', 'personas', 'gente', 'mundo', 'vida', 'amor', 'salud', 'felicidad', 'éxito', 'exito', 'problema', 'problemas', 'solución', 'solucion', 'respuesta', 'pregunta', 'secreto', 'secretos', 'estrategia', 'estrategias', 'método', 'metodo', 'forma', 'manera', 'modo', 'razón', 'razon', 'causa', 'efecto', 'resultado', 'resultados', 'beneficio', 'beneficios', 'ventaja', 'ventajas', 'desventaja', 'desventajas', 'error', 'errores', 'fallo', 'fallos', 'éxito', 'fracaso', 'ganar', 'perder', 'conseguir', 'lograr', 'obtener', 'recibir', 'dar', 'hacer', 'crear', 'construir', 'desarrollar', 'mejorar', 'cambiar', 'transformar', 'aumentar', 'reducir', 'eliminar', 'evitar', 'prevenir', 'proteger', 'cuidar', 'mantener', 'conservar', 'guardar', 'ahorrar', 'gastar', 'invertir', 'comprar', 'vender', 'pagar', 'cobrar', 'gratis', 'gratuito', 'libre', 'fácil', 'facil', 'difícil', 'dificil', 'simple', 'complicado', 'rápido', 'rapido', 'lento', 'nuevo', 'viejo', 'antiguo', 'moderno', 'actual', 'pasado', 'futuro', 'presente', 'grande', 'pequeño', 'alto', 'bajo', 'largo', 'corto', 'ancho', 'estrecho', 'gordo', 'delgado', 'fuerte', 'débil', 'debil', 'bueno', 'malo', 'bien', 'mal', 'mejor', 'peor', 'excelente', 'perfecto', 'imperfecto', 'correcto', 'incorrecto', 'verdadero', 'falso', 'real', 'irreal', 'posible', 'imposible', 'fácil', 'difícil', 'importante', 'necesario', 'útil', 'util', 'inútil', 'inutíl'])

    language_instruction = ""
    if is_spanish:
        language_instruction = """
        INSTRUCCIÓN CRÍTICA DE IDIOMA: Este título está en ESPAÑOL. TODA tu respuesta debe estar en ESPAÑOL PERFECTO.
        - Escribe TODOS los textos en español
        - NO uses palabras en inglés como "engagement", "score", "keywords", "triggers", etc.
        - Usa términos en español: "compromiso/participación" en lugar de "engagement", "puntuación" en lugar de "score", "palabras clave" en lugar de "keywords", "disparadores" en lugar de "triggers"
        - Los títulos alternativos deben estar en español
        - Las emociones deben estar en español (alegría, tristeza, emoción, etc.)
        - Los análisis deben estar en español natural y fluido
        """
    else:
        language_instruction = """
        LANGUAGE INSTRUCTION: This headline appears to be in English. Provide your entire response in English.
        """

    prompt = f"""
    Analyze this headline{context_prompt}: "{headline}"

    {language_instruction}

    IMPORTANT:
    1. {specific_criteria}
    2. RESPOND ENTIRELY IN THE SAME LANGUAGE AS THE HEADLINE
    3. All analysis, recommendations, and alternative headlines must be in the same language as the input headline

    Provide a comprehensive analysis in the following JSON format:
    {{
        "basic_analysis": {{
            "word_count": <number>,
            "char_count": <number>,
            "contains_number": <boolean>,
            "is_question": <boolean>,
            "quality_check": {{
                "is_valid_text": <boolean>,
                "quality_score": <number 0-100>,
                "issues": [<list of issues>],
                "is_nonsense": <boolean>
            }}
        }},
        "advanced_analysis": {{
            "emotional_impact": {{
                "score": <number 0-100>,
                "emotions": [<list of emotions>],
                "analysis": "<detailed analysis>"
            }},
            "clarity_analysis": {{
                "score": <number 0-100>,
                "analysis": "<clarity assessment>"
            }},
            "engagement_potential": {{
                "score": <number 0-100>,
                "factors": [<engagement factors>],
                "analysis": "<engagement analysis>"
            }},
            "seo_analysis": {{
                "score": <number 0-100>,
                "keywords": [<potential keywords>],
                "analysis": "<SEO assessment>"
            }},
            "psychological_triggers": {{
                "triggers": [<psychological triggers>],
                "effectiveness": <number 0-100>,
                "analysis": "<psychological analysis>"
            }},
            "target_audience_fit": {{
                "score": <number 0-100>,
                "analysis": "<audience fit analysis>"
            }},
            "main_topics": [<list of main topics>]
        }},
        "recommendations": {{
            "improvements": [<list of specific improvements>],
            "alternative_headlines": [<5 creative alternative headlines>],
            "viral_variations": [<3 titles optimized specifically for virality with hooks, curiosity gaps, and emotional triggers>],
            "platform_specific": {{
                "google_ads": "<title optimized for Google Ads with strong CTR potential>",
                "facebook": "<title optimized for Facebook engagement and social sharing>",
                "instagram": "<title optimized for Instagram with visual appeal and hashtag potential>",
                "linkedin": "<title optimized for LinkedIn professional audience>",
                "youtube": "<title optimized for YouTube algorithm and thumbnail compatibility>"
            }},
            "competitive_analysis": {{
                "uniqueness_score": <number 0-100>,
                "differentiation_factors": [<what makes this title unique>],
                "market_positioning": "<how this title positions against competitors>"
            }},
            "strengths": [<list of strengths>],
            "weaknesses": [<list of weaknesses>]
        }},
        "overall_score": <number 0-100>
    }}

    Ensure all scores are realistic and based on marketing best practices. Be specific and actionable in recommendations.

    SPECIAL INSTRUCTIONS FOR NEW FEATURES:
    1. VIRAL VARIATIONS: Create titles that use proven viral formulas like "This [X] Will Make You [Y]", "The Secret [X] That [Y]", "[Number] [Things] That Will [Action]"
    2. PLATFORM SPECIFIC: Adapt the title for each platform's unique characteristics and audience behavior
    3. COMPETITIVE ANALYSIS: Evaluate how unique and differentiated this title is in the market
    4. ALTERNATIVE HEADLINES: Make them significantly different from the original, not just minor tweaks

    FINAL LANGUAGE CHECK:
    - If the headline "{headline}" is in Spanish, your ENTIRE JSON response must be in Spanish
    - NO English words allowed in Spanish responses (no "engagement", "score", "keywords", "triggers", "insights", etc.)
    - Use Spanish equivalents: "participación", "puntuación", "palabras clave", "disparadores", "análisis"
    - Alternative headlines must be creative and in the same language
    - Emotions must be in the target language (Spanish: alegría, tristeza, emoción, curiosidad, etc.)
    - Analysis text must be natural and fluent in the target language
    """

    try:
        response = model.generate_content(prompt)

        # Extract JSON from response
        response_text = response.text.strip()

        # Try to extract JSON from the response
        import json

        # Look for JSON content between ```json and ``` or just raw JSON
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to find JSON-like content
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
            else:
                raise ValueError("No valid JSON found in response")

        result = json.loads(json_str)

        # Validate required fields
        required_fields = ['basic_analysis', 'advanced_analysis', 'recommendations', 'overall_score']
        for field in required_fields:
            if field not in result:
                raise ValueError(f"Missing required field: {field}")

        return result

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse Gemini response as JSON: {e}")
        raise ValueError("Invalid response format from AI analysis")
    except Exception as e:
        logger.error(f"Error in Gemini analysis: {e}")
        raise ValueError(f"AI analysis failed: {str(e)}")


@router.post("/analyze-headline", response_model=HeadlineAnalysisResponse)
async def analyze_headline(request: HeadlineAnalysisRequest, http_request: Request) -> HeadlineAnalysisResponse:
    """
    Analyze a headline for marketing effectiveness.

    This endpoint provides comprehensive headline analysis including:
    - Basic metrics (word count, character count, etc.)
    - Advanced analysis (emotional impact, clarity, engagement potential)
    - SEO analysis and recommendations
    - Psychological triggers assessment
    - Target audience fit analysis
    - Actionable improvement recommendations
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"req_{int(time.time()*1000)}")

    logger.info(f"Headline analysis request received. RequestID: {request_id}, Headline: {request.headline[:50]}...")

    if not model:
        logger.error(f"Gemini not configured. RequestID: {request_id}")
        raise HTTPException(
            status_code=503,
            detail={
                "error": "service_unavailable",
                "message": "AI analysis service is not available. Please contact support.",
                "request_id": request_id
            }
        )

    try:
        # Perform AI analysis
        analysis_result = analyze_headline_with_gemini(
            headline=request.headline,
            audience_context=request.audience_context,
            content_type=request.content_type,
            content_type_context=request.content_type_context
        )

        processing_time = time.time() - start_time

        logger.info(f"Headline analysis completed successfully. RequestID: {request_id}, ProcessingTime: {processing_time:.2f}s")

        return HeadlineAnalysisResponse(
            status="success",
            message="Headline analysis completed successfully",
            basic_analysis=analysis_result["basic_analysis"],
            advanced_analysis=analysis_result["advanced_analysis"],
            recommendations=analysis_result["recommendations"],
            overall_score=analysis_result["overall_score"],
            processing_time=processing_time
        )

    except ValueError as e:
        logger.warning(f"Validation error in headline analysis. RequestID: {request_id}, Error: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={
                "error": "validation_error",
                "message": str(e),
                "request_id": request_id
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in headline analysis. RequestID: {request_id}, Error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "internal_server_error",
                "message": "An unexpected error occurred during headline analysis",
                "request_id": request_id
            }
        )
