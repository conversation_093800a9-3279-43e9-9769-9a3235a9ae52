#!/usr/bin/env python3
"""
Test script para verificar la funcionalidad "Generar más como este".
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.post_generation_service import PostGenerationService
from app.services.ai_content_intelligence import AIContentIntelligence
from app.services.content_generation_service import ContentGenerationService


async def test_generate_similar_posts():
    """Test the 'generate more like this' functionality."""
    print("🎯 Probando funcionalidad 'Generar más como este'...")
    
    # Initialize services
    post_service = PostGenerationService()
    ai_intelligence = AIContentIntelligence()
    content_service = ContentGenerationService()
    
    # Test 1: Test reference post analysis
    print("\n📊 Test 1: Analizando post de referencia")
    
    reference_content = "¡Más ventas online para tu negocio! 🚀\n\nWouf: Suplementos que encantan a tus clientes. ¡Aumenta tus ventas ahora!"
    reference_template = "Promocional"
    brand_info = {
        "businessName": "Wouf Suplementos",
        "industry": "suplementos para mascotas",
        "target_audience": "dueños de perros"
    }
    platform = "Instagram"
    
    try:
        analysis = await ai_intelligence.analyze_reference_post_for_similarity(
            reference_content, reference_template, brand_info, platform
        )
        
        print(f"✅ Análisis de referencia completado:")
        print(f"   📝 Enfoque: {analysis.get('similarity_approach', 'N/A')}")
        print(f"   🎭 Tono: {analysis.get('tono', 'N/A')}")
        print(f"   📋 Tipo de contenido: {analysis.get('tipo_contenido', 'N/A')}")
        print(f"   🎯 Elementos clave: {analysis.get('elementos_clave', [])}")
        
    except Exception as e:
        print(f"❌ Error en análisis de referencia: {e}")
        return
    
    # Test 2: Test similar visual hook generation
    print("\n🎨 Test 2: Generando visual hooks similares")
    
    try:
        similar_hooks = []
        for i in range(3):
            hook = await content_service.generate_similar_visual_hook(
                reference_content, analysis, brand_info, i
            )
            similar_hooks.append(hook)
            print(f"   Hook {i+1}: '{hook}'")
        
        print(f"✅ Generados {len(similar_hooks)} hooks similares")
        
    except Exception as e:
        print(f"❌ Error generando hooks similares: {e}")
        return
    
    # Test 3: Test similar post content generation
    print("\n📝 Test 3: Generando contenido similar")
    
    try:
        for i, hook in enumerate(similar_hooks):
            content = await content_service.generate_similar_post_content(
                analysis, hook, brand_info, platform, reference_content
            )
            print(f"   Post {i+1}:")
            print(f"      Hook: '{hook}'")
            print(f"      Contenido: '{content[:100]}...'")
            print()
        
        print(f"✅ Generado contenido similar para {len(similar_hooks)} posts")
        
    except Exception as e:
        print(f"❌ Error generando contenido similar: {e}")
        return
    
    # Test 4: Test complete similar post generation
    print("\n🚀 Test 4: Generación completa de posts similares")
    
    reference_post = {
        "content": reference_content,
        "template": reference_template,
        "platform": platform,
        "metadata": {
            "businessName": "Wouf Suplementos",
            "brandColor": "#3018ef",
            "theme": "Promocional"
        }
    }
    
    generation_config = {
        "count": 3
    }
    
    try:
        response = await post_service.generate_similar_posts(
            brand_info=brand_info,
            reference_post=reference_post,
            generation_config=generation_config
        )
        
        if response.success:
            print(f"✅ Generación completa EXITOSA!")
            print(f"   📊 Posts similares generados: {response.total_generated}")
            print(f"   🎯 Enfoque: {response.metadata.get('similarity_approach', 'N/A')}")
            
            for i, post in enumerate(response.posts):
                print(f"\n   📝 Post similar {i+1}:")
                print(f"      Visual Hook: '{post.metadata.get('visual_hook', 'N/A')}'")
                print(f"      Texto: '{post.text[:100]}...'")
                print(f"      Imagen: {'✅ Generada' if post.image_url else '❌ No generada'}")
                print(f"      Similar a referencia: {post.metadata.get('similar_to_reference', False)}")
                
        else:
            print(f"❌ Error en generación completa: {response.error}")
            
    except Exception as e:
        print(f"❌ Excepción en generación completa: {e}")
    
    print("\n🎯 RESUMEN:")
    print("✅ Análisis de posts de referencia funcional")
    print("✅ Generación de hooks similares implementada")
    print("✅ Generación de contenido similar operativa")
    print("✅ Sistema completo 'Generar más como este' listo")
    print("\n💡 Los usuarios ahora pueden:")
    print("   - Hacer clic en 'Más como este' en cualquier post")
    print("   - Generar 3 posts similares pero únicos")
    print("   - Mantener el estilo y tono del post original")
    print("   - Obtener variaciones inteligentes del contenido")


if __name__ == "__main__":
    asyncio.run(test_generate_similar_posts())
