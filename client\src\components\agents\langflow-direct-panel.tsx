"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, CheckCircle2, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { useAuth } from "@/hooks/use-auth";

interface TweakData {
  [key: string]: any;
}

interface LangFlowDirectPanelProps {
  title?: string;
  description?: string;
  defaultFlowId?: string;
  defaultTweaks?: Record<string, TweakData>;
}

export function LangFlowDirectPanel({
  title = "LangFlow Direct Execution",
  description = "Execute a LangFlow agent directly using the LangFlow API",
  defaultFlowId = "",
  defaultTweaks = {},
}: LangFlowDirectPanelProps) {
  const [flowId, setFlowId] = useState(defaultFlowId);
  const [inputText, setInputText] = useState("");
  const [outputText, setOutputText] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [executionTime, setExecutionTime] = useState<number | null>(null);
  const [enableMemory, setEnableMemory] = useState(true);
  const [sessionId, setSessionId] = useState<string>("");

  const { toast } = useToast();
  const { user } = useAuth();

  // Generar un ID de sesión único para el usuario actual
  useEffect(() => {
    if (user) {
      // Para Firebase auth, usar uid, para el auth local, usar id
      const userId =
        typeof user === "object" && "uid" in user
          ? user.uid
          : typeof user === "object" && "id" in user
            ? String(user.id)
            : null;

      if (userId) {
        // Usar directamente el valor
        setSessionId(userId);
      } else {
        // Si no se puede obtener un ID de usuario válido, generar uno aleatorio
        const anonymousId = `anonymous-${Math.random().toString(36).substring(2, 9)}`;
        setSessionId(anonymousId);
      }
    } else {
      // Si no hay usuario, generar un ID aleatorio
      const anonymousId = `anonymous-${Math.random().toString(36).substring(2, 9)}`;
      setSessionId(anonymousId);
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!flowId.trim()) {
      toast({
        title: "Flow ID requerido",
        description: "Por favor ingresa el ID del flujo a ejecutar",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setSuccess(false);
    setError(null);
    setExecutionTime(null);

    try {
      // Preparar tweaks para la memoria de conversación si está habilitada
      const tweaks = defaultTweaks || {};

      // Buscar componentes de chat en los tweaks y configurar session_id si enableMemory está activado
      if (enableMemory && sessionId) {
        // Iterar sobre los tweaks para encontrar componentes de chat
        Object.keys(tweaks).forEach((key) => {
          // Verificar si es un componente de chat por su nombre (puede variar según la configuración de LangFlow)
          if (
            key.includes("ChatInput") ||
            key.includes("HumanInput") ||
            key.includes("Chat")
          ) {
            const updatedTweak = {
              ...tweaks[key],
              session_id: sessionId,
            };
            tweaks[key] = updatedTweak;
          }
        });
      }

      // Obtener el ID de usuario para el payload
      const userId =
        user && typeof user === "object" && "uid" in user
          ? user.uid
          : user && typeof user === "object" && "id" in user
            ? String(user.id)
            : "anonymous";

      const payload = {
        flow_id: flowId,
        inputs: {
          user_input: inputText,
        },
        tweaks: Object.keys(tweaks).length > 0 ? tweaks : undefined,
        user_id: userId,
        session_id: enableMemory ? sessionId : undefined,
      };

      console.log("Enviando solicitud a LangFlow:", payload);

      const response = await fetch("/api/langflow/run-direct", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.error_message || "Error desconocido al ejecutar el flujo",
        );
      }

      setSuccess(true);

      if (data.data && data.data.output) {
        setOutputText(
          typeof data.data.output === "string"
            ? data.data.output
            : JSON.stringify(data.data.output, null, 2),
        );
      } else {
        setOutputText(JSON.stringify(data.data, null, 2));
      }

      if (data.execution_time) {
        setExecutionTime(data.execution_time);
      }

      toast({
        title: "Flujo ejecutado correctamente",
        description: `Tiempo de ejecución: ${data.execution_time ? data.execution_time.toFixed(2) + "s" : "N/A"}`,
        variant: "default",
      });
    } catch (err: any) {
      setSuccess(false);
      setError(err.message || "Error desconocido");

      toast({
        title: "Error al ejecutar el flujo",
        description: err.message || "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="execution" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="execution">Ejecución</TabsTrigger>
            <TabsTrigger value="results">Resultados</TabsTrigger>
          </TabsList>

          <TabsContent value="execution">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="flow-id">Flow ID</Label>
                <Input
                  id="flow-id"
                  placeholder="Ej: 01234567-89ab-cdef-0123-456789abcdef"
                  value={flowId}
                  onChange={(e) => setFlowId(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="input-text">Entrada</Label>
                <Textarea
                  id="input-text"
                  placeholder="Ingresa tu consulta o texto aquí..."
                  className="min-h-[100px]"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="memory-switch"
                  checked={enableMemory}
                  onCheckedChange={setEnableMemory}
                />
                <Label htmlFor="memory-switch" className="text-sm">
                  Habilitar memoria de conversación (session ID:{" "}
                  {sessionId.substring(0, 8)}...)
                </Label>
              </div>

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Ejecutando...
                  </>
                ) : (
                  "Ejecutar flujo"
                )}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="results">
            <div className="space-y-4">
              {loading && (
                <div className="flex items-center justify-center p-6">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Procesando solicitud...</span>
                </div>
              )}

              {!loading && (success || error) && (
                <div className="space-y-4">
                  <div
                    className={`p-3 rounded-md ${success ? "bg-green-50" : "bg-red-50"} flex items-start`}
                  >
                    {success ? (
                      <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    )}
                    <div>
                      <p
                        className={`font-medium ${success ? "text-green-700" : "text-red-700"}`}
                      >
                        {success ? "Ejecución exitosa" : "Error de ejecución"}
                      </p>
                      {executionTime && success && (
                        <p className="text-sm text-green-600">
                          Tiempo de ejecución: {executionTime.toFixed(2)}{" "}
                          segundos
                        </p>
                      )}
                      {error && <p className="text-sm text-red-600">{error}</p>}
                    </div>
                  </div>

                  {success && (
                    <div className="space-y-2">
                      <Label htmlFor="output-text">Resultado</Label>
                      <Textarea
                        id="output-text"
                        readOnly
                        className="min-h-[200px] font-mono text-sm"
                        value={outputText}
                      />
                    </div>
                  )}
                </div>
              )}

              {!loading && !success && !error && (
                <div className="text-center p-6 text-gray-500">
                  <p>Ejecuta un flujo para ver los resultados aquí</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between text-xs text-gray-500">
        <div>LangFlow API vía acceso directo</div>
      </CardFooter>
    </Card>
  );
}

export default LangFlowDirectPanel;
