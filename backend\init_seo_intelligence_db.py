#!/usr/bin/env python3
"""
Initialize SEO Intelligence Database for Emma Studio SaaS
Creates all necessary tables for storing analysis results and content data
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from sqlalchemy import create_engine
from app.models.seo_intelligence_models import Base
from app.core.config import get_settings

def init_database():
    """Initialize the SEO Intelligence database"""
    print("🚀 Initializing Emma Studio SEO Intelligence Database...")
    
    # Get database URL from settings
    settings = get_settings()
    database_url = settings.DATABASE_URL
    
    print(f"📊 Database URL: {database_url}")
    
    # Create engine
    engine = create_engine(database_url)
    
    # Create all tables
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")
        
        # List created tables
        tables = [
            "seo_analysis_results",
            "saio_analysis_results", 
            "generated_content",
            "keyword_analysis_results",
            "readability_analysis_results",
            "content_optimization_history",
            "api_usage_stats"
        ]
        
        print("\n📋 Created Tables:")
        for table in tables:
            print(f"   ✅ {table}")
        
        print("\n🎯 Database Features:")
        print("   • Real SEO analysis result storage")
        print("   • SAIO/GEO optimization tracking")
        print("   • AI-generated content management")
        print("   • Keyword analysis history")
        print("   • Readability metrics storage")
        print("   • Content optimization tracking")
        print("   • API usage analytics for SaaS billing")
        
        print("\n🔥 Emma Studio SEO Intelligence Database: READY!")
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    success = init_database()
    sys.exit(0 if success else 1)
