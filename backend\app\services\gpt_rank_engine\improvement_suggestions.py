"""
Improvement Suggestions Module
Generates specific improvement suggestions based on analysis results
"""

import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class ImprovementSuggestionGenerator:
    """Generates specific improvement suggestions based on component analysis."""
    
    def __init__(self):
        logger.info("✅ Improvement Suggestion Generator initialized successfully")
    
    def generate_improvement_suggestions(
        self, 
        component_scores: Dict[str, float], 
        content: str, 
        topic: str
    ) -> List[Dict[str, Any]]:
        """
        Generate specific improvement suggestions based on component scores.
        
        Args:
            component_scores: Scores for each component
            content: Original content
            topic: Main topic
            
        Returns:
            List of improvement suggestions
        """
        try:
            suggestions = []
            
            # Analyze each component and suggest improvements
            for component, score in component_scores.items():
                if score < 70:  # Needs improvement
                    suggestion = self._get_component_suggestion(component, score, content, topic)
                    if suggestion:
                        suggestions.append(suggestion)
            
            # Sort by priority (lowest scores first)
            suggestions.sort(key=lambda x: x['current_score'])
            
            # Add general suggestions if overall score is low
            overall_score = sum(component_scores.values()) / len(component_scores)
            if overall_score < 60:
                general_suggestions = self._get_general_improvement_suggestions(content, topic)
                suggestions.extend(general_suggestions)
            
            return suggestions[:8]  # Return top 8 suggestions
            
        except Exception as e:
            logger.error(f"❌ Improvement suggestions generation failed: {str(e)}")
            return []
    
    def _get_component_suggestion(
        self, 
        component: str, 
        score: float, 
        content: str, 
        topic: str
    ) -> Optional[Dict[str, Any]]:
        """Get specific suggestion for a component."""
        suggestions_map = {
            "semantic_similarity": {
                "title": "Mejorar Similitud Semántica",
                "description": "Usa terminología más técnica y estructura similar a fuentes autoritativas",
                "actions": [
                    "Incluye definiciones precisas y técnicas",
                    "Usa lenguaje más formal y académico",
                    "Estructura el contenido como Wikipedia",
                    "Agrega referencias a conceptos establecidos"
                ],
                "examples": [
                    f"Define claramente qué es {topic}",
                    "Usa terminología estándar del campo",
                    "Incluye clasificaciones y categorías"
                ]
            },
            "logical_coherence": {
                "title": "Mejorar Coherencia Lógica",
                "description": "Mejora el flujo y la estructura lógica del contenido",
                "actions": [
                    "Agrega palabras de transición entre párrafos",
                    "Organiza ideas en orden lógico",
                    "Conecta mejor los conceptos",
                    "Crea una progresión clara de ideas"
                ],
                "examples": [
                    "Usa 'además', 'por otro lado', 'sin embargo'",
                    "Estructura: introducción → desarrollo → conclusión",
                    "Cada párrafo debe conectar con el anterior"
                ]
            },
            "authority_signals": {
                "title": "Aumentar Señales de Autoridad",
                "description": "Incluye más indicadores de expertise y credibilidad",
                "actions": [
                    "Agrega datos y estadísticas verificables",
                    "Menciona fuentes confiables y estudios",
                    "Usa lenguaje más autoritativo",
                    "Incluye evidencia científica"
                ],
                "examples": [
                    "Según estudios de [institución]...",
                    "Los datos muestran que...",
                    "La investigación confirma..."
                ]
            },
            "citability_score": {
                "title": "Mejorar Citabilidad",
                "description": "Crea contenido más fácil de citar por IA",
                "actions": [
                    "Incluye definiciones claras y concisas",
                    "Crea declaraciones factuales específicas",
                    "Estructura información en puntos clave",
                    "Evita lenguaje ambiguo"
                ],
                "examples": [
                    f"{topic} se define como...",
                    "Los principales beneficios incluyen...",
                    "Es importante destacar que..."
                ]
            },
            "clarity_score": {
                "title": "Mejorar Claridad",
                "description": "Hace el contenido más claro y legible",
                "actions": [
                    "Simplifica oraciones largas",
                    "Usa párrafos más cortos",
                    "Agrega listas y estructura visual",
                    "Elimina jerga innecesaria"
                ],
                "examples": [
                    "Oraciones de 15-25 palabras",
                    "Párrafos de 3-5 oraciones",
                    "Usa listas numeradas o con viñetas"
                ]
            },
            "completeness_score": {
                "title": "Aumentar Completitud",
                "description": "Cubre más aspectos del tema",
                "actions": [
                    "Responde más preguntas básicas (qué, cómo, por qué)",
                    "Agrega ejemplos prácticos",
                    "Incluye más contexto e información de fondo",
                    "Cubre casos de uso específicos"
                ],
                "examples": [
                    f"¿Qué es {topic}?",
                    f"¿Cómo funciona {topic}?",
                    f"¿Cuándo usar {topic}?"
                ]
            }
        }
        
        if component in suggestions_map:
            suggestion = suggestions_map[component].copy()
            suggestion.update({
                "component": component,
                "current_score": round(score, 1),
                "priority": "high" if score < 50 else "medium" if score < 70 else "low",
                "impact": "high" if component in ["semantic_similarity", "authority_signals"] else "medium"
            })
            return suggestion
        
        return None
    
    def _get_general_improvement_suggestions(self, content: str, topic: str) -> List[Dict[str, Any]]:
        """Get general improvement suggestions for low-scoring content."""
        general_suggestions = []
        
        word_count = len(content.split())
        
        # Content length suggestions
        if word_count < 200:
            general_suggestions.append({
                "title": "Expandir Contenido",
                "description": "El contenido es demasiado corto para ser completo",
                "actions": [
                    "Agrega más detalles y explicaciones",
                    "Incluye ejemplos adicionales",
                    "Desarrolla más los conceptos principales"
                ],
                "component": "general",
                "current_score": 30,
                "priority": "high",
                "impact": "high"
            })
        
        # Structure suggestions
        if '\n\n' not in content:
            general_suggestions.append({
                "title": "Mejorar Estructura",
                "description": "El contenido necesita mejor organización",
                "actions": [
                    "Divide el contenido en párrafos",
                    "Agrega subtítulos si es necesario",
                    "Organiza la información lógicamente"
                ],
                "component": "general",
                "current_score": 35,
                "priority": "medium",
                "impact": "medium"
            })
        
        # Topic focus suggestions
        if topic and topic.lower() not in content.lower():
            general_suggestions.append({
                "title": "Enfocar en el Tema",
                "description": f"El contenido debe centrarse más en {topic}",
                "actions": [
                    f"Menciona {topic} más frecuentemente",
                    f"Relaciona todos los puntos con {topic}",
                    "Mantén el foco en el tema principal"
                ],
                "component": "general",
                "current_score": 25,
                "priority": "high",
                "impact": "high"
            })
        
        return general_suggestions
    
    def prioritize_suggestions(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize suggestions based on impact and current score."""
        try:
            # Define priority weights
            priority_weights = {"high": 3, "medium": 2, "low": 1}
            impact_weights = {"high": 3, "medium": 2, "low": 1}
            
            # Calculate priority score for each suggestion
            for suggestion in suggestions:
                priority_score = priority_weights.get(suggestion.get("priority", "medium"), 2)
                impact_score = impact_weights.get(suggestion.get("impact", "medium"), 2)
                current_score = suggestion.get("current_score", 50)
                
                # Lower current score = higher priority
                score_factor = (100 - current_score) / 100
                
                suggestion["priority_score"] = (priority_score + impact_score) * score_factor
            
            # Sort by priority score (descending)
            return sorted(suggestions, key=lambda x: x.get("priority_score", 0), reverse=True)
            
        except Exception as e:
            logger.error(f"❌ Suggestion prioritization failed: {str(e)}")
            return suggestions
    
    def generate_quick_wins(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate quick win suggestions that are easy to implement."""
        quick_wins = []
        
        for suggestion in suggestions:
            component = suggestion.get("component", "")
            
            # Identify quick wins based on component
            if component == "clarity_score":
                quick_wins.append({
                    "title": "Dividir Oraciones Largas",
                    "description": "Mejora inmediata de claridad",
                    "effort": "low",
                    "impact": "medium",
                    "time_estimate": "5-10 minutos"
                })
            
            elif component == "completeness_score":
                quick_wins.append({
                    "title": "Agregar Ejemplos",
                    "description": "Incluye 1-2 ejemplos prácticos",
                    "effort": "low",
                    "impact": "medium",
                    "time_estimate": "10-15 minutos"
                })
            
            elif component == "semantic_similarity":
                quick_wins.append({
                    "title": "Usar Terminología Técnica",
                    "description": "Reemplaza palabras comunes con términos técnicos",
                    "effort": "medium",
                    "impact": "high",
                    "time_estimate": "15-20 minutos"
                })
        
        return quick_wins[:3]  # Return top 3 quick wins
    
    def generate_action_plan(self, suggestions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a structured action plan based on suggestions."""
        try:
            prioritized_suggestions = self.prioritize_suggestions(suggestions)
            quick_wins = self.generate_quick_wins(suggestions)
            
            # Group suggestions by priority
            high_priority = [s for s in prioritized_suggestions if s.get("priority") == "high"]
            medium_priority = [s for s in prioritized_suggestions if s.get("priority") == "medium"]
            
            action_plan = {
                "immediate_actions": quick_wins,
                "high_priority_improvements": high_priority[:3],
                "medium_priority_improvements": medium_priority[:3],
                "estimated_total_time": self._estimate_total_time(prioritized_suggestions[:6]),
                "expected_score_improvement": self._estimate_score_improvement(prioritized_suggestions[:6])
            }
            
            return action_plan
            
        except Exception as e:
            logger.error(f"❌ Action plan generation failed: {str(e)}")
            return {"error": str(e)}
    
    def _estimate_total_time(self, suggestions: List[Dict[str, Any]]) -> str:
        """Estimate total time needed for improvements."""
        # Simple estimation based on number and type of suggestions
        total_minutes = len(suggestions) * 15  # 15 minutes per suggestion average
        
        if total_minutes < 60:
            return f"{total_minutes} minutos"
        else:
            hours = total_minutes // 60
            minutes = total_minutes % 60
            return f"{hours}h {minutes}m"
    
    def _estimate_score_improvement(self, suggestions: List[Dict[str, Any]]) -> float:
        """Estimate potential score improvement."""
        # Conservative estimate: each suggestion can improve score by 5-15 points
        total_improvement = 0
        
        for suggestion in suggestions:
            current_score = suggestion.get("current_score", 50)
            impact = suggestion.get("impact", "medium")
            
            if impact == "high":
                improvement = min(15, 100 - current_score)
            elif impact == "medium":
                improvement = min(10, 100 - current_score)
            else:
                improvement = min(5, 100 - current_score)
            
            total_improvement += improvement
        
        return min(total_improvement * 0.7, 40)  # Conservative estimate with cap
