"""
Ad Creator Agent Service - Specialized agent for ad creation workflows.
This agent feels like <PERSON> but is focused specifically on ad creation tasks.
"""

import logging
import time
from typing import Dict, Any, List
from app.services.ai_provider_service import AIProviderService

logger = logging.getLogger(__name__)


class AdCreatorAgentService:
    """Specialized agent for ad creation workflows that feels like <PERSON>."""

    def __init__(self):
        """Initialize the ad creator agent with AI capabilities."""
        self.ai_provider = AIProviderService()
        self.agent_name = "Emma Ad Creator"
        self.conversation_memory = {}  # Store conversation history by session
        self.agent_personality = """
        Soy Emma, especialista en creación de anuncios. Estoy entrenada con millones de campañas exitosas de marketing.
        Soy directa, práctica y enfocada en resultados. Mi objetivo es ayudarte a crear anuncios que realmente conviertan.
        Entiendo las especificidades de cada plataforma (Facebook, Instagram, LinkedIn, etc.) y adapto mis sugerencias.
        Siempre respeto los límites de caracteres y soy consciente del contenido ya existente.
        """
        
    async def chat(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Handle chat interactions for ad creation workflows.

        Args:
            message: User's message
            context: Context including platform, current state, etc.

        Returns:
            Dict with response and metadata
        """
        try:
            # Extract context information
            platform = context.get("platform", "general") if context else "general"
            current_prompt = context.get("currentPrompt", "") if context else ""
            task = context.get("task", "general_chat") if context else "general_chat"
            session_id = context.get("sessionId", "default") if context else "default"

            # Extract current field values for context awareness
            current_fields = self._extract_current_fields(context)

            # Update conversation memory
            self._update_conversation_memory(session_id, message, current_fields)

            # Build specialized prompt for ad creation with memory
            system_prompt = self._build_system_prompt(platform, task, current_fields)
            user_prompt = self._build_user_prompt(message, current_prompt, platform, task, session_id)

            # Generate response using AI
            if self.ai_provider.is_ai_available():
                response = await self._generate_ai_response(system_prompt, user_prompt)
                # Ensure we got a real response, not a generic one
                if not response or len(response.strip()) < 10:
                    logger.warning("AI returned empty/short response, using enhanced fallback")
                    response = self._generate_enhanced_fallback_response(message, platform, task, current_fields)
            else:
                response = self._generate_enhanced_fallback_response(message, platform, task, current_fields)

            # Ensure response respects character limits
            response = self._ensure_response_limits(response, message, task)

            # Detect if we should populate form fields
            field_suggestions = self._detect_field_population(message, response, task)

            # Update memory with Emma's response
            self._update_conversation_memory(session_id, response, current_fields, is_emma=True)

            return {
                "response": response,
                "suggestions": self._generate_contextual_suggestions(message, platform, task, current_fields),
                "field_suggestions": field_suggestions,
                "metadata": {
                    "agent": "ad_creator_emma",
                    "platform": platform,
                    "task": task,
                    "timestamp": time.time(),
                    "session_id": session_id
                }
            }

        except Exception as e:
            logger.error(f"Error in ad creator agent chat: {e}")
            return {
                "response": "Lo siento, tuve un problema técnico. ¿Podrías intentar de nuevo?",
                "suggestions": ["Generar headline", "Crear punchline", "Añadir CTA"],
                "field_suggestions": {},
                "metadata": {"error": str(e)}
            }

    async def intelligent_chat(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Intelligent chat method that replaces the "pendejo" hardcoded responses.
        Uses advanced AI with conversation memory and business intelligence.
        """
        try:
            # Extract enhanced context
            platform = context.get("platform", "general") if context else "general"
            current_prompt = context.get("currentPrompt", "") if context else ""
            conversation_history = context.get("conversationHistory", []) if context else []
            session_id = context.get("sessionId", f"intelligent_{int(time.time())}") if context else f"intelligent_{int(time.time())}"

            # Build intelligent system prompt
            intelligent_system_prompt = self._build_intelligent_system_prompt(platform, conversation_history)

            # Build contextual user prompt with business analysis
            intelligent_user_prompt = self._build_intelligent_user_prompt(
                message, current_prompt, platform, conversation_history
            )

            # Generate intelligent response
            if self.ai_provider.is_ai_available():
                response = await self._generate_intelligent_ai_response(
                    intelligent_system_prompt, intelligent_user_prompt
                )

                # Validate response quality
                if not response or len(response.strip()) < 15:
                    logger.warning("AI returned poor quality response, using smart fallback")
                    response = self._generate_smart_business_response(message, platform, current_prompt)
            else:
                response = self._generate_smart_business_response(message, platform, current_prompt)

            # Generate intelligent suggestions
            intelligent_suggestions = self._generate_intelligent_suggestions(
                message, platform, current_prompt, response
            )

            # Update conversation memory
            self._update_conversation_memory(session_id, message, {"prompt": current_prompt})
            self._update_conversation_memory(session_id, response, {"prompt": current_prompt}, is_emma=True)

            return {
                "response": response,
                "suggestions": intelligent_suggestions,
                "field_suggestions": self._detect_intelligent_field_suggestions(message, response),
                "metadata": {
                    "agent": "emma_intelligent",
                    "platform": platform,
                    "mode": "intelligent_chat",
                    "timestamp": time.time(),
                    "session_id": session_id
                }
            }

        except Exception as e:
            logger.error(f"Error in intelligent chat: {e}")
            # Smart fallback instead of generic error
            return await self.generate_smart_fallback(message, context)

    async def generate_smart_fallback(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate smart fallback response when AI fails.
        Much better than the old hardcoded responses.
        """
        try:
            platform = context.get("platform", "general") if context else "general"
            current_prompt = context.get("currentPrompt", "") if context else ""

            # Analyze user intent
            intent = self._analyze_user_intent(message)
            business_type = self._analyze_business_type(current_prompt + " " + message)

            # Generate contextual response
            response = self._generate_contextual_smart_response(intent, business_type, platform, message)

            # Generate smart suggestions
            suggestions = self._generate_smart_suggestions_for_intent(intent, business_type, platform)

            return {
                "response": response,
                "suggestions": suggestions,
                "field_suggestions": {},
                "metadata": {
                    "agent": "emma_smart_fallback",
                    "platform": platform,
                    "fallback": True,
                    "intent": intent,
                    "business_type": business_type
                }
            }

        except Exception as e:
            logger.error(f"Error in smart fallback: {e}")
            return {
                "response": "¡Hola! Soy Emma, tu asistente inteligente de anuncios. Estoy aquí para ayudarte a crear contenido que realmente convierte. ¿En qué puedo ayudarte?",
                "suggestions": ["Generar prompt optimizado", "Crear headline llamativo", "Sugerir CTA efectivo"],
                "field_suggestions": {},
                "metadata": {"error": str(e), "fallback": True}
            }
    
    def _build_system_prompt(self, platform: str, task: str, current_fields: Dict[str, str]) -> str:
        """Build the system prompt for the ad creator agent."""

        # Build context about existing fields
        field_context = ""
        if current_fields:
            filled_fields = [f"- {field}: '{value}'" for field, value in current_fields.items() if value.strip()]
            if filled_fields:
                field_context = f"\n\nCAMPOS YA COMPLETADOS:\n" + "\n".join(filled_fields)

        return f"""
        {self.agent_personality}

        CONTEXTO ACTUAL:
        - Plataforma: {platform}
        - Tarea: {task}
        {field_context}

        LÍMITES ESTRICTOS DE CARACTERES:
        - Headlines: MÁXIMO 40 caracteres (incluye espacios)
        - Punchlines: MÁXIMO 40 caracteres (incluye espacios)
        - CTAs: MÁXIMO 25 caracteres (incluye espacios)
        - Respuestas de chat: MÁXIMO 200 caracteres

        CAPACIDADES ESPECIALES:
        - Genero headlines de máximo 40 caracteres optimizados para {platform}
        - Creo punchlines persuasivos de máximo 40 caracteres
        - Diseño CTAs efectivos de máximo 25 caracteres
        - Optimizo descripciones de productos para anuncios
        - Entiendo las mejores prácticas para cada plataforma
        - Soy consciente del contenido ya existente y no lo repito

        INSTRUCCIONES CRÍTICAS:
        - GENERA contenido específico, NO des consejos genéricos
        - Si el usuario menciona "headline", "título", "encabezado" → GENERA un headline inmediatamente
        - Si el usuario menciona "punchline", "subtítulo", "descripción" → GENERA un punchline inmediatamente
        - Si el usuario menciona "CTA", "botón", "call to action" → GENERA un CTA inmediatamente
        - Si el usuario dice "ayuda", "genera", "crea" → GENERA el contenido que falta automáticamente
        - DETECTA la industria del producto y adapta el lenguaje (suplementos, tech, moda, etc.)
        - SIEMPRE respeta los límites de caracteres
        - NO repitas contenido que ya existe
        - Respuestas de chat máximo 2-3 frases cortas
        """
    
    def _build_user_prompt(self, message: str, current_prompt: str, platform: str, task: str, session_id: str) -> str:
        """Build the user prompt with context and conversation history."""
        context_info = ""
        if current_prompt:
            context_info = f"\nDESCRIPCIÓN DEL PRODUCTO: {current_prompt}"

        # Add conversation history for context
        history_context = ""
        if session_id in self.conversation_memory:
            recent_messages = self.conversation_memory[session_id][-4:]  # Last 4 messages
            if recent_messages:
                history_context = "\n\nCONVERSACIÓN RECIENTE:"
                for msg in recent_messages:
                    sender = "Usuario" if not msg.get("is_emma") else "Emma"
                    history_context += f"\n{sender}: {msg['message'][:100]}"

        # Detect industry automatically
        industry_context = self._detect_industry(current_prompt + " " + message)

        return f"""
        MENSAJE DEL USUARIO: {message}
        {context_info}
        {history_context}
        {industry_context}

        PLATAFORMA: {platform}
        TAREA: {task}

        Responde como Emma, especialista en anuncios para {platform}.
        IMPORTANTE: Mantén tu respuesta corta y específica.
        Si el usuario pide generar contenido, hazlo directamente sin explicaciones.
        """
    
    async def _generate_ai_response(self, system_prompt: str, user_prompt: str) -> str:
        """Generate AI response using the AI provider with enhanced context."""
        try:
            # Enhanced prompt combination with clear instructions
            full_prompt = f"""
{system_prompt}

INSTRUCCIONES CRÍTICAS PARA EMMA:
- Analiza cuidadosamente el contexto del producto mencionado
- Genera contenido específico y relevante para ESE producto exacto
- NO uses respuestas genéricas o plantillas
- Respeta estrictamente los límites de caracteres
- Enfócate en beneficios reales del producto descrito

{user_prompt}

RECORDATORIO: Tu respuesta debe ser específica al producto mencionado, no una respuesta genérica de plantilla.
"""

            response = await self.ai_provider.generate_content(full_prompt)
            cleaned_response = response.strip().replace('"', '').replace("'", "")

            # Log the response for debugging
            logger.info(f"Emma AI generated: {cleaned_response[:100]}...")

            return cleaned_response
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            raise
    
    def _extract_current_fields(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Extract current field values from context."""
        if not context:
            return {}

        current_fields = {}
        field_mappings = {
            "headline": ["headline", "title", "titulo"],
            "punchline": ["punchline", "subtitle", "subtitulo"],
            "cta": ["cta", "callToAction", "button"],
            "description": ["description", "prompt", "currentPrompt"]
        }

        for field_name, possible_keys in field_mappings.items():
            for key in possible_keys:
                if key in context and context[key]:
                    current_fields[field_name] = str(context[key])
                    break

        return current_fields

    def _detect_industry(self, text: str) -> str:
        """Detect the industry/niche from the product description."""
        text_lower = text.lower()

        # Industry keywords mapping
        industries = {
            "suplementos": [
                "suplemento", "proteina", "whey", "creatina", "vitamina", "fitness", "gym",
                "pre-workout", "post-workout", "aminoacidos", "bcaa", "quemador", "grasa",
                "masa muscular", "musculo", "entrenamiento", "nutricion", "deportivo",
                "dolor", "cabeza", "headache", "migrana", "alivio", "pastilla", "capsula",
                "medicina", "natural", "herbal", "salud", "bienestar", "wellness", "energia",
                "fatiga", "cansancio", "estres", "ansiedad", "dormir", "sueño", "inmunidad",
                "defensas", "articulaciones", "huesos", "corazon", "cerebro", "memoria",
                "antiinflamatorio", "analgesico", "ibuprofeno", "paracetamol", "aspirina"
            ],
            "tecnologia": [
                "app", "software", "tech", "digital", "tecnologia", "sistema", "plataforma",
                "web", "mobile", "saas", "api", "desarrollo", "programacion", "codigo"
            ],
            "moda": [
                "ropa", "fashion", "moda", "vestido", "zapatos", "accesorios", "estilo",
                "tendencia", "outfit", "look", "diseño", "marca", "boutique"
            ],
            "comida": [
                "restaurante", "comida", "food", "cocina", "chef", "receta", "plato",
                "gastronomia", "delivery", "menu", "ingredientes", "sabor"
            ],
            "servicios": [
                "servicio", "consulta", "asesor", "profesional", "consultoria", "coaching",
                "capacitacion", "curso", "entrenamiento", "mentoria"
            ],
            "belleza": [
                "belleza", "cosmetico", "skincare", "maquillaje", "crema", "tratamiento",
                "spa", "estetica", "cuidado", "piel", "cabello"
            ],
            "salud": [
                "salud", "medico", "clinica", "hospital", "tratamiento", "terapia",
                "medicina", "doctor", "paciente", "diagnostico"
            ]
        }

        for industry, keywords in industries.items():
            if any(keyword in text_lower for keyword in keywords):
                return f"\n\nINDUSTRIA DETECTADA: {industry.upper()}"

        return "\n\nINDUSTRIA DETECTADA: GENERAL"

    def _get_industry_examples(self, industry: str, field_type: str) -> Dict[str, List[str]]:
        """Get industry-specific examples for field generation."""
        examples = {
            "SUPLEMENTOS": {
                "headline": [
                    "Alivio Natural del Dolor (23 chars)",
                    "Dolor de Cabeza? Resuelto (24 chars)",
                    "Suplemento Anti-Dolor (20 chars)",
                    "Bienestar Garantizado (20 chars)",
                    "Proteína Premium 100% (20 chars)",
                    "Energía Pre-Workout (19 chars)",
                    "Músculos Más Fuertes (20 chars)",
                    "Salud Natural (12 chars)"
                ],
                "punchline": [
                    "Sin Dolor, Más Vida (18 chars)",
                    "Alivio Inmediato (15 chars)",
                    "Salud Natural (12 chars)",
                    "Bienestar Total (14 chars)",
                    "Resultados Garantizados (22 chars)",
                    "Transforma tu Cuerpo (19 chars)",
                    "Máximo Rendimiento (18 chars)",
                    "Tu Mejor Versión (15 chars)"
                ],
                "cta": [
                    "Alivia Ahora (12 chars)",
                    "Siente Alivio (13 chars)",
                    "Mejora Hoy (10 chars)",
                    "Prueba Gratis (13 chars)",
                    "Compra Ahora (12 chars)",
                    "Ordena Ya (9 chars)",
                    "Obtén Resultados (15 chars)",
                    "Transforma (10 chars)"
                ]
            },
            "TECNOLOGIA": {
                "headline": [
                    "Innovación Digital (18 chars)",
                    "Tech del Futuro (15 chars)",
                    "Solución Inteligente (20 chars)",
                    "App Revolucionaria (18 chars)",
                    "Tecnología Avanzada (19 chars)"
                ],
                "punchline": [
                    "Simplifica tu Vida (18 chars)",
                    "Eficiencia Máxima (17 chars)",
                    "Conecta sin Límites (19 chars)",
                    "Automatiza Todo (15 chars)",
                    "Futuro en tus Manos (19 chars)"
                ],
                "cta": [
                    "Descarga App (12 chars)",
                    "Prueba Demo (11 chars)",
                    "Regístrate (10 chars)",
                    "Empieza Gratis (14 chars)",
                    "Ver Demo (8 chars)"
                ]
            },
            "GENERAL": {
                "headline": [
                    "Calidad Superior (16 chars)",
                    "Oferta Especial (15 chars)",
                    "Lo Mejor para Ti (16 chars)",
                    "Descubre Más (12 chars)",
                    "Excelencia Total (16 chars)"
                ],
                "punchline": [
                    "Tu Mejor Elección (17 chars)",
                    "Calidad Garantizada (19 chars)",
                    "Resultados Reales (17 chars)",
                    "Confianza Total (15 chars)",
                    "Satisfacción 100% (17 chars)"
                ],
                "cta": [
                    "Compra Ahora (12 chars)",
                    "Ver Más (7 chars)",
                    "Obtener (7 chars)",
                    "Descubre (9 chars)",
                    "Conoce Más (10 chars)"
                ]
            }
        }

        industry_key = industry if industry in examples else "GENERAL"
        return examples[industry_key].get(field_type, examples["GENERAL"][field_type])

    def _update_conversation_memory(self, session_id: str, message: str, current_fields: Dict[str, str], is_emma: bool = False):
        """Update conversation memory for context awareness."""
        if session_id not in self.conversation_memory:
            self.conversation_memory[session_id] = []

        # Keep only last 10 messages to avoid memory bloat
        if len(self.conversation_memory[session_id]) >= 10:
            self.conversation_memory[session_id] = self.conversation_memory[session_id][-8:]

        self.conversation_memory[session_id].append({
            "message": message,
            "timestamp": time.time(),
            "fields": current_fields.copy(),
            "is_emma": is_emma
        })

    def _ensure_response_limits(self, response: str, message: str, task: str) -> str:
        """Ensure response respects character limits based on context."""
        message_lower = message.lower()

        # If user is asking for specific field generation, enforce strict limits
        if task == "generate_headline" or "headline" in message_lower or "título" in message_lower:
            if len(response) > 40:
                # Try to extract the actual headline from a longer response
                lines = response.split('\n')
                for line in lines:
                    clean_line = line.strip().strip('"\'').strip()
                    if clean_line and len(clean_line) <= 40 and not clean_line.endswith('.'):
                        return clean_line
                # If no suitable line found, truncate
                return response[:37] + "..."

        elif task == "generate_punchline" or "punchline" in message_lower or "subtítulo" in message_lower:
            if len(response) > 40:
                lines = response.split('\n')
                for line in lines:
                    clean_line = line.strip().strip('"\'').strip()
                    if clean_line and len(clean_line) <= 40 and not clean_line.endswith('.'):
                        return clean_line
                return response[:37] + "..."

        elif task == "generate_cta" or "cta" in message_lower or "botón" in message_lower:
            if len(response) > 25:
                lines = response.split('\n')
                for line in lines:
                    clean_line = line.strip().strip('"\'').strip()
                    if clean_line and len(clean_line) <= 25 and not clean_line.endswith('.'):
                        return clean_line
                return response[:22] + "..."

        # For general chat, limit to 200 characters
        elif len(response) > 200:
            return response[:197] + "..."

        return response

    def _build_intelligent_system_prompt(self, platform: str, conversation_history: List[Dict]) -> str:
        """Build intelligent system prompt with conversation awareness."""
        history_context = ""
        if conversation_history:
            recent_history = conversation_history[-3:]  # Last 3 exchanges
            history_context = "\n\nCONTEXTO DE CONVERSACIÓN:"
            for msg in recent_history:
                role = msg.get('role', 'user')
                content = msg.get('content', '')[:100]
                history_context += f"\n{role}: {content}"

        return f"""
        Soy Emma, la asistente de marketing más inteligente del mundo. Tengo experiencia analizando millones de campañas exitosas.

        PERSONALIDAD INTELIGENTE:
        - Analizo el negocio del usuario automáticamente
        - Genero contenido específico, no plantillas genéricas
        - Entiendo el contexto y la conversación previa
        - Soy directa, práctica y enfocada en resultados
        - Adapto mi lenguaje al tipo de negocio detectado

        CAPACIDADES AVANZADAS:
        - Detección automática de industria (suplementos, tech, moda, etc.)
        - Análisis de intención del usuario
        - Generación contextual de headlines, punchlines y CTAs
        - Optimización específica para {platform}
        - Memoria de conversación para coherencia

        PLATAFORMA ACTUAL: {platform}
        {history_context}

        INSTRUCCIONES CRÍTICAS:
        - Analiza el contexto del producto mencionado
        - Genera respuestas específicas al negocio del usuario
        - Mantén coherencia con la conversación previa
        - Sé concisa pero útil (máximo 150 caracteres para chat)
        - Si detectas una solicitud específica, actúa inmediatamente
        """

    def _build_intelligent_user_prompt(self, message: str, current_prompt: str, platform: str, conversation_history: List[Dict]) -> str:
        """Build intelligent user prompt with business context."""
        business_analysis = self._analyze_business_type(current_prompt + " " + message)
        intent_analysis = self._analyze_user_intent(message)

        context_info = ""
        if current_prompt:
            context_info = f"\nPRODUCTO/SERVICIO: {current_prompt}"

        return f"""
        MENSAJE ACTUAL: {message}
        {context_info}

        ANÁLISIS AUTOMÁTICO:
        - Tipo de negocio: {business_analysis}
        - Intención detectada: {intent_analysis}
        - Plataforma: {platform}

        Como Emma, responde de manera inteligente y específica al contexto detectado.
        Si el usuario pide generar algo específico, hazlo directamente.
        """

    async def _generate_intelligent_ai_response(self, system_prompt: str, user_prompt: str) -> str:
        """Generate intelligent AI response with enhanced prompting."""
        try:
            enhanced_prompt = f"""
            {system_prompt}

            MODO INTELIGENTE ACTIVADO:
            - Analiza cuidadosamente el contexto del negocio
            - Genera contenido específico y relevante
            - Evita respuestas genéricas o de plantilla
            - Mantén coherencia con la conversación
            - Responde como una experta en marketing

            {user_prompt}

            RECORDATORIO: Sé específica, inteligente y útil. No uses respuestas genéricas.
            """

            response = await self.ai_provider.generate_content(enhanced_prompt)
            cleaned_response = response.strip().replace('"', '').replace("'", "")

            logger.info(f"Emma intelligent response: {cleaned_response[:100]}...")
            return cleaned_response

        except Exception as e:
            logger.error(f"Error in intelligent AI response: {e}")
            raise

    def _analyze_user_intent(self, message: str) -> str:
        """Analyze user intent from message."""
        message_lower = message.lower()

        if any(word in message_lower for word in ['headline', 'título', 'encabezado']):
            return "generar_headline"
        elif any(word in message_lower for word in ['punchline', 'subtítulo', 'descripción']):
            return "generar_punchline"
        elif any(word in message_lower for word in ['cta', 'botón', 'call to action']):
            return "generar_cta"
        elif any(word in message_lower for word in ['prompt', 'descripción', 'crear', 'generar']):
            return "generar_prompt"
        elif any(word in message_lower for word in ['ayuda', 'help', 'como', 'qué']):
            return "solicitar_ayuda"
        elif any(word in message_lower for word in ['optimizar', 'mejorar', 'mejor']):
            return "optimizar_contenido"
        else:
            return "conversacion_general"

    def _analyze_business_type(self, text: str) -> str:
        """Analyze business type from text with enhanced detection."""
        text_lower = text.lower()

        # Enhanced business patterns
        business_patterns = {
            "suplementos_salud": [
                "suplemento", "proteina", "vitamina", "dolor", "cabeza", "migrana", "alivio",
                "pastilla", "capsula", "medicina", "natural", "salud", "bienestar", "energia",
                "fatiga", "articulaciones", "inmunidad", "antiinflamatorio", "analgesico"
            ],
            "fitness_deportes": [
                "fitness", "gym", "entrenamiento", "musculo", "masa", "pre-workout", "post-workout",
                "creatina", "whey", "bcaa", "quemador", "grasa", "deportivo", "atleta"
            ],
            "tecnologia": [
                "app", "software", "tech", "digital", "tecnologia", "sistema", "plataforma",
                "web", "mobile", "saas", "api", "desarrollo", "programacion"
            ],
            "moda_belleza": [
                "ropa", "fashion", "moda", "vestido", "zapatos", "belleza", "cosmetico",
                "skincare", "maquillaje", "estilo", "tendencia"
            ],
            "comida_restaurante": [
                "restaurante", "comida", "food", "cocina", "chef", "delivery", "gastronomia",
                "plato", "menu", "ingredientes"
            ],
            "servicios_profesionales": [
                "servicio", "consulta", "asesor", "profesional", "consultoria", "coaching",
                "capacitacion", "curso", "mentoria"
            ]
        }

        for business_type, keywords in business_patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                return business_type

        return "general"

    def _generate_smart_business_response(self, message: str, platform: str, current_prompt: str) -> str:
        """Generate smart business-specific response."""
        business_type = self._analyze_business_type(current_prompt + " " + message)
        intent = self._analyze_user_intent(message)

        # Business-specific responses
        business_responses = {
            "suplementos_salud": {
                "generar_headline": "Alivio Natural Garantizado",
                "generar_punchline": "Sin Dolor, Más Vida",
                "generar_cta": "Alivia Ahora",
                "conversacion_general": f"Perfecto, veo que trabajas con suplementos para la salud. En {platform} los anuncios de salud funcionan mejor mostrando beneficios específicos y testimonios reales. ¿Qué necesitas generar?"
            },
            "fitness_deportes": {
                "generar_headline": "Transforma Tu Cuerpo",
                "generar_punchline": "Resultados Garantizados",
                "generar_cta": "Entrena Ya",
                "conversacion_general": f"Excelente, el nicho fitness es muy rentable en {platform}. Los anuncios que mejor funcionan muestran transformaciones y resultados. ¿Te ayudo a crear contenido específico?"
            },
            "tecnologia": {
                "generar_headline": "Innovación Digital",
                "generar_punchline": "Simplifica tu Vida",
                "generar_cta": "Prueba Gratis",
                "conversacion_general": f"Genial, tech es un sector muy competitivo en {platform}. Necesitas destacar tu propuesta de valor única. ¿Qué aspecto de tu solución quieres promocionar?"
            },
            "general": {
                "generar_headline": "Descubre la Diferencia",
                "generar_punchline": "Calidad que Convence",
                "generar_cta": "Conoce Más",
                "conversacion_general": f"¡Hola! Soy Emma, especialista en anuncios para {platform}. Analizo tu negocio y genero contenido que realmente convierte. ¿En qué puedo ayudarte?"
            }
        }

        responses = business_responses.get(business_type, business_responses["general"])
        return responses.get(intent, responses["conversacion_general"])

    def _generate_intelligent_suggestions(self, message: str, platform: str, current_prompt: str, response: str) -> List[str]:
        """Generate intelligent suggestions based on context."""
        business_type = self._analyze_business_type(current_prompt + " " + message)
        # Note: platform, response parameters available for future use

        # Business-specific suggestions
        business_suggestions = {
            "suplementos_salud": [
                "Crear anuncio antes/después",
                "Destacar beneficios específicos",
                "Añadir testimonios reales",
                "Optimizar para conversión"
            ],
            "fitness_deportes": [
                "Mostrar transformaciones",
                "Destacar resultados rápidos",
                "Crear urgencia limitada",
                "Optimizar para fitness"
            ],
            "tecnologia": [
                "Destacar innovación",
                "Mostrar casos de uso",
                "Crear demo interactivo",
                "Optimizar para B2B"
            ],
            "general": [
                "Generar prompt optimizado",
                "Crear headline llamativo",
                "Sugerir CTA efectivo",
                "Optimizar para conversión"
            ]
        }

        return business_suggestions.get(business_type, business_suggestions["general"])

    def _generate_contextual_smart_response(self, intent: str, business_type: str, platform: str, message: str) -> str:
        """Generate contextual smart response for fallback."""
        # Note: message parameter available for future context analysis
        responses = {
            "generar_headline": f"Perfecto! Basándome en tu negocio de {business_type}, te sugiero un headline optimizado para {platform}. ¿Quieres que lo genere ahora?",
            "generar_punchline": f"Excelente! Para {business_type} en {platform}, puedo crear un punchline que realmente conecte con tu audiencia. ¿Procedemos?",
            "generar_cta": f"Genial! Un CTA efectivo es clave para {business_type} en {platform}. Te ayudo a crear uno que genere acción inmediata.",
            "solicitar_ayuda": f"¡Hola! Soy Emma, especialista en {platform}. Veo que trabajas con {business_type}. Puedo ayudarte a crear contenido que realmente convierte. ¿Qué necesitas?",
            "conversacion_general": f"Entiendo que quieres optimizar tu estrategia de {business_type} para {platform}. Estoy aquí para ayudarte a crear anuncios que generen resultados reales."
        }

        return responses.get(intent, responses["conversacion_general"])

    def _generate_smart_suggestions_for_intent(self, intent: str, business_type: str, platform: str) -> List[str]:
        """Generate smart suggestions based on intent and business type."""
        # Note: business_type, platform parameters available for future customization
        base_suggestions = {
            "generar_headline": ["Generar headline optimizado", "Crear variaciones", "Optimizar para CTR"],
            "generar_punchline": ["Generar punchline persuasivo", "Crear variaciones", "Optimizar para engagement"],
            "generar_cta": ["Generar CTA efectivo", "Crear variaciones", "Optimizar para conversión"],
            "solicitar_ayuda": ["Generar prompt completo", "Crear headline", "Sugerir estrategia"],
            "conversacion_general": ["Analizar competencia", "Optimizar estrategia", "Generar contenido"]
        }

        return base_suggestions.get(intent, base_suggestions["conversacion_general"])

    def _detect_intelligent_field_suggestions(self, message: str, response: str) -> Dict[str, str]:
        """Detect intelligent field suggestions from response."""
        field_suggestions = {}
        intent = self._analyze_user_intent(message)
        clean_response = response.strip().strip('"\'').strip()

        # Smart field detection based on intent and response characteristics
        if intent == "generar_headline" and len(clean_response) <= 40:
            field_suggestions["headline"] = clean_response
        elif intent == "generar_punchline" and len(clean_response) <= 40:
            field_suggestions["punchline"] = clean_response
        elif intent == "generar_cta" and len(clean_response) <= 25:
            field_suggestions["cta"] = clean_response

        return field_suggestions

    def _generate_fallback_response(self, message: str, platform: str, task: str) -> str:
        """Generate fallback response when AI is not available."""
        message_lower = message.lower()

        # Use task parameter to provide more specific responses
        if task == "generate_headline" or "headline" in message_lower or "título" in message_lower:
            return f"Perfecto, puedo ayudarte a crear un headline impactante para {platform}. Necesito que me des más detalles sobre tu producto para generar algo específico y efectivo."

        elif task == "generate_punchline" or "punchline" in message_lower or "subtítulo" in message_lower:
            return f"Excelente, vamos a crear un punchline persuasivo para {platform}. Con más información sobre tu producto, puedo generar algo que realmente conecte con tu audiencia."

        elif task == "generate_cta" or "cta" in message_lower or "botón" in message_lower:
            return f"Genial, un buen CTA es clave para {platform}. Cuéntame más sobre la acción que quieres que tome tu audiencia y crearemos algo irresistible."

        else:
            return f"¡Hola! Soy Emma, especialista en anuncios para {platform}. Puedo ayudarte a generar headlines, punchlines, CTAs y optimizar tu descripción. ¿En qué te ayudo específicamente?"

    def _generate_enhanced_fallback_response(self, message: str, platform: str, task: str, current_fields: Dict[str, str]) -> str:
        """Generate enhanced fallback response with context awareness."""
        message_lower = message.lower()

        # Detect product context from message and current fields for logging
        if current_fields.get("description"):
            logger.info(f"Product context detected: {current_fields['description'][:50]}...")
        elif "suplemento" in message_lower or "dolor" in message_lower or "cabeza" in message_lower:
            logger.info("Product context detected: suplemento para dolor de cabeza")
        elif "proteina" in message_lower or "fitness" in message_lower:
            logger.info("Product context detected: suplemento fitness")

        # Industry-specific responses
        if "suplemento" in message_lower or "dolor" in message_lower or "salud" in message_lower:
            if task == "generate_headline" or "headline" in message_lower:
                return "Alivio Natural del Dolor"
            elif task == "generate_punchline" or "punchline" in message_lower:
                return "Sin Dolor, Más Vida"
            elif task == "generate_cta" or "cta" in message_lower:
                return "Alivia Ahora"
            else:
                return f"Perfecto, veo que trabajas con suplementos para la salud. Puedo ayudarte a crear contenido específico que conecte con personas que buscan alivio natural. ¿Qué necesitas generar?"

        # Default enhanced responses
        if task == "generate_headline" or "headline" in message_lower:
            return "Descubre la Diferencia"
        elif task == "generate_punchline" or "punchline" in message_lower:
            return "Calidad que Convence"
        elif task == "generate_cta" or "cta" in message_lower:
            return "Conoce Más"
        else:
            return f"¡Hola! Soy Emma, especialista en anuncios para {platform}. Analizo tu producto y genero contenido específico que realmente convierte. ¿Qué necesitas crear?"
    
    def _detect_field_population(self, message: str, response: str, task: str) -> Dict[str, str]:
        """Detect if we should populate specific form fields."""
        field_suggestions = {}
        message_lower = message.lower()

        # Clean response for field detection
        clean_response = response.strip().strip('"\'').strip()

        # More aggressive detection - check for any mention of field types
        headline_triggers = ["headline", "título", "encabezado", "title", "header"]
        punchline_triggers = ["punchline", "subtítulo", "descripción", "subtitle", "tagline"]
        cta_triggers = ["cta", "botón", "call to action", "button", "acción"]

        # Check if user is asking for specific field generation
        if (task == "generate_headline" or
            any(trigger in message_lower for trigger in headline_triggers)):
            # Extract headline from response if it looks like one
            if len(clean_response) <= 40 and not clean_response.endswith('.') and not clean_response.endswith('?'):
                field_suggestions["headline"] = clean_response
            else:
                # Try to extract from multi-line response
                lines = response.split('\n')
                for line in lines:
                    clean_line = line.strip().strip('"\'').strip()
                    if clean_line and len(clean_line) <= 40 and not clean_line.endswith('.') and not clean_line.endswith('?'):
                        field_suggestions["headline"] = clean_line
                        break

        elif (task == "generate_punchline" or
              any(trigger in message_lower for trigger in punchline_triggers)):
            if len(clean_response) <= 40 and not clean_response.endswith('.') and not clean_response.endswith('?'):
                field_suggestions["punchline"] = clean_response
            else:
                lines = response.split('\n')
                for line in lines:
                    clean_line = line.strip().strip('"\'').strip()
                    if clean_line and len(clean_line) <= 40 and not clean_line.endswith('.') and not clean_line.endswith('?'):
                        field_suggestions["punchline"] = clean_line
                        break

        elif (task == "generate_cta" or
              any(trigger in message_lower for trigger in cta_triggers)):
            if len(clean_response) <= 25 and not clean_response.endswith('.') and not clean_response.endswith('?'):
                field_suggestions["cta"] = clean_response
            else:
                lines = response.split('\n')
                for line in lines:
                    clean_line = line.strip().strip('"\'').strip()
                    if clean_line and len(clean_line) <= 25 and not clean_line.endswith('.') and not clean_line.endswith('?'):
                        field_suggestions["cta"] = clean_line
                        break

        # Auto-generate missing fields if user asks for general help
        help_triggers = ["ayuda", "genera", "crea", "help", "create", "generate"]
        if any(trigger in message_lower for trigger in help_triggers):
            # This will be handled by the frontend to call specific generation endpoints
            pass

        return field_suggestions
    
    def _generate_contextual_suggestions(self, message: str, platform: str, task: str, current_fields: Dict[str, str]) -> List[str]:
        """Generate contextual suggestions based on the conversation and current state."""
        message_lower = message.lower()
        suggestions = []

        # Task-specific suggestions
        if task == "generate_headline" or "headline" in message_lower:
            suggestions = [f"Generar otro headline para {platform}", "Crear variación del headline", "Optimizar headline actual"]
        elif task == "generate_punchline" or "punchline" in message_lower:
            suggestions = [f"Generar otro punchline para {platform}", "Crear variación del punchline", "Optimizar punchline actual"]
        elif task == "generate_cta" or "cta" in message_lower:
            suggestions = [f"Generar otro CTA para {platform}", "Crear variación del CTA", "Optimizar CTA actual"]
        else:
            # Smart suggestions based on what's missing
            missing_fields = []
            if not current_fields.get("headline"):
                missing_fields.append("Generar headline atractivo")
            if not current_fields.get("punchline"):
                missing_fields.append("Crear punchline persuasivo")
            if not current_fields.get("cta"):
                missing_fields.append("Añadir CTA efectivo")

            if missing_fields:
                suggestions = missing_fields
            else:
                # All fields filled, suggest optimizations
                suggestions = [
                    f"Optimizar para {platform}",
                    "Mejorar conversión",
                    "Crear variaciones",
                    "Analizar competencia"
                ]

        # Platform-specific suggestions if no task-specific ones
        if not suggestions:
            platform_suggestions = {
                "facebook": [
                    "Crear headline viral",
                    "Punchline para feed",
                    "CTA de engagement",
                    "Optimizar para Facebook"
                ],
                "instagram": [
                    "Headline estético",
                    "Punchline visual",
                    "CTA para stories",
                    "Optimizar para Instagram"
                ],
                "linkedin": [
                    "Headline profesional",
                    "Punchline B2B",
                    "CTA corporativo",
                    "Optimizar para LinkedIn"
                ]
            }

            # Default suggestions
            base_suggestions = [
                "Generar headline atractivo",
                "Crear punchline persuasivo",
                "Añadir CTA efectivo",
                "Optimizar descripción"
            ]

            suggestions = platform_suggestions.get(platform, base_suggestions)

        return suggestions[:4]  # Limit to 4 suggestions
    
    async def generate_specific_field(self, field_type: str, context: Dict[str, Any]) -> str:
        """
        Generate specific field content (headline, punchline, CTA).
        
        Args:
            field_type: Type of field to generate (headline, punchline, cta)
            context: Context including platform, product description, etc.
            
        Returns:
            Generated content for the field
        """
        try:
            platform = context.get("platform", "general")
            product_description = context.get("productDescription", "")

            # Detect industry for specialized prompts
            industry_info = self._detect_industry(product_description)
            industry = industry_info.split(": ")[-1].strip() if ": " in industry_info else "GENERAL"

            # Industry-specific examples and guidelines
            industry_examples = self._get_industry_examples(industry, field_type)

            prompts = {
                "headline": f"""
                Eres Emma, especialista en anuncios con experiencia en millones de campañas exitosas.

                ANÁLISIS DEL PRODUCTO:
                Descripción: {product_description}
                Industria detectada: {industry}
                Plataforma objetivo: {platform}

                TAREA: Genera UN headline específico y relevante para este producto exacto.

                REGLAS OBLIGATORIAS:
                - MÁXIMO 40 caracteres (cuenta cada letra y espacio)
                - Debe ser específico al producto descrito, NO genérico
                - Enfócate en el beneficio principal que ofrece este producto
                - Usa palabras que conecten emocionalmente con la audiencia
                - Sin puntos finales, sin comillas, sin explicaciones

                CONTEXTO DE INDUSTRIA {industry}:
                {chr(10).join([f"• {example}" for example in industry_examples])}

                Analiza el producto descrito y genera UN headline específico que realmente venda ESTE producto.
                Responde SOLO con el headline, nada más.
                """,

                "punchline": f"""
                Eres Emma, especialista en anuncios con experiencia en millones de campañas exitosas.

                ANÁLISIS DEL PRODUCTO:
                Descripción: {product_description}
                Industria detectada: {industry}
                Plataforma objetivo: {platform}

                TAREA: Genera UN punchline persuasivo específico para este producto exacto.

                REGLAS OBLIGATORIAS:
                - MÁXIMO 40 caracteres (cuenta cada letra y espacio)
                - Debe generar confianza específica en ESTE producto
                - Enfócate en el resultado/beneficio emocional que ofrece
                - Debe ser convincente y crear urgencia/deseo
                - Sin puntos finales, sin comillas, sin explicaciones

                CONTEXTO DE INDUSTRIA {industry}:
                {chr(10).join([f"• {example}" for example in self._get_industry_examples(industry, "punchline")])}

                Analiza el producto descrito y genera UN punchline que haga que la gente QUIERA este producto específico.
                Responde SOLO con el punchline, nada más.
                """,

                "cta": f"""
                Eres Emma, especialista en anuncios con experiencia en millones de campañas exitosas.

                ANÁLISIS DEL PRODUCTO:
                Descripción: {product_description}
                Industria detectada: {industry}
                Plataforma objetivo: {platform}

                TAREA: Genera UN call-to-action específico para este producto exacto.

                REGLAS OBLIGATORIAS:
                - MÁXIMO 25 caracteres (cuenta cada letra y espacio)
                - Debe motivar acción específica relacionada con ESTE producto
                - Crea urgencia y deseo de actuar AHORA
                - Usa verbos de acción potentes
                - Sin puntos finales, sin comillas, sin explicaciones

                CONTEXTO DE INDUSTRIA {industry}:
                {chr(10).join([f"• {example}" for example in self._get_industry_examples(industry, "cta")])}

                Analiza el producto descrito y genera UN CTA que haga que la gente haga clic para obtener ESTE producto específico.
                Responde SOLO con el CTA, nada más.
                """
            }
            
            prompt = prompts.get(field_type, "")
            if not prompt:
                raise ValueError(f"Unknown field type: {field_type}")
            
            if self.ai_provider.is_ai_available():
                response = await self.ai_provider.generate_content(prompt)
                clean_response = response.strip().replace('"', '').replace("'", "").strip()

                # Ensure the response meets character limits
                max_lengths = {"headline": 40, "punchline": 40, "cta": 25}
                max_length = max_lengths.get(field_type, 40)

                if len(clean_response) > max_length:
                    # Try to extract a shorter version
                    lines = clean_response.split('\n')
                    for line in lines:
                        clean_line = line.strip()
                        if clean_line and len(clean_line) <= max_length:
                            return clean_line
                    # If no suitable line found, truncate intelligently
                    words = clean_response.split()
                    truncated = ""
                    for word in words:
                        if len(truncated + " " + word) <= max_length - 3:
                            truncated += (" " if truncated else "") + word
                        else:
                            break
                    return truncated + "..." if truncated else clean_response[:max_length-3] + "..."

                return clean_response
            else:
                # Fallback responses
                fallbacks = {
                    "headline": "Descubre la Diferencia",
                    "punchline": "Calidad que Convence",
                    "cta": "Conoce Más"
                }
                return fallbacks.get(field_type, "Generar Contenido")
                
        except Exception as e:
            logger.error(f"Error generating {field_type}: {e}")
            return f"Error generando {field_type}"
