/**
 * Test script to verify the Supabase Storage image display fix
 * This script tests the complete flow: save analysis with image, then load from history
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🧪 Testing Supabase Storage Image Display Fix...');

// Test configuration
const TEST_CONFIG = {
  waitTime: 3000, // Wait time between actions
  maxRetries: 3,
  testImageDataUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==' // 1x1 pixel PNG
};

// Helper functions
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const findElement = async (selector, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    const element = document.querySelector(selector);
    if (element) return element;
    await wait(1000);
  }
  throw new Error(`Element not found: ${selector}`);
};

const createTestImage = () => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    // Create a colorful test pattern
    ctx.fillStyle = '#ff6b6b';
    ctx.fillRect(0, 0, 100, 100);
    ctx.fillStyle = '#4ecdc4';
    ctx.fillRect(100, 0, 100, 100);
    ctx.fillStyle = '#45b7d1';
    ctx.fillRect(0, 100, 100, 100);
    ctx.fillStyle = '#f9ca24';
    ctx.fillRect(100, 100, 100, 100);
    
    // Add text
    ctx.fillStyle = '#2c2c2c';
    ctx.font = '16px Arial';
    ctx.fillText('TEST', 80, 105);
    
    canvas.toBlob((blob) => {
      const file = new File([blob], 'test-image-fix.png', { type: 'image/png' });
      resolve(file);
    }, 'image/png');
  });
};

// Test 1: Upload and analyze an image
async function testImageUploadAndAnalysis() {
  console.log('📤 Test 1: Upload and analyze an image');
  
  try {
    // Navigate to analyze tab
    const analyzeTab = await findElement('[value="analyze"]');
    analyzeTab.click();
    await wait(TEST_CONFIG.waitTime);
    
    // Create test image
    const testFile = await createTestImage();
    console.log('🖼️ Created test image:', testFile.name);
    
    // Find file input and upload
    const fileInput = await findElement('input[type="file"]');
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(testFile);
    fileInput.files = dataTransfer.files;
    
    // Trigger change event
    const event = new Event('change', { bubbles: true });
    fileInput.dispatchEvent(event);
    
    console.log('📁 Test file uploaded');
    await wait(TEST_CONFIG.waitTime);
    
    // Check if preview is displayed
    const previewImage = document.querySelector('img[src*="blob:"], img[src*="data:"]');
    if (previewImage) {
      console.log('✅ Image preview displayed after upload');
    } else {
      console.log('❌ No image preview after upload');
    }
    
    // Find and click analyze button
    const analyzeButton = document.querySelector('button:contains("Analizar"), button[class*="analyze"]');
    if (analyzeButton) {
      console.log('🔍 Clicking analyze button...');
      analyzeButton.click();
      
      // Wait for analysis to complete
      console.log('⏳ Waiting for analysis to complete...');
      await wait(15000); // Wait longer for analysis
      
      console.log('✅ Analysis should be complete');
      return true;
    } else {
      console.log('❌ Analyze button not found');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test 1 ERROR:', error);
    return false;
  }
}

// Test 2: Load saved analysis and verify image display
async function testLoadSavedAnalysis() {
  console.log('📋 Test 2: Load saved analysis and verify image display');
  
  try {
    // Navigate to history tab
    const historyTab = await findElement('[value="history"]');
    historyTab.click();
    await wait(TEST_CONFIG.waitTime);
    
    // Find analysis cards
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], [class*="AnalysisCard"]');
    console.log(`📊 Found ${analysisCards.length} analysis cards`);
    
    if (analysisCards.length === 0) {
      console.log('⚠️ No saved analyses found to test');
      return false;
    }
    
    // Find the most recent analysis (should be our test)
    const firstCard = analysisCards[0];
    console.log('📋 Testing with first analysis card');
    
    // Check if the card shows an image preview
    const cardImage = firstCard.querySelector('img');
    if (cardImage) {
      console.log('🖼️ Analysis card shows image preview:', {
        src: cardImage.src.substring(0, 50) + '...',
        loaded: cardImage.complete && cardImage.naturalHeight !== 0
      });
    }
    
    // Find and click load button
    const loadButton = firstCard.querySelector('button:contains("Cargar"), button[class*="load"], button[title*="load"], button[title*="Cargar"]');
    if (!loadButton) {
      // Try clicking the card itself if no specific load button
      console.log('🔄 No specific load button found, trying to click card...');
      firstCard.click();
    } else {
      console.log('🔄 Clicking load button...');
      loadButton.click();
    }
    
    await wait(TEST_CONFIG.waitTime * 2); // Wait longer for loading
    
    // Check if we're on analyze tab
    const analyzeTabActive = document.querySelector('[value="analyze"][data-state="active"], [value="analyze"].active');
    if (analyzeTabActive) {
      console.log('✅ Switched to analyze tab');
      
      // Check if image preview is displayed
      await wait(2000); // Wait a bit more for image loading
      const imagePreview = document.querySelector('img[src*="http"], img[src*="blob:"], img[src*="data:"]');
      
      if (imagePreview && imagePreview.src) {
        console.log('✅ Test 2 PASSED: Image preview is displayed after loading saved analysis');
        console.log('🖼️ Image details:', {
          src: imagePreview.src.substring(0, 100) + '...',
          width: imagePreview.naturalWidth,
          height: imagePreview.naturalHeight,
          loaded: imagePreview.complete && imagePreview.naturalHeight !== 0
        });
        
        // Test if it's an object URL (from authenticated download)
        if (imagePreview.src.startsWith('blob:')) {
          console.log('🔐 Using object URL from authenticated download - FIX IS WORKING!');
        } else if (imagePreview.src.startsWith('http')) {
          console.log('🌐 Using public URL');
        }
        
        return true;
      } else {
        console.log('❌ Test 2 FAILED: No image preview found after loading');
        return false;
      }
    } else {
      console.log('❌ Test 2 FAILED: Did not switch to analyze tab');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test 2 ERROR:', error);
    return false;
  }
}

// Test 3: Verify designAnalysisService.getImageUrl method
async function testGetImageUrlMethod() {
  console.log('🔧 Test 3: Test designAnalysisService.getImageUrl method');
  
  try {
    // Import the service
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    const { supabase } = await import('/src/lib/supabase.ts');
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.log('⚠️ Not authenticated, skipping service test');
      return false;
    }
    
    // Get a sample analysis
    const { data: analyses, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('*')
      .eq('user_id', session.user.id)
      .not('file_url', 'is', null)
      .limit(1);
    
    if (error || !analyses || analyses.length === 0) {
      console.log('⚠️ No analyses with images found for service test');
      return false;
    }
    
    const analysis = analyses[0];
    console.log('📋 Testing getImageUrl with:', {
      id: analysis.id,
      filename: analysis.original_filename,
      file_url: analysis.file_url
    });
    
    const imageUrl = await designAnalysisService.getImageUrl(analysis.file_url);
    
    if (imageUrl) {
      console.log('✅ Test 3 PASSED: getImageUrl returned URL:', imageUrl.substring(0, 50) + '...');
      
      if (imageUrl.startsWith('blob:')) {
        console.log('🔐 Method is using authenticated download - FIX IS WORKING!');
      }
      
      return true;
    } else {
      console.log('❌ Test 3 FAILED: getImageUrl returned null');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test 3 ERROR:', error);
    return false;
  }
}

// Main test runner
async function runImageDisplayTests() {
  console.log('🚀 Starting Image Display Fix Tests...\n');
  
  const results = {
    uploadAndAnalysis: false,
    loadSavedAnalysis: false,
    getImageUrlMethod: false
  };
  
  // Run Test 1: Upload and Analysis
  console.log('='.repeat(60));
  results.uploadAndAnalysis = await testImageUploadAndAnalysis();
  
  // Run Test 2: Load Saved Analysis
  console.log('\n' + '='.repeat(60));
  results.loadSavedAnalysis = await testLoadSavedAnalysis();
  
  // Run Test 3: Service Method Test
  console.log('\n' + '='.repeat(60));
  results.getImageUrlMethod = await testGetImageUrlMethod();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 IMAGE DISPLAY FIX TEST RESULTS:');
  console.log('='.repeat(60));
  console.log(`📤 Upload & Analysis: ${results.uploadAndAnalysis ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`📋 Load Saved Analysis: ${results.loadSavedAnalysis ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`🔧 Service Method: ${results.getImageUrlMethod ? '✅ PASSED' : '❌ FAILED'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (results.loadSavedAnalysis && results.getImageUrlMethod) {
    console.log('🎉 IMAGE DISPLAY FIX IS WORKING! Images should now display when loading saved analyses.');
  } else {
    console.log('⚠️ Some tests failed. The fix may need additional work.');
  }
  
  return results;
}

// Make functions available globally
window.testImageDisplayFix = {
  runImageDisplayTests,
  testImageUploadAndAnalysis,
  testLoadSavedAnalysis,
  testGetImageUrlMethod
};

console.log('🔧 Image Display Fix Test Script Loaded');
console.log('📝 Run testImageDisplayFix.runImageDisplayTests() to start testing');
