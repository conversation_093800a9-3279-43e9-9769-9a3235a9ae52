<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyCrew AI Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            min-height: 100px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .loading {
            display: none;
            margin-top: 10px;
            color: #666;
        }
        .error {
            color: #d32f2f;
            margin-top: 10px;
        }
        .success {
            color: #388e3c;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>MyCrew AI Frontend Test</h1>
    <p>This page tests the communication between the frontend and the MyCrew AI backend.</p>
    
    <div class="container">
        <!-- Content Generation Test -->
        <div class="card">
            <h2>Content Generation Test</h2>
            <div class="form-group">
                <label for="prompt">Prompt:</label>
                <textarea id="prompt">Create a short social media post about AI technology</textarea>
            </div>
            <div class="form-group">
                <label for="content-type">Content Type:</label>
                <select id="content-type">
                    <option value="social_media">Social Media</option>
                    <option value="campaign">Marketing Campaign</option>
                    <option value="general_content">General Content</option>
                </select>
            </div>
            <div class="form-group">
                <label for="audience">Audience:</label>
                <input type="text" id="audience" value="tech enthusiasts">
            </div>
            <div class="form-group">
                <label for="tone">Tone:</label>
                <input type="text" id="tone" value="professional">
            </div>
            <button id="generate-content">Generate Content</button>
            <div id="content-loading" class="loading">Generating content... (this may take a minute)</div>
            <div id="content-error" class="error"></div>
            <div id="content-success" class="success"></div>
            <div id="content-result" class="result"></div>
        </div>
        
        <!-- Agent Chat Test -->
        <div class="card">
            <h2>Agent Chat Test</h2>
            <div class="form-group">
                <label for="agent-id">Agent ID:</label>
                <input type="text" id="agent-id" value="emma">
            </div>
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message">What can you help me with?</textarea>
            </div>
            <button id="send-message">Send Message</button>
            <div id="chat-loading" class="loading">Sending message... (this may take a minute)</div>
            <div id="chat-error" class="error"></div>
            <div id="chat-success" class="success"></div>
            <div id="chat-result" class="result"></div>
        </div>
        
        <!-- Crew Run Test -->
        <div class="card">
            <h2>Crew Run Test</h2>
            <div class="form-group">
                <label for="crew-prompt">Prompt:</label>
                <textarea id="crew-prompt">Create a brief marketing strategy for a new fitness app</textarea>
            </div>
            <button id="run-crew">Run Crew</button>
            <div id="crew-loading" class="loading">Running crew... (this may take a minute)</div>
            <div id="crew-error" class="error"></div>
            <div id="crew-success" class="success"></div>
            <div id="crew-result" class="result"></div>
        </div>
    </div>
    
    <script>
        // API base URL - adjust if needed
        const BASE_URL = 'http://localhost:8000/api/v1';
        
        // Content Generation Test
        document.getElementById('generate-content').addEventListener('click', async () => {
            const promptEl = document.getElementById('prompt');
            const contentTypeEl = document.getElementById('content-type');
            const audienceEl = document.getElementById('audience');
            const toneEl = document.getElementById('tone');
            const loadingEl = document.getElementById('content-loading');
            const errorEl = document.getElementById('content-error');
            const successEl = document.getElementById('content-success');
            const resultEl = document.getElementById('content-result');
            
            // Clear previous results
            errorEl.textContent = '';
            successEl.textContent = '';
            resultEl.textContent = '';
            loadingEl.style.display = 'block';
            
            try {
                const response = await fetch(`${BASE_URL}/content/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: promptEl.value,
                        type: contentTypeEl.value,
                        audience: audienceEl.value,
                        tone: toneEl.value
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                loadingEl.style.display = 'none';
                successEl.textContent = `Content generated successfully! Request ID: ${data.request_id}`;
                resultEl.textContent = data.result;
                
                // Store the trace ID in localStorage for later retrieval
                localStorage.setItem('lastTraceId', data.request_id);
                
            } catch (error) {
                loadingEl.style.display = 'none';
                errorEl.textContent = `Error: ${error.message}`;
                console.error('Content generation error:', error);
            }
        });
        
        // Agent Chat Test
        document.getElementById('send-message').addEventListener('click', async () => {
            const agentIdEl = document.getElementById('agent-id');
            const messageEl = document.getElementById('message');
            const loadingEl = document.getElementById('chat-loading');
            const errorEl = document.getElementById('chat-error');
            const successEl = document.getElementById('chat-success');
            const resultEl = document.getElementById('chat-result');
            
            // Clear previous results
            errorEl.textContent = '';
            successEl.textContent = '';
            resultEl.textContent = '';
            loadingEl.style.display = 'block';
            
            try {
                const response = await fetch(`${BASE_URL}/crew/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        agent_id: agentIdEl.value,
                        message: messageEl.value,
                        context: {}
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                loadingEl.style.display = 'none';
                successEl.textContent = 'Message sent successfully!';
                resultEl.textContent = data.response;
                
            } catch (error) {
                loadingEl.style.display = 'none';
                errorEl.textContent = `Error: ${error.message}`;
                console.error('Agent chat error:', error);
                
                // Try with a different agent ID if the first one failed
                if (agentIdEl.value === 'emma') {
                    errorEl.textContent += ' Trying with a different agent ID...';
                    agentIdEl.value = 'content_creator';
                    document.getElementById('send-message').click();
                }
            }
        });
        
        // Crew Run Test
        document.getElementById('run-crew').addEventListener('click', async () => {
            const promptEl = document.getElementById('crew-prompt');
            const loadingEl = document.getElementById('crew-loading');
            const errorEl = document.getElementById('crew-error');
            const successEl = document.getElementById('crew-success');
            const resultEl = document.getElementById('crew-result');
            
            // Clear previous results
            errorEl.textContent = '';
            successEl.textContent = '';
            resultEl.textContent = '';
            loadingEl.style.display = 'block';
            
            try {
                const response = await fetch(`${BASE_URL}/crew/run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: promptEl.value,
                        inputs: {
                            target_audience: 'health-conscious millennials',
                            platform: 'mobile'
                        }
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                loadingEl.style.display = 'none';
                successEl.textContent = 'Crew run successful!';
                resultEl.textContent = data.result;
                
            } catch (error) {
                loadingEl.style.display = 'none';
                errorEl.textContent = `Error: ${error.message}`;
                console.error('Crew run error:', error);
            }
        });
    </script>
</body>
</html>
