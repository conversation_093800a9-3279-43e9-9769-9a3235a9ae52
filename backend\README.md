# Emma Studio Backend

Backend service for the Emma Studio AI content generation platform.

## Dependency Management

This project uses Poetry for dependency management. The dependencies are organized into groups:

- **Main dependencies**: FastAPI, Uvicorn, Pydantic, SQLAlchemy, and other core dependencies
- **dev**: Development tools like pytest, black, etc.

### Python Version Compatibility

This project is compatible with Python 3.10-3.12. Python 3.12 is recommended, but specific dependency versions have been pinned to ensure compatibility across these Python versions.

## Installation

### Quick Setup

The easiest way to set up the development environment is to use the provided setup script:

```bash
# Make the script executable if needed
chmod +x setup.sh

# Run the setup script
./setup.sh
```

### Manual Installation

```bash
# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Configure Poetry to create virtual environment in project directory
poetry config virtualenvs.in-project true

# Install all dependencies
poetry install
```

## Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file to add your API keys and configuration settings:
   - `GEMINI_API_KEY`: Required for Google Gemini AI
   - `STABILITY_API_KEY`: Required for Stability AI image generation
   - `OPENAI_API_KEY`: Optional for OpenAI integration
   - `DATABASE_URL`: Database connection string (defaults to SQLite)
   - `REDIS_URL`: Optional Redis connection string for caching

## Running the Application

There are multiple ways to run the application:

### Method 1: Using Poetry and Uvicorn directly

```bash
# From the backend directory
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Method 2: Using the wrapper script

```bash
# From the backend directory
poetry run python -m main
```

### Method 3: Using the run script

```bash
# From the backend directory
./run.sh

# With custom options
./run.sh --host=127.0.0.1 --port=8080 --no-reload --log-level=debug
```

### Method 4: Using Poetry scripts (if configured)

```bash
poetry run start-dev
```



## API Endpoints

The API follows a consistent structure with all endpoints under the `/api/v1` prefix:

### Health Check
- `GET /api/v1/health` - Check if the API is running

### Content Generation
- `POST /api/v1/content/generate` - Generate content (text, copy, etc.)
- `POST /api/v1/content/improve` - Improve a prompt or content
- `GET /api/v1/content/history` - Get content generation history

### Image Generation
- `POST /api/v1/images/generate` - Generate images using AI

### Agent Management
- `POST /api/v1/agents/run` - Run a team of AI agents
- `POST /api/v1/agents/chat` - Chat with a specific agent

## API Documentation

The API documentation is available at:
- Swagger UI: `http://localhost:8000/api/v1/docs`
- ReDoc: `http://localhost:8000/api/v1/redoc`

## Configuration

The application uses a combination of:
1. Environment variables (loaded from `.env` file)
2. YAML configuration files for agent components:
   - `backend/config/agents.yaml` - Agent definitions and capabilities
   - `backend/config/tools.yaml` - Tool definitions for agents
   - `backend/config/tasks.yaml` - Task definitions and workflows
   - `backend/config/teams.yaml` - Team configurations for agent collaboration

Note: Despite the naming conventions that might suggest CrewAI usage, these configuration files are used by our custom agent implementation.

## Agent Architecture

**IMPORTANT**: Emma Studio is transitioning to a fully custom agent implementation. Currently:

- **Frontend**: Uses a custom agent implementation in TypeScript
- **Backend**: Currently uses CrewAI but is transitioning to a custom implementation

The CrewAI code in `backend/crewai_app/` is marked as deprecated but is still in use. See `backend/crewai_app/README_DEPRECATED.md` for the transition plan.

For detailed information about the agent architecture and transition plan, see [AGENT_ARCHITECTURE.md](../AGENT_ARCHITECTURE.md).

## Troubleshooting

### Python 3.12 Compatibility Issues

If you encounter a `ForwardRef._evaluate()` error with Python 3.12, it's due to compatibility issues with Pydantic and FastAPI versions.

Solution: Use the following compatible versions in your pyproject.toml:
```toml
fastapi = "0.104.1"  # Compatible with Python 3.12
pydantic = "2.5.3"   # Compatible with FastAPI 0.104.1
```

### Other Dependency Issues

If you encounter other dependency issues:

1. Delete the `.venv` directory and Poetry lock file:
   ```bash
   rm -rf .venv poetry.lock
   ```
2. Reinstall dependencies:
   ```bash
   poetry install
   ```

## Project Structure

```
backend/
├── app/                    # Main application code
│   ├── api/                # API endpoints
│   │   └── endpoints/      # API route handlers
│   ├── core/               # Core functionality
│   ├── db/                 # Database models and session
│   ├── schemas/            # Pydantic models
│   ├── services/           # Business logic
│   └── main.py             # FastAPI application
├── config/                 # YAML configuration files
├── agents/                 # Custom agent implementation
│   ├── base.py             # Base agent class
│   ├── team.py             # Team orchestration
│   └── specialized/        # Specialized agent implementations
├── crewai_app/             # Legacy code (NOT USED - for reference only)
├── main.py                 # Entry point wrapper
└── pyproject.toml          # Poetry dependencies
```

**Note**: The `crewai_app` directory contains legacy code that is kept for reference only. It is not used in the current implementation. The actual agent implementation is in the `agents` directory.