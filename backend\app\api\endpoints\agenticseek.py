"""
AgenticSeek integration endpoint
"""
import os
import sys
import configparser
import asyncio
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional

# Add agenticseek to path
agenticseek_path = os.path.join(os.path.dirname(__file__), '..', '..', 'agenticseek')
sys.path.insert(0, agenticseek_path)

try:
    from sources.llm_provider import Provider
    from sources.interaction import Interaction
    from sources.agents.casual_agent import CasualAgent
    from sources.agents.code_agent import CoderAgent
    from sources.agents.file_agent import FileAgent
    from sources.agents.planner_agent import PlannerAgent
    from sources.agents.browser_agent import BrowserAgent
    from sources.router import AgentRouter
    from sources.logger import Logger
    from sources.browser import Browser, create_driver
except ImportError as e:
    print(f"Error importing AgenticSeek modules: {e}")
    # We'll handle this gracefully

router = APIRouter()

class AgenticSeekRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None

class AgenticSeekResponse(BaseModel):
    success: bool
    response: str
    agent_used: Optional[str] = None
    blocks: Optional[Dict[str, Any]] = None
    reasoning: Optional[str] = None
    error: Optional[str] = None

def initialize_agenticseek():
    """Initialize AgenticSeek components - FULL VERSION"""
    try:
        # Import here to avoid module loading issues
        import sys
        import os
        agenticseek_path = os.path.join(os.path.dirname(__file__), '..', '..', 'agenticseek')
        sys.path.insert(0, agenticseek_path)

        from sources.llm_provider import Provider
        from sources.interaction import Interaction
        from sources.agents.casual_agent import CasualAgent
        from sources.agents.code_agent import CoderAgent
        from sources.agents.file_agent import FileAgent
        from sources.agents.planner_agent import PlannerAgent
        from sources.agents.browser_agent import BrowserAgent
        from sources.browser import Browser, create_driver, CloudBrowser, create_cloud_browser

        # Save current working directory
        original_cwd = os.getcwd()

        # Change to AgenticSeek directory for proper relative path resolution
        os.chdir(agenticseek_path)

        # Read config
        config_path = os.path.join(agenticseek_path, 'config.ini')
        config = configparser.ConfigParser()
        config.read(config_path)

        # Initialize provider
        provider = Provider(
            provider_name=config.get('MAIN', 'provider_name'),
            model=config.get('MAIN', 'provider_model'),
            server_address=config.get('MAIN', 'provider_server_address'),
            is_local=config.getboolean('MAIN', 'is_local')
        )

        # Initialize browser (cloud or local)
        browser = None
        try:
            # Try cloud browser first
            use_cloud_browser = config.getboolean('BROWSER', 'use_cloud_browser')
            if use_cloud_browser:
                browserless_api_key = config.get('BROWSER', 'browserless_api_key')
                browser = create_cloud_browser(browserless_api_key)
                print("CloudBrowser initialized with Browserless.io")
            else:
                # Fallback to local browser
                headless = config.getboolean('BROWSER', 'headless_browser')
                stealth = config.getboolean('BROWSER', 'stealth_mode')
                driver = create_driver(headless=headless, stealth_mode=stealth)
                browser = Browser(driver)
                print("Local Browser initialized")
        except Exception as browser_error:
            print(f"Browser initialization failed: {browser_error}")
            # Try fallback to cloud browser if local fails
            try:
                browserless_api_key = config.get('BROWSER', 'browserless_api_key')
                browser = create_cloud_browser(browserless_api_key)
                print("Fallback to CloudBrowser successful")
            except Exception as fallback_error:
                print(f"Fallback browser initialization failed: {fallback_error}")
                browser = None

        # Initialize ALL agents
        agent_name = config.get('MAIN', 'agent_name')
        personality = "jarvis" if config.getboolean('MAIN', 'jarvis_personality') else "base"

        agents = [
            CasualAgent(
                agent_name,
                f"prompts/{personality}/casual_agent.txt",
                provider,
                verbose=False
            ),
            CoderAgent(
                agent_name,
                f"prompts/{personality}/coder_agent.txt",
                provider,
                verbose=False
            ),
            FileAgent(
                agent_name,
                f"prompts/{personality}/file_agent.txt",
                provider,
                verbose=False
            ),
            PlannerAgent(
                agent_name,
                f"prompts/{personality}/planner_agent.txt",
                provider,
                verbose=False,
                browser=browser
            ),
            BrowserAgent(
                agent_name,
                f"prompts/{personality}/browser_agent.txt",
                provider,
                verbose=False,
                browser=browser
            )
        ]

        # Create interaction instance - FULL SYSTEM
        languages = config.get('MAIN', 'languages').split()
        interaction = Interaction(
            agents=agents,
            tts_enabled=False,  # Disable audio for web API
            stt_enabled=False,  # Disable audio for web API
            recover_last_session=False,
            langs=languages
        )

        # Restore original working directory
        os.chdir(original_cwd)

        return interaction, None

    except Exception as e:
        # Restore original working directory in case of error
        try:
            os.chdir(original_cwd)
        except:
            pass
        return None, str(e)

@router.post("/agenticseek/chat", response_model=AgenticSeekResponse)
async def agenticseek_chat(request: AgenticSeekRequest):
    """
    Process a message using AgenticSeek system - FULL VERSION
    """
    try:
        # Initialize AgenticSeek FULL SYSTEM
        interaction, error = initialize_agenticseek()

        if error:
            return AgenticSeekResponse(
                success=False,
                response="Error initializing AgenticSeek",
                error=error
            )

        # Use the FULL AgenticSeek system exactly as designed
        interaction.set_query(request.message)
        success = await interaction.think()

        if success and interaction.last_answer:
            # PASS the blocks from the agent to frontend
            blocks_data = {}
            if interaction.current_agent and hasattr(interaction.current_agent, 'blocks_result'):
                for i, block in enumerate(interaction.current_agent.blocks_result):
                    blocks_data[str(i)] = {
                        "tool": block.tool_type,
                        "content": block.block,
                        "feedback": block.feedback,
                        "success": block.success
                    }

            return AgenticSeekResponse(
                success=True,
                response=interaction.last_answer,
                agent_used=interaction.current_agent.type if interaction.current_agent else 'unknown',
                blocks=blocks_data,
                reasoning=interaction.last_reasoning
            )
        else:
            return AgenticSeekResponse(
                success=False,
                response="No response generated",
                error="Agent processing failed"
            )

    except Exception as e:
        return AgenticSeekResponse(
            success=False,
            response="Error processing request",
            error=str(e)
        )

@router.post("/agenticseek-browser/chat", response_model=AgenticSeekResponse)
async def agenticseek_browser_chat(request: AgenticSeekRequest):
    """
    Process a message using AgenticSeek with BROWSER mode (enhanced with activity tracking)
    """
    try:
        # For now, use the same system as cloud but with browser mode indication
        # This avoids the browser initialization issues while providing the interface
        interaction, error = initialize_agenticseek()

        if error:
            return AgenticSeekResponse(
                success=False,
                response="Error initializing AgenticSeek",
                error=error
            )

        # Process the query with full AgenticSeek system
        interaction.set_query(request.message)
        success = await interaction.think()

        # Create a mock screenshot indicator for now
        screenshot_taken = False
        try:
            # Create screenshots directory if it doesn't exist
            screenshot_dir = "/tmp/emma_screenshots"
            os.makedirs(screenshot_dir, exist_ok=True)

            # Try to get screenshot from current agent's browser (if available)
            if (hasattr(interaction, 'current_agent') and
                interaction.current_agent and
                hasattr(interaction.current_agent, 'browser') and
                interaction.current_agent.browser and
                hasattr(interaction.current_agent.browser, 'driver')):

                screenshot_path = os.path.join(screenshot_dir, "latest_screenshot.png")
                interaction.current_agent.browser.driver.save_screenshot(screenshot_path)
                screenshot_taken = True
                print(f"Screenshot saved to: {screenshot_path}")
            else:
                print("Browser mode active but no screenshot available")

        except Exception as screenshot_error:
            print(f"Screenshot attempt failed: {screenshot_error}")

        if success and interaction.last_answer:
            # PASS the blocks from the agent to frontend
            blocks_data = {}
            if interaction.current_agent and hasattr(interaction.current_agent, 'blocks_result'):
                for i, block in enumerate(interaction.current_agent.blocks_result):
                    blocks_data[str(i)] = {
                        "tool": block.tool_type,
                        "content": block.block,
                        "feedback": block.feedback,
                        "success": block.success
                    }

            return AgenticSeekResponse(
                success=True,
                response=interaction.last_answer,
                agent_used=interaction.current_agent.type if interaction.current_agent else 'browser_agent',
                blocks=blocks_data,
                reasoning=interaction.last_reasoning
            )
        else:
            return AgenticSeekResponse(
                success=False,
                response="No response generated",
                error="Agent processing failed"
            )

    except Exception as e:
        return AgenticSeekResponse(
            success=False,
            response="Error processing request",
            error=str(e)
        )

@router.get("/agenticseek/screenshot")
async def get_screenshot():
    """
    Get the latest screenshot from browser (CloudBrowser or local)
    """
    try:
        from fastapi.responses import FileResponse

        # Try AgenticSeek screenshot folder first
        agenticseek_screenshot = os.path.join(agenticseek_path, ".screenshots", "updated_screen.png")
        if os.path.exists(agenticseek_screenshot):
            return FileResponse(
                agenticseek_screenshot,
                media_type="image/png",
                filename="emma_screenshot.png",
                headers={"Cache-Control": "no-cache, no-store, must-revalidate"}
            )

        # Fallback to legacy path
        screenshot_path = "/tmp/emma_screenshots/latest_screenshot.png"
        if os.path.exists(screenshot_path):
            return FileResponse(
                screenshot_path,
                media_type="image/png",
                filename="emma_screenshot.png",
                headers={"Cache-Control": "no-cache, no-store, must-revalidate"}
            )
        else:
            raise HTTPException(status_code=404, detail="No screenshot available yet")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Screenshot error: {str(e)}")

@router.post("/agenticseek/take-screenshot")
async def take_screenshot():
    """
    Force take a screenshot for testing
    """
    try:
        # Initialize AgenticSeek
        interaction, error = initialize_agenticseek()

        if error:
            raise HTTPException(status_code=500, detail=f"Initialization error: {error}")

        # Create screenshots directory
        screenshot_dir = "/tmp/emma_screenshots"
        os.makedirs(screenshot_dir, exist_ok=True)
        screenshot_path = os.path.join(screenshot_dir, "latest_screenshot.png")

        # Try to find a browser agent and take screenshot
        browser_agent = None
        for agent in interaction.agents:
            if hasattr(agent, 'browser') and agent.browser:
                browser_agent = agent
                break

        if browser_agent and browser_agent.browser:
            # Navigate to a test page first
            browser_agent.browser.go_to("https://www.google.com")

            # Take screenshot (works for both CloudBrowser and local Browser)
            if hasattr(browser_agent.browser, 'screenshot'):
                # CloudBrowser or local Browser with screenshot method
                browser_agent.browser.screenshot("latest_screenshot.png")
                # Copy to legacy path for compatibility
                import shutil
                agenticseek_screenshot = browser_agent.browser.get_screenshot()
                if os.path.exists(agenticseek_screenshot):
                    shutil.copy2(agenticseek_screenshot, screenshot_path)
            elif hasattr(browser_agent.browser, 'driver'):
                # Local Browser with driver
                browser_agent.browser.driver.save_screenshot(screenshot_path)

            return {
                "success": True,
                "message": "Screenshot taken successfully",
                "path": screenshot_path,
                "browser_type": type(browser_agent.browser).__name__
            }
        else:
            return {
                "success": False,
                "message": "No browser available",
                "agents_info": [type(agent).__name__ for agent in interaction.agents]
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Screenshot test error: {str(e)}")

@router.get("/agenticseek/status")
async def agenticseek_status():
    """
    Get AgenticSeek system status
    """
    try:
        config_path = os.path.join(agenticseek_path, 'config.ini')

        return {
            "status": "available",
            "config_path": config_path,
            "agenticseek_path": agenticseek_path
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }
