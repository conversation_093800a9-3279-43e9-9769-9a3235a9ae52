"""Service for image-to-video generation using Stability AI v2beta API."""

import logging
import httpx
import os
from typing import Op<PERSON>, <PERSON><PERSON>
from fastapi import HTTPException, UploadFile
from PIL import Image
import io
import base64

from app.schemas.video import (
    ImageToVideoRequest,
    ImageToVideoStartResponse,
    ImageToVideoResultResponse,
    VideoGenerationConstants,
    validate_image_dimensions,
    get_closest_supported_dimensions,
    validate_image_format
)

logger = logging.getLogger(__name__)

class VideoGenerationService:
    """Service for handling image-to-video generation with Stability AI."""
    
    def __init__(self):
        self.api_key = os.getenv("STABILITY_API_KEY")
        if not self.api_key:
            logger.error("STABILITY_API_KEY not found in environment variables")
            raise ValueError("STABILITY_API_KEY is required for video generation")
        
        self.base_url = VideoGenerationConstants.STABILITY_BASE_URL
        self.timeout = 180.0  # 3 minutes timeout
    
    def _get_headers(self) -> dict:
        """Get headers for Stability AI API requests."""
        return {
            "authorization": f"Bearer {self.api_key}",
            "accept": "application/json"
        }
    
    async def _validate_and_process_image(self, image_file: UploadFile) -> Tuple[bytes, str]:
        """
        Validate and process the uploaded image file.
        Returns: (processed_image_bytes, content_type)
        """
        # Check file size
        content = await image_file.read()
        if len(content) > VideoGenerationConstants.MAX_FILE_SIZE_BYTES:
            raise HTTPException(
                status_code=413,
                detail=f"Image file too large. Maximum size is {VideoGenerationConstants.MAX_FILE_SIZE_BYTES / (1024*1024):.1f}MB"
            )
        
        # Reset file pointer
        await image_file.seek(0)
        
        # Validate content type
        content_type = image_file.content_type or "image/jpeg"
        if not validate_image_format(content_type):
            logger.warning(f"Unsupported image format: {content_type}. Converting to JPEG.")
            content_type = "image/jpeg"
        
        try:
            # Open and validate image
            image = Image.open(io.BytesIO(content))
            width, height = image.size
            
            logger.info(f"Original image dimensions: {width}x{height}")
            
            # Check if dimensions are supported
            if not validate_image_dimensions(width, height):
                # Resize to closest supported dimensions
                target_width, target_height = get_closest_supported_dimensions(width, height)
                logger.info(f"Resizing image to supported dimensions: {target_width}x{target_height}")
                
                # Resize maintaining aspect ratio and center crop if needed
                image = self._resize_and_crop_image(image, target_width, target_height)
            
            # Convert to RGB if necessary (for JPEG)
            if content_type == "image/jpeg" and image.mode in ("RGBA", "P"):
                # Create white background for transparency
                background = Image.new("RGB", image.size, (255, 255, 255))
                if image.mode == "P":
                    image = image.convert("RGBA")
                background.paste(image, mask=image.split()[-1] if image.mode == "RGBA" else None)
                image = background
            
            # Save processed image to bytes
            output_buffer = io.BytesIO()
            format_name = "JPEG" if content_type == "image/jpeg" else "PNG"
            image.save(output_buffer, format=format_name, quality=95 if format_name == "JPEG" else None)
            processed_bytes = output_buffer.getvalue()
            
            logger.info(f"Processed image size: {len(processed_bytes)} bytes")
            return processed_bytes, content_type
            
        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid image file: {str(e)}"
            )
    
    def _resize_and_crop_image(self, image: Image.Image, target_width: int, target_height: int) -> Image.Image:
        """Resize and center crop image to target dimensions."""
        original_width, original_height = image.size
        target_ratio = target_width / target_height
        original_ratio = original_width / original_height
        
        if original_ratio > target_ratio:
            # Image is wider, crop width
            new_height = original_height
            new_width = int(original_height * target_ratio)
            left = (original_width - new_width) // 2
            top = 0
            right = left + new_width
            bottom = original_height
        else:
            # Image is taller, crop height
            new_width = original_width
            new_height = int(original_width / target_ratio)
            left = 0
            top = (original_height - new_height) // 2
            right = original_width
            bottom = top + new_height
        
        # Crop and resize
        cropped = image.crop((left, top, right, bottom))
        resized = cropped.resize((target_width, target_height), Image.Resampling.LANCZOS)
        
        return resized
    
    async def start_image_to_video_generation(
        self, 
        image_file: UploadFile, 
        request: ImageToVideoRequest
    ) -> ImageToVideoStartResponse:
        """
        Start image-to-video generation with Stability AI.
        Returns generation ID for polling.
        """
        logger.info("Starting image-to-video generation")
        
        try:
            # Validate and process image
            image_bytes, content_type = await self._validate_and_process_image(image_file)
            
            # Prepare form data
            files = {
                "image": ("image.jpg" if content_type == "image/jpeg" else "image.png", image_bytes, content_type)
            }
            
            data = {
                "seed": str(request.seed),
                "cfg_scale": str(request.cfg_scale),
                "motion_bucket_id": str(request.motion_bucket_id)
            }
            
            logger.info(f"Sending request to Stability AI with params: {data}")
            
            # Make request to Stability AI
            url = f"{self.base_url}{VideoGenerationConstants.IMAGE_TO_VIDEO_ENDPOINT}"
            headers = self._get_headers()
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    url,
                    headers=headers,
                    data=data,
                    files=files
                )
                
                if response.status_code != 200:
                    error_detail = f"Stability AI API error: {response.status_code}"
                    try:
                        error_data = response.json()
                        if "errors" in error_data:
                            error_detail = f"API Error: {', '.join(error_data['errors'])}"
                    except:
                        error_detail = f"API Error: {response.text}"
                    
                    logger.error(f"Stability AI API error: {error_detail}")
                    raise HTTPException(status_code=response.status_code, detail=error_detail)
                
                result = response.json()
                generation_id = result.get("id")
                
                if not generation_id:
                    raise HTTPException(
                        status_code=500,
                        detail="No generation ID received from Stability AI"
                    )
                
                logger.info(f"Generation started successfully with ID: {generation_id}")
                return ImageToVideoStartResponse(id=generation_id)
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error starting video generation: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to start video generation: {str(e)}"
            )
    
    async def get_video_generation_result(self, generation_id: str) -> ImageToVideoResultResponse:
        """
        Get the result of an image-to-video generation by ID.
        """
        logger.info(f"Checking video generation status for ID: {generation_id}")
        
        try:
            url = f"{self.base_url}{VideoGenerationConstants.IMAGE_TO_VIDEO_RESULT_ENDPOINT}/{generation_id}"
            headers = self._get_headers()
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 202:
                    # Still processing
                    logger.info(f"Generation {generation_id} still in progress")
                    return ImageToVideoResultResponse(
                        id=generation_id,
                        status="in-progress"
                    )
                elif response.status_code == 200:
                    # Completed - check if we get JSON or binary
                    content_type = response.headers.get("content-type", "")
                    
                    if "application/json" in content_type:
                        # JSON response with base64 video
                        result = response.json()
                        video_base64 = result.get("video")
                        finish_reason = result.get("finish_reason", "SUCCESS")
                        seed = result.get("seed")
                        
                        logger.info(f"Generation {generation_id} completed successfully")
                        return ImageToVideoResultResponse(
                            id=generation_id,
                            status="completed",
                            video=video_base64,
                            finish_reason=finish_reason,
                            seed=seed
                        )
                    else:
                        # Binary video response
                        video_bytes = response.content
                        video_base64 = base64.b64encode(video_bytes).decode('utf-8')
                        
                        # Get metadata from headers
                        finish_reason = response.headers.get("finish-reason", "SUCCESS")
                        seed = response.headers.get("seed")
                        
                        logger.info(f"Generation {generation_id} completed successfully (binary)")
                        return ImageToVideoResultResponse(
                            id=generation_id,
                            status="completed",
                            video=video_base64,
                            finish_reason=finish_reason,
                            seed=int(seed) if seed else None
                        )
                else:
                    # Error
                    error_detail = f"API Error: {response.status_code}"
                    try:
                        error_data = response.json()
                        if "errors" in error_data:
                            error_detail = f"API Error: {', '.join(error_data['errors'])}"
                    except:
                        error_detail = f"API Error: {response.text}"
                    
                    logger.error(f"Generation {generation_id} failed: {error_detail}")
                    return ImageToVideoResultResponse(
                        id=generation_id,
                        status="failed",
                        error=error_detail
                    )
                    
        except Exception as e:
            logger.error(f"Error checking video generation status: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to check generation status: {str(e)}"
            )

# Global service instance
video_service = VideoGenerationService()
