"""
Clarity Analyzer Module
Analyzes clarity and readability of content
"""

import logging
import re
from typing import Dict, Any

logger = logging.getLogger(__name__)

class ClarityAnalyzer:
    """Analyzes clarity and readability of content."""
    
    def __init__(self):
        logger.info("✅ Clarity Analyzer initialized successfully")
    
    def calculate_clarity_score(self, content: str) -> float:
        """
        Calculate clarity and readability of content.
        
        Args:
            content: Content to analyze
            
        Returns:
            Clarity score (0-100)
        """
        try:
            score = 50.0  # Base score
            
            # Calculate average sentence length
            sentences = [s.strip() for s in content.split('.') if s.strip()]
            if sentences:
                avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
                
                # Optimal sentence length is 15-25 words
                if 15 <= avg_sentence_length <= 25:
                    score += 15
                elif 10 <= avg_sentence_length <= 30:
                    score += 10
                else:
                    score += 5
            
            # Check for clear structure
            if content.count('\n\n') >= 2:  # Multiple paragraphs
                score += 10
            
            # Check for lists or bullet points
            if any(indicator in content for indicator in ['1.', '2.', '•', '-']):
                score += 10
            
            # Check for complex words (penalize excessive complexity)
            words = content.split()
            complex_words = sum(1 for word in words if len(word) > 12)
            complexity_ratio = complex_words / len(words) if words else 0
            
            if complexity_ratio < 0.1:
                score += 15
            elif complexity_ratio < 0.2:
                score += 10
            else:
                score += 5
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"❌ Clarity score calculation failed: {str(e)}")
            return 50.0
    
    def analyze_readability_metrics(self, content: str) -> Dict[str, Any]:
        """Analyze detailed readability metrics."""
        try:
            sentences = [s.strip() for s in content.split('.') if s.strip()]
            words = content.split()
            paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
            
            # Basic metrics
            metrics = {
                "word_count": len(words),
                "sentence_count": len(sentences),
                "paragraph_count": len(paragraphs),
                "avg_words_per_sentence": len(words) / len(sentences) if sentences else 0,
                "avg_sentences_per_paragraph": len(sentences) / len(paragraphs) if paragraphs else 0
            }
            
            # Sentence length analysis
            sentence_lengths = [len(s.split()) for s in sentences]
            if sentence_lengths:
                metrics.update({
                    "min_sentence_length": min(sentence_lengths),
                    "max_sentence_length": max(sentence_lengths),
                    "sentence_length_variance": self._calculate_variance(sentence_lengths)
                })
            
            # Word complexity analysis
            complex_words = [word for word in words if len(word) > 12]
            metrics.update({
                "complex_word_count": len(complex_words),
                "complex_word_ratio": len(complex_words) / len(words) if words else 0
            })
            
            # Structure analysis
            structure_elements = {
                "has_headings": bool(re.search(r'^[A-Z][^.]*:$', content, re.MULTILINE)),
                "has_lists": any(marker in content for marker in ['1.', '2.', '•', '-']),
                "has_paragraphs": len(paragraphs) > 1,
                "has_transitions": any(trans in content.lower() for trans in ['además', 'sin embargo', 'por lo tanto'])
            }
            metrics["structure_elements"] = structure_elements
            
            # Calculate readability score
            readability_score = self._calculate_readability_score(metrics)
            metrics["readability_score"] = readability_score
            metrics["readability_level"] = self._classify_readability_level(readability_score)
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Readability metrics analysis failed: {str(e)}")
            return {
                "readability_score": 50,
                "readability_level": "unknown",
                "error": str(e)
            }
    
    def _calculate_variance(self, values: list) -> float:
        """Calculate variance of a list of values."""
        if not values:
            return 0
        mean = sum(values) / len(values)
        return sum((x - mean) ** 2 for x in values) / len(values)
    
    def _calculate_readability_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall readability score from metrics."""
        score = 50  # Base score
        
        # Sentence length score
        avg_sentence_length = metrics.get("avg_words_per_sentence", 20)
        if 15 <= avg_sentence_length <= 25:
            score += 20
        elif 10 <= avg_sentence_length <= 30:
            score += 10
        
        # Complexity score
        complex_ratio = metrics.get("complex_word_ratio", 0.1)
        if complex_ratio < 0.1:
            score += 15
        elif complex_ratio < 0.2:
            score += 10
        
        # Structure score
        structure_elements = metrics.get("structure_elements", {})
        structure_score = sum(structure_elements.values()) * 5
        score += structure_score
        
        # Paragraph organization score
        paragraph_count = metrics.get("paragraph_count", 1)
        if paragraph_count >= 3:
            score += 10
        elif paragraph_count >= 2:
            score += 5
        
        return min(score, 100)
    
    def _classify_readability_level(self, readability_score: float) -> str:
        """Classify readability level based on score."""
        if readability_score >= 80:
            return "very_clear"
        elif readability_score >= 65:
            return "clear"
        elif readability_score >= 50:
            return "moderate"
        else:
            return "unclear"
