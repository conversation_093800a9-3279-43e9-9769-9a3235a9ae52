"""
WebSocket endpoints for real-time agent communication.

This module provides WebSocket endpoints for real-time bidirectional communication
between the frontend and backend agent systems.
"""

import asyncio
import json
import logging
import uuid
from typing import Any, Dict, List, Optional, Union

from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect, HTTPException, status
from pydantic import BaseModel, Field

from app.core.dependencies import get_db, verify_api_key
# from app.core.security import get_current_user  # Not implemented yet
from app.services.agent_service import get_available_agents
# from app.agents import (
#     AgentOrchestrator,
#     AgentProtocol,
#     MessageType,
#     AgentIdentity,
#     AgentRole,
#     AgentMessage,
#     BaseAgent
# )  # These imports are not needed for WebSocket functionality

# Configure logging
logger = logging.getLogger(__name__)
router = APIRouter()

# Store active connections
active_connections: Dict[str, WebSocket] = {}
# Store agent sessions
agent_sessions: Dict[str, Dict[str, Any]] = {}


class WebSocketMessage(BaseModel):
    """Schema for WebSocket messages."""
    type: str = Field(..., description="Message type")
    content: Dict[str, Any] = Field(..., description="Message content")
    session_id: Optional[str] = Field(None, description="Session ID")
    agent_id: Optional[str] = Field(None, description="Agent ID")
    request_id: Optional[str] = Field(None, description="Request ID")


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time agent communication.

    This endpoint handles WebSocket connections for real-time bidirectional
    communication between the frontend and backend agent systems.
    """
    # Accept the connection
    await websocket.accept()

    # Generate a unique connection ID
    connection_id = str(uuid.uuid4())
    active_connections[connection_id] = websocket

    logger.info(f"WebSocket connection established: {connection_id}")

    try:
        # Send a welcome message
        await websocket.send_json({
            "type": "connection_established",
            "content": {
                "connection_id": connection_id,
                "message": "WebSocket connection established"
            }
        })

        # Handle incoming messages
        while True:
            # Receive a message
            data = await websocket.receive_text()

            try:
                # Parse the message
                message_data = json.loads(data)
                message = WebSocketMessage(**message_data)

                # Process the message based on its type
                if message.type == "register_session":
                    # Register a new agent session
                    session_id = message.content.get("session_id") or str(uuid.uuid4())
                    agent_sessions[session_id] = {
                        "connection_id": connection_id,
                        "agents": message.content.get("agents", []),
                        "created_at": asyncio.get_event_loop().time()
                    }

                    # Send a confirmation
                    await websocket.send_json({
                        "type": "session_registered",
                        "content": {
                            "session_id": session_id,
                            "message": "Session registered successfully"
                        }
                    })

                    logger.info(f"Agent session registered: {session_id}")

                elif message.type == "agent_request":
                    # Process an agent request
                    if not message.session_id or message.session_id not in agent_sessions:
                        await websocket.send_json({
                            "type": "error",
                            "content": {
                                "message": "Invalid or missing session ID"
                            }
                        })
                        continue

                    # Get the request details
                    request_id = message.request_id or str(uuid.uuid4())
                    agent_id = message.agent_id
                    user_message = message.content.get("message", "")

                    # Send acknowledgment
                    await websocket.send_json({
                        "type": "request_received",
                        "content": {
                            "request_id": request_id,
                            "message": "Request received and processing"
                        }
                    })

                    # Process the request in a background task to not block the WebSocket
                    asyncio.create_task(
                        process_agent_request(
                            websocket,
                            request_id,
                            agent_id,
                            user_message,
                            message.session_id
                        )
                    )

                else:
                    # Unknown message type
                    await websocket.send_json({
                        "type": "error",
                        "content": {
                            "message": f"Unknown message type: {message.type}"
                        }
                    })

            except json.JSONDecodeError:
                # Invalid JSON
                await websocket.send_json({
                    "type": "error",
                    "content": {
                        "message": "Invalid JSON message"
                    }
                })

            except Exception as e:
                # Other errors
                logger.error(f"Error processing WebSocket message: {str(e)}", exc_info=True)
                await websocket.send_json({
                    "type": "error",
                    "content": {
                        "message": f"Error processing message: {str(e)}"
                    }
                })

    except WebSocketDisconnect:
        # Client disconnected
        logger.info(f"WebSocket client disconnected: {connection_id}")

    except Exception as e:
        # Other errors
        logger.error(f"WebSocket error: {str(e)}", exc_info=True)

    finally:
        # Clean up
        if connection_id in active_connections:
            del active_connections[connection_id]

        # Remove any sessions associated with this connection
        for session_id, session in list(agent_sessions.items()):
            if session.get("connection_id") == connection_id:
                del agent_sessions[session_id]


async def process_agent_request(
    websocket: WebSocket,
    request_id: str,
    agent_id: Optional[str],
    user_message: str,
    session_id: str
):
    """
    Process an agent request in a background task.

    Args:
        websocket: The WebSocket connection
        request_id: The request ID
        agent_id: The agent ID (optional)
        user_message: The user message
        session_id: The session ID
    """
    try:
        from app.schemas.crew import AgentChatRequest

        # Create a request object
        request = AgentChatRequest(
            agent_id=agent_id or "emma",  # Default to Emma if no agent specified
            message=user_message,
            context={"session_id": session_id}
        )

        # Send thinking status
        await websocket.send_json({
            "type": "agent_thinking",
            "content": {
                "request_id": request_id,
                "agent_id": agent_id,
                "message": "Agent is thinking..."
            }
        })

        # Track the full response for history
        full_response = ""

        # Process the request with streaming
        async for partial_response in stream_agent_response(request):
            # Accumulate the full response
            full_response += partial_response

            # Send partial response
            await websocket.send_json({
                "type": "agent_partial_response",
                "content": {
                    "request_id": request_id,
                    "agent_id": agent_id,
                    "message": partial_response
                }
            })

            # Small delay to prevent overwhelming the client
            await asyncio.sleep(0.05)

        # Get reasoning trace from the agent
        from app.services.agent_service import get_agent_reasoning_trace
        reasoning_trace = await get_agent_reasoning_trace(agent_id, request_id)

        # Send completion status with reasoning trace
        await websocket.send_json({
            "type": "agent_response_complete",
            "content": {
                "request_id": request_id,
                "agent_id": agent_id,
                "message": "Response complete",
                "full_response": full_response,
                "reasoning_trace": reasoning_trace
            }
        })

        # Save the conversation to history (if needed)
        # This could be implemented in a separate function

    except Exception as e:
        # Handle errors
        logger.error(f"Error processing agent request: {str(e)}", exc_info=True)
        await websocket.send_json({
            "type": "agent_error",
            "content": {
                "request_id": request_id,
                "agent_id": agent_id,
                "error": str(e)
            }
        })


async def stream_agent_response(request: Any):
    """
    Stream an agent response.

    Uses the streaming implementation from the agent system.

    Args:
        request: The agent request

    Yields:
        Partial response chunks
    """
    from app.services.agent_service import chat_with_agent_stream
    import time

    # Track reasoning steps
    reasoning_steps = []
    current_step = {"timestamp": int(time.time() * 1000), "content": ""}

    # Use the streaming implementation
    try:
        async for chunk in chat_with_agent_stream(request):
            # Check if this is a reasoning marker
            if chunk.startswith("REASONING:"):
                # Save current step if it has content
                if current_step["content"]:
                    reasoning_steps.append(current_step)

                # Start a new reasoning step
                current_step = {
                    "timestamp": int(time.time() * 1000),
                    "type": "reasoning",
                    "content": chunk[10:].strip()  # Remove the REASONING: prefix
                }
                continue

            # Check if this is a tool usage marker
            elif chunk.startswith("TOOL:"):
                # Save current step if it has content
                if current_step["content"]:
                    reasoning_steps.append(current_step)

                # Start a new tool usage step
                current_step = {
                    "timestamp": int(time.time() * 1000),
                    "type": "tool_usage",
                    "content": chunk[5:].strip()  # Remove the TOOL: prefix
                }
                continue

            # Regular content
            if "content" in current_step:
                current_step["content"] += chunk

            # Yield the chunk for the regular response
            yield chunk

        # Save the final step if it has content
        if current_step.get("content"):
            reasoning_steps.append(current_step)

    except Exception as e:
        # Log the error
        logger.error(f"Error in stream_agent_response: {str(e)}", exc_info=True)

        # Add error to reasoning steps
        reasoning_steps.append({
            "timestamp": int(time.time() * 1000),
            "type": "error",
            "content": f"Error: {str(e)}"
        })

        # Yield error message
        yield f"I'm sorry, I encountered an error: {str(e)}"
