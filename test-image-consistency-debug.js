/**
 * Comprehensive test script to debug Visual Complexity Analyzer image loading inconsistencies
 * This script investigates why only 1 out of 4 saved analyses shows images correctly
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🔍 Starting Visual Complexity Analyzer Image Consistency Debug...');

class ImageConsistencyDebugger {
  constructor() {
    this.results = {
      auth: null,
      analyses: [],
      storageFiles: [],
      testResults: [],
      summary: {}
    };
    this.supabase = window.supabase;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`[${timestamp}] ${emoji} ${message}`);
  }

  async checkAuth() {
    this.log('🔐 Checking authentication status...');
    
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();
      
      if (error) {
        this.log(`Authentication error: ${error.message}`, 'error');
        return false;
      }
      
      if (!user) {
        this.log('No authenticated user found', 'warn');
        return false;
      }
      
      this.results.auth = {
        userId: user.id,
        email: user.email
      };
      
      this.log(`✅ Authenticated as: ${user.email} (${user.id})`);
      return true;
    } catch (error) {
      this.log(`Failed to check auth: ${error.message}`, 'error');
      return false;
    }
  }

  async getAllAnalyses() {
    this.log('📊 Fetching all analyses with file_url data...');
    
    try {
      const { data: analyses, error } = await this.supabase
        .schema('api')
        .from('design_analyses')
        .select('*')
        .eq('user_id', this.results.auth.userId)
        .order('created_at', { ascending: false });

      if (error) {
        this.log(`Error fetching analyses: ${error.message}`, 'error');
        return [];
      }

      this.results.analyses = analyses || [];
      
      const withImages = analyses.filter(a => a.file_url);
      const withoutImages = analyses.filter(a => !a.file_url);
      
      this.log(`📈 Found ${analyses.length} total analyses:`);
      this.log(`  - ${withImages.length} with file_url`);
      this.log(`  - ${withoutImages.length} without file_url`);
      
      // Analyze file_url patterns
      const urlPatterns = {};
      withImages.forEach(analysis => {
        const url = analysis.file_url;
        let pattern;
        
        if (url.startsWith('http')) {
          pattern = 'HTTP_URL';
        } else if (url.includes('/')) {
          pattern = 'FILE_PATH';
        } else {
          pattern = 'OTHER';
        }
        
        urlPatterns[pattern] = (urlPatterns[pattern] || 0) + 1;
      });
      
      this.log('📋 File URL patterns:');
      Object.entries(urlPatterns).forEach(([pattern, count]) => {
        this.log(`  - ${pattern}: ${count} analyses`);
      });
      
      return withImages;
    } catch (error) {
      this.log(`Failed to fetch analyses: ${error.message}`, 'error');
      return [];
    }
  }

  async testImageUrl(analysis) {
    this.log(`🖼️ Testing image URL for analysis: ${analysis.id}`);
    
    const testResult = {
      analysisId: analysis.id,
      filename: analysis.original_filename,
      fileUrl: analysis.file_url,
      urlType: analysis.file_url.startsWith('http') ? 'HTTP_URL' : 'FILE_PATH',
      tests: {}
    };

    // Test 1: Direct URL test (if HTTP)
    if (analysis.file_url.startsWith('http')) {
      testResult.tests.directUrl = await this.testDirectImageLoad(analysis.file_url);
    }

    // Test 2: Supabase Storage download test
    testResult.tests.supabaseDownload = await this.testSupabaseDownload(analysis.file_url);

    // Test 3: getImageUrl service test
    testResult.tests.serviceMethod = await this.testServiceMethod(analysis.file_url);

    // Test 4: File existence check
    testResult.tests.fileExists = await this.testFileExists(analysis.file_url);

    this.results.testResults.push(testResult);
    return testResult;
  }

  async testDirectImageLoad(url) {
    this.log(`  📡 Testing direct image load: ${url.substring(0, 50)}...`);
    
    return new Promise((resolve) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        this.log('    ⏱️ Direct load timeout (10s)', 'warn');
        resolve({ success: false, error: 'timeout' });
      }, 10000);

      img.onload = () => {
        clearTimeout(timeout);
        this.log(`    ✅ Direct load success: ${img.naturalWidth}x${img.naturalHeight}`);
        resolve({ success: true, dimensions: `${img.naturalWidth}x${img.naturalHeight}` });
      };

      img.onerror = (error) => {
        clearTimeout(timeout);
        this.log(`    ❌ Direct load failed: ${error}`, 'error');
        resolve({ success: false, error: 'load_error' });
      };

      img.src = url;
    });
  }

  async testSupabaseDownload(filePath) {
    this.log(`  📥 Testing Supabase storage download: ${filePath}`);
    
    try {
      // Extract actual file path if it's a URL
      let actualPath = filePath;
      if (filePath.startsWith('http')) {
        const url = new URL(filePath);
        actualPath = url.pathname.split('/').slice(-2).join('/');
      }

      const { data, error } = await this.supabase.storage
        .from('design-analysis-images')
        .download(actualPath);

      if (error) {
        this.log(`    ❌ Supabase download failed: ${error.message}`, 'error');
        return { success: false, error: error.message };
      }

      if (data) {
        this.log(`    ✅ Supabase download success: ${data.size} bytes, type: ${data.type}`);
        return { success: true, size: data.size, type: data.type };
      }

      this.log(`    ❌ Supabase download returned no data`, 'error');
      return { success: false, error: 'no_data' };
    } catch (error) {
      this.log(`    ❌ Supabase download exception: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  async testServiceMethod(filePath) {
    this.log(`  🔧 Testing designAnalysisService.getImageUrl: ${filePath}`);
    
    try {
      // Access the service from the global scope or component
      const service = window.designAnalysisService || 
                    (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current?.memoizedProps?.designAnalysisService);
      
      if (!service) {
        this.log(`    ❌ designAnalysisService not accessible`, 'error');
        return { success: false, error: 'service_not_found' };
      }

      const url = await service.getImageUrl(filePath);
      
      if (url) {
        this.log(`    ✅ Service method success: ${url.substring(0, 50)}...`);
        
        // Test if the returned URL actually works
        const loadTest = await this.testDirectImageLoad(url);
        return { 
          success: true, 
          url: url.substring(0, 50) + '...', 
          urlType: url.startsWith('blob:') ? 'blob' : 'http',
          loadTest 
        };
      } else {
        this.log(`    ❌ Service method returned null`, 'error');
        return { success: false, error: 'null_result' };
      }
    } catch (error) {
      this.log(`    ❌ Service method exception: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  async testFileExists(filePath) {
    this.log(`  📁 Testing file existence: ${filePath}`);
    
    try {
      // Extract actual file path if it's a URL
      let actualPath = filePath;
      if (filePath.startsWith('http')) {
        const url = new URL(filePath);
        actualPath = url.pathname.split('/').slice(-2).join('/');
      }

      const pathParts = actualPath.split('/');
      if (pathParts.length < 2) {
        this.log(`    ❌ Invalid file path format: ${actualPath}`, 'error');
        return { success: false, error: 'invalid_path' };
      }

      const [userId, fileName] = pathParts;
      
      const { data, error } = await this.supabase.storage
        .from('design-analysis-images')
        .list(userId, {
          search: fileName
        });

      if (error) {
        this.log(`    ❌ File existence check failed: ${error.message}`, 'error');
        return { success: false, error: error.message };
      }

      const fileExists = data && data.length > 0;
      
      if (fileExists) {
        const fileInfo = data[0];
        this.log(`    ✅ File exists: ${fileInfo.name} (${fileInfo.metadata?.size || 'unknown size'})`);
        return { success: true, fileInfo };
      } else {
        this.log(`    ❌ File not found in storage`, 'error');
        return { success: false, error: 'file_not_found' };
      }
    } catch (error) {
      this.log(`    ❌ File existence check exception: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }

  async runFullDiagnostic() {
    this.log('🚀 Starting full diagnostic...');

    // Step 1: Check authentication
    const authOk = await this.checkAuth();
    if (!authOk) {
      this.log('❌ Cannot proceed without authentication', 'error');
      return this.results;
    }

    // Step 2: Get all analyses
    const analysesWithImages = await this.getAllAnalyses();
    if (analysesWithImages.length === 0) {
      this.log('❌ No analyses with images found', 'error');
      return this.results;
    }

    // Step 3: Test each analysis
    this.log(`🧪 Testing ${analysesWithImages.length} analyses with images...`);

    for (const analysis of analysesWithImages) {
      await this.testImageUrl(analysis);
      // Small delay to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Step 4: Generate summary
    this.generateSummary();

    this.log('✅ Full diagnostic complete!');
    return this.results;
  }

  generateSummary() {
    this.log('📊 Generating summary...');

    const total = this.results.testResults.length;
    const summary = {
      total,
      patterns: {},
      successRates: {},
      commonErrors: {},
      recommendations: []
    };

    // Analyze patterns
    this.results.testResults.forEach(result => {
      const pattern = result.urlType;
      summary.patterns[pattern] = (summary.patterns[pattern] || 0) + 1;
    });

    // Analyze success rates for each test type
    const testTypes = ['directUrl', 'supabaseDownload', 'serviceMethod', 'fileExists'];

    testTypes.forEach(testType => {
      const applicable = this.results.testResults.filter(r => r.tests[testType]);
      const successful = applicable.filter(r => r.tests[testType].success);

      summary.successRates[testType] = {
        successful: successful.length,
        total: applicable.length,
        rate: applicable.length > 0 ? (successful.length / applicable.length * 100).toFixed(1) : 'N/A'
      };
    });

    // Collect common errors
    this.results.testResults.forEach(result => {
      Object.values(result.tests).forEach(test => {
        if (!test.success && test.error) {
          summary.commonErrors[test.error] = (summary.commonErrors[test.error] || 0) + 1;
        }
      });
    });

    // Generate recommendations
    if (summary.successRates.fileExists.rate < 100) {
      summary.recommendations.push('Some files are missing from Supabase Storage - database cleanup needed');
    }

    if (summary.successRates.supabaseDownload.rate < summary.successRates.fileExists.rate) {
      summary.recommendations.push('File download issues detected - check RLS policies and authentication');
    }

    if (summary.successRates.serviceMethod.rate < summary.successRates.supabaseDownload.rate) {
      summary.recommendations.push('Service method issues detected - check getImageUrl implementation');
    }

    this.results.summary = summary;

    // Log summary
    this.log('📋 DIAGNOSTIC SUMMARY:');
    this.log(`  Total analyses tested: ${total}`);
    this.log('  URL patterns:');
    Object.entries(summary.patterns).forEach(([pattern, count]) => {
      this.log(`    - ${pattern}: ${count}`);
    });
    this.log('  Success rates:');
    Object.entries(summary.successRates).forEach(([test, data]) => {
      this.log(`    - ${test}: ${data.successful}/${data.total} (${data.rate}%)`);
    });
    this.log('  Common errors:');
    Object.entries(summary.commonErrors).forEach(([error, count]) => {
      this.log(`    - ${error}: ${count} occurrences`);
    });
    this.log('  Recommendations:');
    summary.recommendations.forEach(rec => {
      this.log(`    - ${rec}`);
    });
  }
}

// Create and run the debugger
const debugger = new ImageConsistencyDebugger();

// Export to global scope for manual testing
window.imageDebugger = debugger;

// Auto-run the diagnostic
debugger.runFullDiagnostic().then(results => {
  console.log('🎯 Full diagnostic results:', results);
  console.log('💡 Use window.imageDebugger to access the debugger instance for manual testing');
}).catch(error => {
  console.error('❌ Diagnostic failed:', error);
});
