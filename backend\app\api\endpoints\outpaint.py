"""
API endpoints for outpaint (expand image) functionality.
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from app.core.auth import verify_api_key
from app.schemas.outpaint import OutpaintRequest, FrontendOutpaintResponse
from app.services.outpaint_service import outpaint_image_stability

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/outpaint",
    response_model=FrontendOutpaintResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Expand image with AI-generated content",
    description="""
    Expand an image by adding AI-generated content in specified directions using Stability AI v2beta API.

    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Aspect Ratio:** Between 1:2.5 and 2.5:1
    **Cost:** 4 credits per successful generation

    **Parameters:**
    - **image**: The image file to expand (required)
    - **left**: Pixels to expand on the left (0-2000, default: 0)
    - **right**: Pixels to expand on the right (0-2000, default: 0)
    - **up**: Pixels to expand on the top (0-2000, default: 0)
    - **down**: Pixels to expand on the bottom (0-2000, default: 0)
    - **prompt**: Description of what to generate in expanded areas (optional)
    - **creativity**: How creative the expansion should be (0.0-1.0, default: 0.5)
    - **seed**: Random seed for reproducible results (0 = random, default: 0)
    - **output_format**: Output format ("jpeg", "png", or "webp", default: "png")
    - **style_preset**: Style preset for the generation (optional)

    **Requirements:**
    - At least one direction (left, right, up, down) must be greater than 0
    - For best quality, use expansion values smaller or equal to your source image dimensions
    - Use descriptive prompts for better results in expanded areas
    """
)
async def outpaint_image_endpoint(
    image: UploadFile = File(..., description="Image file to expand"),
    left: Optional[int] = Form(0, description="Pixels to expand on the left (0-2000)"),
    right: Optional[int] = Form(0, description="Pixels to expand on the right (0-2000)"),
    up: Optional[int] = Form(0, description="Pixels to expand on the top (0-2000)"),
    down: Optional[int] = Form(0, description="Pixels to expand on the bottom (0-2000)"),
    prompt: Optional[str] = Form(None, description="Description of what to generate in expanded areas"),
    creativity: Optional[float] = Form(0.5, description="How creative the expansion should be (0.0-1.0)"),
    seed: Optional[int] = Form(0, description="Random seed (0 = random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)"),
    style_preset: Optional[str] = Form(None, description="Style preset for the generation")
) -> FrontendOutpaintResponse:
    """
    Expand an image by adding AI-generated content in specified directions using Stability AI v2beta API.

    This endpoint expands images by generating new content in the specified directions.
    At least one direction must have a value greater than 0.
    """
    try:
        logger.info(f"Received outpaint request for file: {image.filename}")
        logger.info(f"Directions: left={left}, right={right}, up={up}, down={down}")
        logger.info(f"Creativity: {creativity}, Prompt: {prompt is not None}")

        # Validar parámetros de dirección
        for direction, value in [("left", left), ("right", right), ("up", up), ("down", down)]:
            if value is not None and (value < 0 or value > 2000):
                raise HTTPException(
                    status_code=400,
                    detail=f"{direction} must be between 0 and 2000"
                )

        # Validar que al menos una dirección esté especificada
        if not any([(left or 0) > 0, (right or 0) > 0, (up or 0) > 0, (down or 0) > 0]):
            raise HTTPException(
                status_code=400,
                detail="At least one direction (left, right, up, down) must be greater than 0"
            )

        # Validar creativity
        if creativity is not None and (creativity < 0.0 or creativity > 1.0):
            raise HTTPException(
                status_code=400,
                detail="creativity must be between 0.0 and 1.0"
            )

        # Validar seed
        if seed is not None and (seed < 0 or seed > 4294967294):
            raise HTTPException(
                status_code=400,
                detail="seed must be between 0 and 4294967294"
            )

        # Validar output_format
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="output_format must be 'jpeg', 'png', or 'webp'"
            )

        # Crear request object
        outpaint_request = OutpaintRequest(
            left=left or 0,
            right=right or 0,
            up=up or 0,
            down=down or 0,
            prompt=prompt.strip() if prompt and prompt.strip() else None,
            creativity=creativity or 0.5,
            seed=seed or 0,
            output_format=output_format or "png",
            style_preset=style_preset
        )

        # Llamar al servicio de Stability AI
        service_response = await outpaint_image_stability(
            image_file=image,
            request=outpaint_request
        )

        # Crear data URL para el frontend (igual que erase y inpaint)
        mime_type = f"image/{output_format or 'png'}"
        image_data_url = f"data:{mime_type};base64,{service_response.image}"

        logger.info(f"Outpaint operation completed successfully. Result size: {len(image_data_url)} chars")

        return FrontendOutpaintResponse(
            success=True,
            image_url=image_data_url,
            seed=service_response.seed,
            finish_reason=service_response.finish_reason,
            metadata={
                "left": left,
                "right": right,
                "up": up,
                "down": down,
                "prompt": prompt,
                "creativity": creativity,
                "seed": seed,
                "output_format": output_format,
                "style_preset": style_preset,
                "original_filename": image.filename
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in outpaint operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during outpaint operation: {str(e)}"
        )
