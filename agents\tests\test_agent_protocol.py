"""
Tests for the AgentProtocol class
"""

import unittest
import asyncio
from unittest.mock import MagicMock, patch

from agents.protocols import (
    AgentProtocol,
    AgentIdentity,
    AgentRole,
    MessageType,
    AgentRequest,
    AgentResponse
)

class TestAgentProtocol(unittest.TestCase):
    """Test cases for the AgentProtocol class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.identity = AgentIdentity(
            id="test_agent",
            name="Test Agent",
            role=AgentRole.EXECUTOR,
            capabilities=["test"]
        )
        self.protocol = AgentProtocol(self.identity)
    
    def test_initialization(self):
        """Test protocol initialization."""
        self.assertEqual(self.protocol.identity, self.identity)
        self.assertIsNotNone(self.protocol.event_emitter)
        self.assertEqual(len(self.protocol.pending_requests), 0)
        self.assertEqual(len(self.protocol.message_handlers), len(MessageType))
    
    def test_register_handler(self):
        """Test registering a message handler."""
        # Create a handler
        handler = MagicMock()
        
        # Register the handler
        self.protocol.register_handler(MessageType.REQUEST, handler)
        
        # Check that the handler was registered
        self.assertIn(handler, self.protocol.message_handlers[MessageType.REQUEST])
    
    def test_unregister_handler(self):
        """Test unregistering a message handler."""
        # Create a handler
        handler = MagicMock()
        
        # Register the handler
        self.protocol.register_handler(MessageType.REQUEST, handler)
        
        # Check that the handler was registered
        self.assertIn(handler, self.protocol.message_handlers[MessageType.REQUEST])
        
        # Unregister the handler
        self.protocol.unregister_handler(MessageType.REQUEST, handler)
        
        # Check that the handler was unregistered
        self.assertNotIn(handler, self.protocol.message_handlers[MessageType.REQUEST])
    
    @patch.object(AgentProtocol, 'handle_message')
    def test_handle_message(self, mock_handle_message):
        """Test handling a message."""
        # Create a message
        message = AgentRequest(
            id="test_message",
            sender="test_sender",
            recipient=self.identity,
            message_type=MessageType.REQUEST,
            content="Test content",
            timestamp=123456789,
            expects_response=True
        )
        
        # Create a handler
        handler = MagicMock()
        
        # Register the handler
        self.protocol.register_handler(MessageType.REQUEST, handler)
        
        # Handle the message
        asyncio.run(self.protocol.handle_message(message))
        
        # Check that the handler was called
        handler.assert_called_once_with(message)
    
    @patch.object(AgentProtocol, 'event_emitter')
    def test_send_notification(self, mock_event_emitter):
        """Test sending a notification."""
        # Send a notification
        asyncio.run(self.protocol.send_notification(
            recipient="test_recipient",
            content="Test content"
        ))
        
        # Check that the event was emitted
        mock_event_emitter.emit.assert_called_once()
        args = mock_event_emitter.emit.call_args[0]
        self.assertEqual(args[0], 'message')
        message = args[1]
        self.assertEqual(message.sender, self.identity)
        self.assertEqual(message.recipient, "test_recipient")
        self.assertEqual(message.message_type, MessageType.NOTIFICATION)
        self.assertEqual(message.content, "Test content")
    
    @patch.object(AgentProtocol, 'event_emitter')
    def test_respond_to_request(self, mock_event_emitter):
        """Test responding to a request."""
        # Create a request
        request = AgentRequest(
            id="test_request",
            sender="test_sender",
            recipient=self.identity,
            message_type=MessageType.REQUEST,
            content="Test content",
            timestamp=123456789,
            expects_response=True
        )
        
        # Respond to the request
        asyncio.run(self.protocol.respond_to_request(
            request=request,
            content="Test response",
            status="success"
        ))
        
        # Check that the event was emitted
        mock_event_emitter.emit.assert_called_once()
        args = mock_event_emitter.emit.call_args[0]
        self.assertEqual(args[0], 'message')
        response = args[1]
        self.assertEqual(response.sender, self.identity)
        self.assertEqual(response.recipient, "test_sender")
        self.assertEqual(response.message_type, MessageType.RESPONSE)
        self.assertEqual(response.request_id, "test_request")
        self.assertEqual(response.content, "Test response")
        self.assertEqual(response.status, "success")

if __name__ == "__main__":
    unittest.main()
