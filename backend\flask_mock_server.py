"""
Flask Mock Server for Testing Frontend Integration

This script creates a simple Flask server that mocks the behavior of the
agent system API endpoints for testing the frontend integration.
"""

import json
import logging
import time
import uuid
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s: %(message)s")
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

@app.route("/api/v1/crew/run", methods=["POST"])
def run_crew():
    """Mock endpoint for running a crew of agents."""
    data = request.json
    logger.info(f"Received crew run request: {data.get('prompt')}")

    # Generate a unique request ID
    request_id = str(uuid.uuid4())

    # Create a mock response
    current_time = int(time.time() * 1000)

    # Create reasoning trace
    reasoning_trace = [
        {
            "type": "prompt",
            "content": data.get("prompt", ""),
            "timestamp": str(current_time)
        },
        {
            "type": "info",
            "content": "Analyzing request...",
            "agent": "emma",
            "timestamp": str(current_time + 1000)
        },
        {
            "type": "asset",
            "content": "Generated content based on the prompt.",
            "agent": "content",
            "timestamp": str(current_time + 2000)
        }
    ]

    # Create response
    response = {
        "request_id": request_id,
        "status": "success",
        "result": f"This is a mock response for the prompt: '{data.get('prompt')}'",
        "reasoning_trace": reasoning_trace,
        "metadata": {
            "execution_time_seconds": 0.5,
            "crew_id": data.get("crew_id")
        }
    }

    return jsonify(response)

@app.route("/api/v1/crew/chat", methods=["POST"])
def chat_with_agent():
    """Mock endpoint for chatting with a specific agent."""
    data = request.json
    logger.info(f"Received agent chat request: {data}")
    logger.info(f"Headers: {dict(request.headers)}")

    # Create a mock response based on the agent
    agent_id = data.get("agent_id")
    if agent_id == "emma":
        response = "Hello! I'm Emma, the main coordinator agent. How can I help you today?"
    elif agent_id == "seo":
        response = "Hi there! I'm the SEO specialist. I can help you optimize your content for search engines."
    elif agent_id == "content":
        response = "Greetings! I'm the Content Creator agent. I specialize in generating high-quality content."
    else:
        response = f"Hello from agent {agent_id}. I received your message: '{data.get('message')}'"

    return jsonify({
        "response": response,
        "metadata": {
            "agent_id": agent_id,
            "timestamp": time.time()
        }
    })

@app.route("/api/v1/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok"})

@app.route("/api/v1/crew/chat", methods=["OPTIONS"])
def chat_options():
    """Handle OPTIONS requests for CORS preflight."""
    response = jsonify({"status": "ok"})
    response.headers.add("Access-Control-Allow-Origin", "*")
    response.headers.add("Access-Control-Allow-Headers", "Content-Type,Authorization")
    response.headers.add("Access-Control-Allow-Methods", "GET,POST,OPTIONS")
    return response

@app.route("/api/v1/crew/run", methods=["OPTIONS"])
def run_options():
    """Handle OPTIONS requests for CORS preflight."""
    response = jsonify({"status": "ok"})
    response.headers.add("Access-Control-Allow-Origin", "*")
    response.headers.add("Access-Control-Allow-Headers", "Content-Type,Authorization")
    response.headers.add("Access-Control-Allow-Methods", "GET,POST,OPTIONS")
    return response

if __name__ == "__main__":
    logger.info("Starting mock server on http://localhost:8001")
    app.run(host="0.0.0.0", port=8001, debug=True)
