"""
Emma Visual Workflows API - Sistema simplificado de workflows visuales
Ejecuta workflows paso a paso usando las APIs existentes de Emma Studio

El usuario solo selecciona herramientas, nosotros manejamos:
- APIs keys y configuraciones
- Llamadas a servicios externos
- Procesamiento y optimización
- Manejo de errores y reintentos
"""

import logging
import httpx
import base64
import os
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# Importar servicios existentes de Emma Studio
from app.services.ideogram_service import IdeogramService
from app.services.openai_image_service import OpenAIImageService
from app.services.luma_service import LumaService

# Configurar directorios de almacenamiento
STORAGE_DIR = "storage"
WORKFLOWS_DIR = os.path.join(STORAGE_DIR, "workflows")
TEMP_DIR = os.path.join(STORAGE_DIR, "temp")

# Crear directorios si no existen
os.makedirs(WORKFLOWS_DIR, exist_ok=True)
os.makedirs(TEMP_DIR, exist_ok=True)

router = APIRouter()

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Modelos Pydantic
class WorkflowNode(BaseModel):
    """Nodo en el workflow"""
    id: str
    type: str
    inputs: Dict[str, Any]
    position: Dict[str, float]

class WorkflowEdge(BaseModel):
    """Conexión entre nodos"""
    source: str
    target: str
    sourceHandle: Optional[str] = None
    targetHandle: Optional[str] = None

class EmmaWorkflowRequest(BaseModel):
    """Request para ejecutar workflow Emma"""
    workflow: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = {}

class WorkflowExecutionResponse(BaseModel):
    """Response de ejecución"""
    success: bool
    execution_id: str
    message: str
    results: Dict[str, Any] = {}
    errors: List[str] = []

class WorkflowSaveRequest(BaseModel):
    """Request para guardar workflow"""
    name: str
    description: str
    workflow: Dict[str, Any]
    tags: List[str] = []
    category: str = "general"

class WorkflowListResponse(BaseModel):
    """Response para listar workflows"""
    success: bool
    workflows: List[Dict[str, Any]] = []

# Utilidades para manejo de archivos
class FileManager:
    """Maneja archivos temporales y permanentes para workflows"""

    def __init__(self):
        self.temp_dir = "/tmp/emma_workflows"
        self.storage_dir = "storage/workflows"
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.storage_dir, exist_ok=True)

    def save_temp_file(self, content: bytes, extension: str = "png") -> str:
        """Guarda archivo temporal y retorna la ruta"""
        filename = f"{uuid.uuid4()}.{extension}"
        filepath = os.path.join(self.temp_dir, filename)

        with open(filepath, "wb") as f:
            f.write(content)

        return filepath

    def save_base64_image(self, base64_data: str, extension: str = "png") -> str:
        """Convierte base64 a archivo y lo guarda"""
        try:
            # Remover prefijo data:image si existe
            if "," in base64_data:
                base64_data = base64_data.split(",")[1]

            image_data = base64.b64decode(base64_data)
            return self.save_temp_file(image_data, extension)
        except Exception as e:
            logger.error(f"Error guardando imagen base64: {e}")
            raise

    def image_to_base64(self, image_path: str) -> str:
        """Convierte imagen a base64"""
        try:
            with open(image_path, "rb") as f:
                image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.error(f"Error convirtiendo imagen a base64: {e}")
            raise

# Ejecutor de workflows Emma
class EmmaWorkflowExecutor:
    """Ejecuta workflows Emma paso a paso"""

    def __init__(self):
        # Inicializar servicios
        self.ideogram_service = IdeogramService()
        self.openai_service = OpenAIImageService()
        self.luma_service = LumaService()
        self.file_manager = FileManager()

        # Mapeo de ejecutores de nodos
        self.node_executors = {
            'text-input': self._execute_text_input,
            'image-input': self._execute_image_input,
            'ideogram-generator': self._execute_ideogram_generator,
            'dalle-generator': self._execute_dalle_generator,
            'upscale': self._execute_upscale,
            'background-remover': self._execute_background_remover,
            'style-transfer': self._execute_style_transfer,
            'video-generator': self._execute_video_generator,
            'image-output': self._execute_image_output,
            'video-output': self._execute_video_output,
        }
    
    async def execute_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta un workflow completo"""
        try:
            nodes = workflow_data.get('nodes', [])
            edges = workflow_data.get('edges', [])
            
            # Construir grafo de dependencias
            dependency_graph = self._build_dependency_graph(nodes, edges)
            
            # Ejecutar nodos en orden topológico
            execution_order = self._topological_sort(dependency_graph)
            
            # Almacenar resultados de cada nodo
            node_results = {}
            
            for node_id in execution_order:
                node = next((n for n in nodes if n['id'] == node_id), None)
                if not node:
                    continue
                
                # Resolver inputs desde nodos anteriores
                resolved_inputs = self._resolve_node_inputs(node, node_results, edges)
                
                # Ejecutar nodo
                result = await self._execute_node(node, resolved_inputs)
                node_results[node_id] = result
                
                logging.info(f"Nodo {node_id} ejecutado: {result.get('success', False)}")
            
            return {
                'success': True,
                'execution_id': f"emma_exec_{int(datetime.now().timestamp())}",
                'message': 'Workflow ejecutado exitosamente',
                'results': node_results
            }
            
        except Exception as e:
            logging.error(f"Error ejecutando workflow: {e}")
            return {
                'success': False,
                'execution_id': '',
                'message': f'Error: {str(e)}',
                'results': {}
            }
    
    def _build_dependency_graph(self, nodes: List[Dict], edges: List[Dict]) -> Dict[str, List[str]]:
        """Construye grafo de dependencias"""
        graph = {node['id']: [] for node in nodes}
        
        for edge in edges:
            # target depende de source
            if edge['target'] in graph:
                graph[edge['target']].append(edge['source'])
        
        return graph
    
    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """Ordenamiento topológico para determinar orden de ejecución"""
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(node):
            if node in temp_visited:
                raise Exception("Ciclo detectado en el workflow")
            if node in visited:
                return
            
            temp_visited.add(node)
            for dependency in graph[node]:
                visit(dependency)
            temp_visited.remove(node)
            visited.add(node)
            result.append(node)
        
        for node in graph:
            if node not in visited:
                visit(node)
        
        return result
    
    def _resolve_node_inputs(self, node: Dict, node_results: Dict, edges: List[Dict]) -> Dict[str, Any]:
        """Resuelve inputs del nodo desde resultados de nodos anteriores"""
        resolved_inputs = node.get('data', {}).get('inputs', {}).copy()

        # Buscar conexiones que alimentan este nodo
        for edge in edges:
            if edge['target'] == node['id']:
                source_id = edge['source']
                source_handle = edge.get('sourceHandle', 'output')
                target_handle = edge.get('targetHandle', 'input')

                # Obtener resultado del nodo fuente
                if source_id in node_results:
                    source_result = node_results[source_id]
                    if source_result.get('success') and 'outputs' in source_result:
                        if source_handle in source_result['outputs']:
                            resolved_inputs[target_handle] = source_result['outputs'][source_handle]
                            logging.info(f"Transferido {source_handle} -> {target_handle}: {str(source_result['outputs'][source_handle])[:100]}...")

        return resolved_inputs
    
    async def _execute_node(self, node: Dict, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta un nodo individual"""
        node_type = node.get('type', 'unknown')
        
        if node_type in self.node_executors:
            return await self.node_executors[node_type](node, inputs)
        else:
            return {
                'success': False,
                'message': f'Tipo de nodo no soportado: {node_type}',
                'outputs': {}
            }
    
    # Ejecutores específicos para cada tipo de nodo
    async def _execute_text_input(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta nodo de entrada de texto"""
        try:
            # Obtener texto desde inputs del nodo o desde conexiones
            text = inputs.get('text', node.get('inputs', {}).get('text', ''))

            if not text:
                return {
                    'success': False,
                    'message': 'No se proporcionó texto de entrada',
                    'outputs': {}
                }

            logger.info(f"📝 Procesando texto: {text[:100]}...")

            return {
                'success': True,
                'message': 'Texto procesado correctamente',
                'outputs': {
                    'text': text
                }
            }
        except Exception as e:
            logger.error(f"Error en text_input: {e}")
            return {
                'success': False,
                'message': f'Error procesando texto: {str(e)}',
                'outputs': {}
            }

    async def _execute_image_input(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta nodo de entrada de imagen"""
        try:
            # Obtener imagen desde inputs (puede ser base64, URL o path)
            image_data = inputs.get('image', node.get('inputs', {}).get('image', ''))

            if not image_data:
                return {
                    'success': False,
                    'message': 'No se proporcionó imagen de entrada',
                    'outputs': {}
                }

            logger.info("🖼️ Procesando imagen de entrada...")

            # Si es base64, convertir a archivo temporal
            if image_data.startswith('data:image') or len(image_data) > 1000:
                try:
                    image_path = self.file_manager.save_base64_image(image_data)
                    image_url = f"/tmp/workflows/{os.path.basename(image_path)}"
                except:
                    # Si falla, asumir que es una URL
                    image_url = image_data
            else:
                # Asumir que es una URL
                image_url = image_data

            return {
                'success': True,
                'message': 'Imagen procesada correctamente',
                'outputs': {
                    'image': image_url,
                    'url': image_url
                }
            }
        except Exception as e:
            logger.error(f"Error en image_input: {e}")
            return {
                'success': False,
                'message': f'Error procesando imagen: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_ideogram_generator(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta generación con Ideogram usando el servicio real"""
        try:
            # Obtener parámetros del nodo
            prompt = inputs.get('prompt', node.get('inputs', {}).get('prompt', ''))
            model = inputs.get('model', node.get('inputs', {}).get('model', 'ideogram-3.0'))
            aspect_ratio = inputs.get('aspect_ratio', node.get('inputs', {}).get('aspect_ratio', '1:1'))
            style = inputs.get('style', node.get('inputs', {}).get('style', 'auto'))

            if not prompt:
                return {
                    'success': False,
                    'message': 'Se requiere un prompt para generar la imagen',
                    'outputs': {}
                }

            logger.info(f"🎨 Generando imagen con Ideogram: {prompt[:100]}...")

            # Mapear aspect_ratio a dimensiones
            aspect_mapping = {
                '1:1': '1024x1024',
                '16:9': '1792x1024',
                '9:16': '1024x1792',
                '4:3': '1024x768',
                '3:4': '768x1024'
            }

            size = aspect_mapping.get(aspect_ratio, '1024x1024')

            # Llamar al servicio de Ideogram
            result = await self.ideogram_service.generate_ad(
                prompt=prompt,
                size=size
            )

            if result.get('success'):
                image_url = result.get('image_url')

                return {
                    'success': True,
                    'message': 'Imagen generada exitosamente con Ideogram',
                    'outputs': {
                        'image': image_url,
                        'url': image_url
                    },
                    'metadata': {
                        'model': model,
                        'aspect_ratio': aspect_ratio,
                        'style': style,
                        'prompt': prompt
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f"Error de Ideogram: {result.get('error', 'Error desconocido')}",
                    'outputs': {}
                }

        except Exception as e:
            logger.error(f"Error en ideogram_generator: {e}")
            return {
                'success': False,
                'message': f'Error generando imagen: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_dalle_generator(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta generación con DALL-E usando el servicio real"""
        try:
            # Obtener parámetros del nodo
            prompt = inputs.get('prompt', node.get('inputs', {}).get('prompt', ''))
            model = inputs.get('model', node.get('inputs', {}).get('model', 'dall-e-3'))
            size = inputs.get('size', node.get('inputs', {}).get('size', '1024x1024'))
            quality = inputs.get('quality', node.get('inputs', {}).get('quality', 'standard'))

            if not prompt:
                return {
                    'success': False,
                    'message': 'Se requiere un prompt para generar la imagen',
                    'outputs': {}
                }

            logger.info(f"🤖 Generando imagen con DALL-E: {prompt[:100]}...")

            # Llamar al servicio de OpenAI
            result = await self.openai_service.generate_image(
                prompt=prompt,
                size=size
            )

            if result.get('success'):
                image_url = result.get('image_url')

                return {
                    'success': True,
                    'message': 'Imagen generada exitosamente con DALL-E',
                    'outputs': {
                        'image': image_url,
                        'url': image_url
                    },
                    'metadata': {
                        'model': model,
                        'size': size,
                        'quality': quality,
                        'prompt': prompt
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f"Error de DALL-E: {result.get('error', 'Error desconocido')}",
                    'outputs': {}
                }

        except Exception as e:
            logger.error(f"Error en dalle_generator: {e}")
            return {
                'success': False,
                'message': f'Error generando imagen: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_upscale(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta upscaling con Stability AI usando la API real"""
        try:
            # Obtener imagen de entrada
            image_input = inputs.get('image', node.get('inputs', {}).get('image', ''))
            scale_factor = inputs.get('scale_factor', node.get('inputs', {}).get('scale_factor', 2))
            creativity = inputs.get('creativity', node.get('inputs', {}).get('creativity', 0.3))

            if not image_input:
                return {
                    'success': False,
                    'message': 'Se requiere una imagen para hacer upscale',
                    'outputs': {}
                }

            logger.info(f"⬆️ Mejorando imagen con factor {scale_factor}...")

            # Llamar a la API de upscaling existente
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    "http://localhost:8000/api/v1/ai-editor/upscale",
                    json={
                        "image_url": image_input,
                        "scale_factor": str(scale_factor),
                        "creativity": creativity
                    }
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        return {
                            'success': True,
                            'message': 'Imagen mejorada exitosamente',
                            'outputs': {
                                'image': result.get('upscaled_image_url'),
                                'url': result.get('upscaled_image_url')
                            },
                            'metadata': {
                                'scale_factor': scale_factor,
                                'creativity': creativity
                            }
                        }

                return {
                    'success': False,
                    'message': 'Error en el servicio de upscaling',
                    'outputs': {}
                }

        except Exception as e:
            logger.error(f"Error en upscale: {e}")
            return {
                'success': False,
                'message': f'Error mejorando imagen: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_background_remover(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta remoción de fondo usando la API real de Stability AI"""
        try:
            # Obtener imagen de entrada
            image_input = inputs.get('image', node.get('inputs', {}).get('image', ''))

            if not image_input:
                return {
                    'success': False,
                    'message': 'Se requiere una imagen para remover el fondo',
                    'outputs': {}
                }

            logger.info("🗑️ Removiendo fondo de imagen...")

            # Llamar a la API de background removal existente
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    "http://localhost:8000/api/stability-remove-bg/remove-background",
                    json={
                        "image_url": image_input
                    }
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        return {
                            'success': True,
                            'message': 'Fondo removido exitosamente',
                            'outputs': {
                                'image': result.get('result_url'),
                                'url': result.get('result_url')
                            }
                        }

                return {
                    'success': False,
                    'message': 'Error en el servicio de remoción de fondo',
                    'outputs': {}
                }

        except Exception as e:
            logger.error(f"Error en background_remover: {e}")
            return {
                'success': False,
                'message': f'Error removiendo fondo: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_style_transfer(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta transferencia de estilo usando la API real"""
        try:
            # Obtener imágenes de entrada
            content_image = inputs.get('content_image', node.get('inputs', {}).get('content_image', ''))
            style_image = inputs.get('style_image', node.get('inputs', {}).get('style_image', ''))
            strength = inputs.get('strength', node.get('inputs', {}).get('strength', 0.7))

            if not content_image or not style_image:
                return {
                    'success': False,
                    'message': 'Se requieren imagen base e imagen de estilo',
                    'outputs': {}
                }

            logger.info("🎭 Transfiriendo estilo entre imágenes...")

            # Llamar a la API de style transfer existente
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    "http://localhost:8000/api/v1/images/style-transfer",
                    json={
                        "content_image_url": content_image,
                        "style_image_url": style_image,
                        "strength": strength
                    }
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        return {
                            'success': True,
                            'message': 'Estilo transferido exitosamente',
                            'outputs': {
                                'image': result.get('result_url'),
                                'url': result.get('result_url')
                            },
                            'metadata': {
                                'strength': strength
                            }
                        }

                return {
                    'success': False,
                    'message': 'Error en el servicio de transferencia de estilo',
                    'outputs': {}
                }

        except Exception as e:
            logger.error(f"Error en style_transfer: {e}")
            return {
                'success': False,
                'message': f'Error transfiriendo estilo: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_video_generator(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta generación de video usando Luma Labs"""
        try:
            # Obtener parámetros del nodo
            prompt = inputs.get('prompt', node.get('inputs', {}).get('prompt', ''))
            reference_image = inputs.get('reference_image', node.get('inputs', {}).get('reference_image', ''))
            duration = inputs.get('duration', node.get('inputs', {}).get('duration', '5'))
            aspect_ratio = inputs.get('aspect_ratio', node.get('inputs', {}).get('aspect_ratio', '16:9'))

            if not prompt:
                return {
                    'success': False,
                    'message': 'Se requiere un prompt para generar el video',
                    'outputs': {}
                }

            logger.info(f"🎬 Generando video con Luma Labs: {prompt[:100]}...")

            # Llamar a la API de Luma Labs existente
            async with httpx.AsyncClient(timeout=300.0) as client:  # Timeout más largo para videos
                payload = {
                    "prompt": prompt,
                    "duration": duration,
                    "aspect_ratio": aspect_ratio
                }

                if reference_image:
                    payload["reference_image"] = reference_image

                response = await client.post(
                    "http://localhost:8000/api/luma-labs/generate",
                    json=payload
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        return {
                            'success': True,
                            'message': 'Video generado exitosamente',
                            'outputs': {
                                'video': result.get('video_url'),
                                'url': result.get('video_url')
                            },
                            'metadata': {
                                'duration': duration,
                                'aspect_ratio': aspect_ratio,
                                'prompt': prompt
                            }
                        }

                return {
                    'success': False,
                    'message': 'Error en el servicio de generación de video',
                    'outputs': {}
                }

        except Exception as e:
            logger.error(f"Error en video_generator: {e}")
            return {
                'success': False,
                'message': f'Error generando video: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_image_output(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta guardado de imagen con opciones de formato"""
        try:
            # Obtener parámetros
            image_input = inputs.get('image', node.get('inputs', {}).get('image', ''))
            filename = inputs.get('filename', node.get('inputs', {}).get('filename', 'emma-workflow'))
            format_type = inputs.get('format', node.get('inputs', {}).get('format', 'PNG'))

            if not image_input:
                return {
                    'success': False,
                    'message': 'Se requiere una imagen para guardar',
                    'outputs': {}
                }

            logger.info(f"💾 Guardando imagen como {filename}.{format_type.lower()}...")

            # Por ahora, simplemente retornamos la URL de la imagen
            # En el futuro se puede implementar conversión de formato y almacenamiento
            final_url = image_input

            return {
                'success': True,
                'message': f'Imagen guardada como {filename}.{format_type.lower()}',
                'outputs': {
                    'url': final_url
                },
                'metadata': {
                    'filename': filename,
                    'format': format_type
                }
            }

        except Exception as e:
            logger.error(f"Error en image_output: {e}")
            return {
                'success': False,
                'message': f'Error guardando imagen: {str(e)}',
                'outputs': {}
            }

    async def _execute_video_output(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta guardado de video"""
        try:
            # Obtener parámetros
            video_input = inputs.get('video', node.get('inputs', {}).get('video', ''))
            filename = inputs.get('filename', node.get('inputs', {}).get('filename', 'emma-video'))

            if not video_input:
                return {
                    'success': False,
                    'message': 'Se requiere un video para guardar',
                    'outputs': {}
                }

            logger.info(f"🎥 Guardando video como {filename}.mp4...")

            # Por ahora, simplemente retornamos la URL del video
            final_url = video_input

            return {
                'success': True,
                'message': f'Video guardado como {filename}.mp4',
                'outputs': {
                    'url': final_url
                },
                'metadata': {
                    'filename': filename
                }
            }

        except Exception as e:
            logger.error(f"Error en video_output: {e}")
            return {
                'success': False,
                'message': f'Error guardando video: {str(e)}',
                'outputs': {}
            }

# Almacén en memoria para ejecuciones (en producción usar Redis/DB)
execution_store = {}

# Instancia global del ejecutor
workflow_executor = EmmaWorkflowExecutor()

@router.post("/execute")
async def execute_emma_workflow(request: EmmaWorkflowRequest):
    """Ejecuta un workflow Emma de forma síncrona"""
    try:
        logger.info("🚀 Iniciando ejecución de workflow...")

        # Validar workflow antes de ejecutar
        validation_result = await validate_workflow_internal(request.workflow)
        if not validation_result['valid']:
            return WorkflowExecutionResponse(
                success=False,
                execution_id="",
                message="Workflow inválido",
                results={},
                errors=validation_result['errors']
            )

        result = await workflow_executor.execute_workflow(request.workflow)

        logger.info(f"✅ Workflow ejecutado: {result['execution_id']}")

        return WorkflowExecutionResponse(
            success=result['success'],
            execution_id=result['execution_id'],
            message=result['message'],
            results=result['results'],
            errors=result.get('errors', [])
        )

    except Exception as e:
        logger.error(f"Error en execute_emma_workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Error ejecutando workflow: {str(e)}")

@router.post("/execute-async")
async def execute_workflow_async(request: EmmaWorkflowRequest):
    """Inicia la ejecución asíncrona de un workflow"""
    try:
        execution_id = f"async_exec_{uuid.uuid4().hex[:8]}"

        # Validar workflow
        validation_result = await validate_workflow_internal(request.workflow)
        if not validation_result['valid']:
            return {
                "success": False,
                "execution_id": execution_id,
                "message": "Workflow inválido",
                "errors": validation_result['errors']
            }

        # Guardar estado inicial
        execution_store[execution_id] = {
            "status": "running",
            "started_at": datetime.now().isoformat(),
            "progress": 0,
            "current_node": None,
            "results": {},
            "errors": []
        }

        logger.info(f"🚀 Iniciando ejecución asíncrona: {execution_id}")

        # Ejecutar en background (en producción usar Celery)
        import asyncio
        asyncio.create_task(execute_workflow_background(execution_id, request.workflow))

        return {
            "success": True,
            "execution_id": execution_id,
            "message": "Ejecución iniciada",
            "status_url": f"/api/v1/emma-workflows/status/{execution_id}"
        }

    except Exception as e:
        logger.error(f"Error iniciando ejecución asíncrona: {e}")
        raise HTTPException(status_code=500, detail=f"Error iniciando ejecución: {str(e)}")

async def execute_workflow_background(execution_id: str, workflow_data: Dict[str, Any]):
    """Ejecuta workflow en background y actualiza el estado"""
    try:
        execution_store[execution_id]["status"] = "running"

        result = await workflow_executor.execute_workflow(workflow_data)

        execution_store[execution_id].update({
            "status": "completed" if result['success'] else "failed",
            "completed_at": datetime.now().isoformat(),
            "progress": 100,
            "results": result['results'],
            "errors": result.get('errors', []),
            "message": result['message']
        })

        logger.info(f"✅ Ejecución asíncrona completada: {execution_id}")

    except Exception as e:
        execution_store[execution_id].update({
            "status": "failed",
            "completed_at": datetime.now().isoformat(),
            "progress": 0,
            "errors": [str(e)],
            "message": f"Error en ejecución: {str(e)}"
        })
        logger.error(f"❌ Error en ejecución asíncrona {execution_id}: {e}")

@router.get("/status/{execution_id}")
async def get_execution_status(execution_id: str):
    """Obtiene el estado de una ejecución asíncrona"""
    if execution_id not in execution_store:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")

    return {
        "success": True,
        "execution_id": execution_id,
        **execution_store[execution_id]
    }

async def validate_workflow_internal(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """Validación interna de workflow"""
    nodes = workflow_data.get('nodes', [])

    validation_errors = []

    if not nodes:
        validation_errors.append("El workflow debe tener al menos un nodo")

    # Validar tipos de nodos
    for node in nodes:
        node_type = node.get('type')
        if node_type not in workflow_executor.node_executors:
            validation_errors.append(f"Tipo de nodo no soportado: {node_type}")

    return {
        "valid": len(validation_errors) == 0,
        "errors": validation_errors
    }

@router.post("/save")
async def save_workflow(request: WorkflowSaveRequest):
    """Guarda un workflow Emma"""
    try:
        workflow_id = f"workflow_{uuid.uuid4().hex[:8]}"

        workflow_data = {
            "id": workflow_id,
            "name": request.name,
            "description": request.description,
            "workflow": request.workflow,
            "tags": request.tags,
            "category": request.category,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # Guardar en archivo JSON (en producción usar base de datos)
        workflows_dir = "storage/workflows"
        os.makedirs(workflows_dir, exist_ok=True)

        workflow_file = os.path.join(workflows_dir, f"{workflow_id}.json")

        import json
        with open(workflow_file, 'w', encoding='utf-8') as f:
            json.dump(workflow_data, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Workflow guardado: {request.name} ({workflow_id})")

        return {
            "success": True,
            "message": "Workflow guardado exitosamente",
            "workflow_id": workflow_id
        }

    except Exception as e:
        logger.error(f"Error guardando workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Error guardando workflow: {str(e)}")

@router.get("/list")
async def list_workflows():
    """Lista todos los workflows guardados"""
    try:
        workflows_dir = "storage/workflows"
        workflows = []

        if os.path.exists(workflows_dir):
            import json
            for filename in os.listdir(workflows_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(workflows_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            workflow_data = json.load(f)
                            # Solo incluir metadatos, no el workflow completo
                            workflows.append({
                                "id": workflow_data.get("id"),
                                "name": workflow_data.get("name"),
                                "description": workflow_data.get("description"),
                                "tags": workflow_data.get("tags", []),
                                "category": workflow_data.get("category", "general"),
                                "created_at": workflow_data.get("created_at"),
                                "updated_at": workflow_data.get("updated_at")
                            })
                    except Exception as e:
                        logger.warning(f"Error leyendo workflow {filename}: {e}")

        return WorkflowListResponse(
            success=True,
            workflows=workflows
        )

    except Exception as e:
        logger.error(f"Error listando workflows: {e}")
        raise HTTPException(status_code=500, detail=f"Error listando workflows: {str(e)}")

@router.get("/load/{workflow_id}")
async def load_workflow(workflow_id: str):
    """Carga un workflow específico"""
    try:
        workflow_file = f"storage/workflows/{workflow_id}.json"

        if not os.path.exists(workflow_file):
            raise HTTPException(status_code=404, detail="Workflow no encontrado")

        import json
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow_data = json.load(f)

        logger.info(f"📂 Workflow cargado: {workflow_data.get('name')} ({workflow_id})")

        return {
            "success": True,
            "workflow": workflow_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cargando workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Error cargando workflow: {str(e)}")

@router.delete("/delete/{workflow_id}")
async def delete_workflow(workflow_id: str):
    """Elimina un workflow"""
    try:
        workflow_file = f"storage/workflows/{workflow_id}.json"

        if not os.path.exists(workflow_file):
            raise HTTPException(status_code=404, detail="Workflow no encontrado")

        os.remove(workflow_file)

        logger.info(f"🗑️ Workflow eliminado: {workflow_id}")

        return {
            "success": True,
            "message": "Workflow eliminado exitosamente"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error eliminando workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Error eliminando workflow: {str(e)}")

@router.get("/templates")
async def get_workflow_templates():
    """Obtiene templates predefinidos de workflows"""
    templates = {
        "image_generation": {
            "name": "Generación de Imagen Simple",
            "description": "Genera una imagen usando Ideogram AI",
            "workflow": {
                "nodes": [
                    {
                        "id": "text-1",
                        "type": "text-input",
                        "position": {"x": 100, "y": 100},
                        "inputs": {"text": "Un hermoso paisaje montañoso al atardecer"}
                    },
                    {
                        "id": "ideogram-1",
                        "type": "ideogram-generator",
                        "position": {"x": 400, "y": 100},
                        "inputs": {"model": "ideogram-3.0", "aspect_ratio": "16:9"}
                    },
                    {
                        "id": "output-1",
                        "type": "image-output",
                        "position": {"x": 700, "y": 100},
                        "inputs": {"filename": "paisaje", "format": "PNG"}
                    }
                ],
                "edges": [
                    {
                        "source": "text-1",
                        "target": "ideogram-1",
                        "sourceHandle": "text",
                        "targetHandle": "prompt"
                    },
                    {
                        "source": "ideogram-1",
                        "target": "output-1",
                        "sourceHandle": "image",
                        "targetHandle": "image"
                    }
                ]
            }
        },
        "image_enhancement": {
            "name": "Mejora de Imagen",
            "description": "Sube una imagen y mejórala con upscaling",
            "workflow": {
                "nodes": [
                    {
                        "id": "image-1",
                        "type": "image-input",
                        "position": {"x": 100, "y": 100},
                        "inputs": {}
                    },
                    {
                        "id": "upscale-1",
                        "type": "upscale",
                        "position": {"x": 400, "y": 100},
                        "inputs": {"scale_factor": 2, "creativity": 0.3}
                    },
                    {
                        "id": "output-1",
                        "type": "image-output",
                        "position": {"x": 700, "y": 100},
                        "inputs": {"filename": "imagen_mejorada", "format": "PNG"}
                    }
                ],
                "edges": [
                    {
                        "source": "image-1",
                        "target": "upscale-1",
                        "sourceHandle": "image",
                        "targetHandle": "image"
                    },
                    {
                        "source": "upscale-1",
                        "target": "output-1",
                        "sourceHandle": "image",
                        "targetHandle": "image"
                    }
                ]
            }
        }
    }

    return {
        "success": True,
        "templates": templates
    }

@router.post("/validate")
async def validate_workflow(request: EmmaWorkflowRequest):
    """Valida un workflow antes de ejecutarlo"""
    try:
        workflow_data = request.workflow
        nodes = workflow_data.get('nodes', [])
        edges = workflow_data.get('edges', [])

        validation_errors = []
        warnings = []

        # Validar que hay nodos
        if not nodes:
            validation_errors.append("El workflow debe tener al menos un nodo")

        # Validar nodos
        node_ids = set()
        for node in nodes:
            node_id = node.get('id')
            node_type = node.get('type')

            if not node_id:
                validation_errors.append("Todos los nodos deben tener un ID")
                continue

            if node_id in node_ids:
                validation_errors.append(f"ID de nodo duplicado: {node_id}")
            node_ids.add(node_id)

            if node_type not in workflow_executor.node_executors:
                validation_errors.append(f"Tipo de nodo no soportado: {node_type}")

            # Validar inputs requeridos
            if node_type == 'ideogram-generator' or node_type == 'dalle-generator':
                inputs = node.get('inputs', {})
                if not inputs.get('prompt'):
                    # Verificar si viene de una conexión
                    has_prompt_connection = any(
                        edge.get('target') == node_id and edge.get('targetHandle') == 'prompt'
                        for edge in edges
                    )
                    if not has_prompt_connection:
                        validation_errors.append(f"Nodo {node_id}: Se requiere un prompt")

        # Validar conexiones
        for edge in edges:
            source = edge.get('source')
            target = edge.get('target')

            if source not in node_ids:
                validation_errors.append(f"Conexión inválida: nodo fuente {source} no existe")
            if target not in node_ids:
                validation_errors.append(f"Conexión inválida: nodo destino {target} no existe")

        # Detectar ciclos
        try:
            dependency_graph = workflow_executor._build_dependency_graph(nodes, edges)
            workflow_executor._topological_sort(dependency_graph)
        except Exception as e:
            validation_errors.append(f"Error en el flujo del workflow: {str(e)}")

        # Verificar que hay al menos un nodo de salida
        output_nodes = [n for n in nodes if n.get('type', '').endswith('-output')]
        if not output_nodes:
            warnings.append("Se recomienda agregar al menos un nodo de salida")

        is_valid = len(validation_errors) == 0

        return {
            "success": True,
            "valid": is_valid,
            "errors": validation_errors,
            "warnings": warnings,
            "node_count": len(nodes),
            "edge_count": len(edges)
        }

    except Exception as e:
        logger.error(f"Error validando workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Error validando workflow: {str(e)}")

@router.get("/nodes")
async def get_available_nodes():
    """Obtiene información sobre los nodos disponibles"""
    # Información básica de nodos disponibles
    node_definitions = {
        "text-input": {
            "name": "Entrada de Texto",
            "category": "Entrada",
            "description": "Ingresa texto o prompts para generar contenido",
            "icon": "📝",
            "inputs": {},
            "outputs": {"text": {"type": "text", "label": "Texto"}}
        },
        "image-input": {
            "name": "Subir Imagen",
            "category": "Entrada",
            "description": "Sube una imagen para procesar",
            "icon": "🖼️",
            "inputs": {},
            "outputs": {"image": {"type": "image", "label": "Imagen"}}
        },
        "ideogram-generator": {
            "name": "Ideogram AI",
            "category": "Generación",
            "description": "Genera imágenes con Ideogram AI",
            "icon": "🎨",
            "inputs": {
                "prompt": {"type": "text", "label": "Prompt", "required": True},
                "model": {"type": "text", "label": "Modelo", "default": "ideogram-3.0"},
                "aspect_ratio": {"type": "text", "label": "Aspecto", "default": "1:1"},
                "style": {"type": "text", "label": "Estilo", "default": "auto"}
            },
            "outputs": {"image": {"type": "image", "label": "Imagen"}, "url": {"type": "url", "label": "URL"}}
        },
        "dalle-generator": {
            "name": "DALL-E 3",
            "category": "Generación",
            "description": "Genera imágenes con OpenAI DALL-E",
            "icon": "🤖",
            "inputs": {
                "prompt": {"type": "text", "label": "Prompt", "required": True},
                "model": {"type": "text", "label": "Modelo", "default": "dall-e-3"},
                "size": {"type": "text", "label": "Tamaño", "default": "1024x1024"},
                "quality": {"type": "text", "label": "Calidad", "default": "standard"}
            },
            "outputs": {"image": {"type": "image", "label": "Imagen"}, "url": {"type": "url", "label": "URL"}}
        },
        "upscale": {
            "name": "Mejorar Imagen",
            "category": "Edición",
            "description": "Mejora la resolución y calidad de la imagen",
            "icon": "⬆️",
            "inputs": {
                "image": {"type": "image", "label": "Imagen", "required": True},
                "scale_factor": {"type": "number", "label": "Factor", "default": 2},
                "creativity": {"type": "number", "label": "Creatividad", "default": 0.3}
            },
            "outputs": {"image": {"type": "image", "label": "Imagen Mejorada"}, "url": {"type": "url", "label": "URL"}}
        },
        "background-remover": {
            "name": "Remover Fondo",
            "category": "Edición",
            "description": "Remueve el fondo de la imagen automáticamente",
            "icon": "🗑️",
            "inputs": {"image": {"type": "image", "label": "Imagen", "required": True}},
            "outputs": {"image": {"type": "image", "label": "Sin Fondo"}, "url": {"type": "url", "label": "URL"}}
        },
        "style-transfer": {
            "name": "Transferir Estilo",
            "category": "Edición",
            "description": "Aplica el estilo de una imagen a otra",
            "icon": "🎭",
            "inputs": {
                "content_image": {"type": "image", "label": "Imagen Base", "required": True},
                "style_image": {"type": "image", "label": "Imagen de Estilo", "required": True},
                "strength": {"type": "number", "label": "Intensidad", "default": 0.7}
            },
            "outputs": {"image": {"type": "image", "label": "Imagen Estilizada"}, "url": {"type": "url", "label": "URL"}}
        },
        "video-generator": {
            "name": "Generar Video",
            "category": "Video",
            "description": "Genera videos con Luma Labs",
            "icon": "🎬",
            "inputs": {
                "prompt": {"type": "text", "label": "Prompt de Video", "required": True},
                "reference_image": {"type": "image", "label": "Imagen de Referencia"},
                "duration": {"type": "text", "label": "Duración", "default": "5"},
                "aspect_ratio": {"type": "text", "label": "Aspecto", "default": "16:9"}
            },
            "outputs": {"video": {"type": "video", "label": "Video"}, "url": {"type": "url", "label": "URL"}}
        },
        "image-output": {
            "name": "Guardar Imagen",
            "category": "Salida",
            "description": "Guarda la imagen final",
            "icon": "💾",
            "inputs": {
                "image": {"type": "image", "label": "Imagen", "required": True},
                "filename": {"type": "text", "label": "Nombre", "default": "emma-workflow"},
                "format": {"type": "text", "label": "Formato", "default": "PNG"}
            },
            "outputs": {"url": {"type": "url", "label": "URL Final"}}
        },
        "video-output": {
            "name": "Guardar Video",
            "category": "Salida",
            "description": "Guarda el video final",
            "icon": "🎥",
            "inputs": {
                "video": {"type": "video", "label": "Video", "required": True},
                "filename": {"type": "text", "label": "Nombre", "default": "emma-video"}
            },
            "outputs": {"url": {"type": "url", "label": "URL Final"}}
        }
    }

    categories = {
        "Entrada": ["text-input", "image-input"],
        "Generación": ["ideogram-generator", "dalle-generator"],
        "Edición": ["upscale", "background-remover", "style-transfer"],
        "Video": ["video-generator"],
        "Salida": ["image-output", "video-output"]
    }

    return {
        "success": True,
        "nodes": node_definitions,
        "categories": categories,
        "total_nodes": len(node_definitions)
    }

@router.get("/health")
async def health_check():
    """Verifica el estado del sistema de workflows Emma"""
    return {
        "success": True,
        "message": "Emma Workflows API funcionando correctamente",
        "available_nodes": len(workflow_executor.node_executors),
        "services": {
            "ideogram": bool(workflow_executor.ideogram_service.api_key),
            "openai": bool(workflow_executor.openai_service.api_key),
            "luma": bool(workflow_executor.luma_service)
        },
        "storage": {
            "workflows_dir": WORKFLOWS_DIR,
            "temp_dir": TEMP_DIR
        }
    }

@router.post("/validate-connection")
async def validate_connection(connection_data: dict):
    """Valida si una conexión entre nodos es válida"""
    try:
        source_handle = connection_data.get('sourceHandle')
        target_handle = connection_data.get('targetHandle')

        # Mapeo de tipos compatibles
        type_compatibility = {
            'text': ['text', 'prompt', 'description', 'negative_prompt'],
            'image': ['image', 'content_image', 'style_image', 'reference_image'],
            'video': ['video'],
            'url': ['url', 'image', 'video'],
            'any': ['text', 'image', 'video', 'url']
        }

        # Verificar compatibilidad básica
        is_valid = True
        message = "Conexión válida"

        # Aquí puedes agregar lógica más específica según los tipos de nodos
        if source_handle and target_handle:
            # Verificar si los tipos son compatibles
            for source_type, compatible_targets in type_compatibility.items():
                if source_handle in compatible_targets or source_type == 'any':
                    if target_handle in compatible_targets or target_handle in type_compatibility.get('any', []):
                        is_valid = True
                        break
            else:
                is_valid = False
                message = f"Tipos incompatibles: {source_handle} -> {target_handle}"

        return {
            'success': True,
            'valid': is_valid,
            'message': message
        }

    except Exception as e:
        logger.error(f"Error validando conexión: {e}")
        return {
            'success': False,
            'valid': False,
            'message': f'Error: {str(e)}'
        }
