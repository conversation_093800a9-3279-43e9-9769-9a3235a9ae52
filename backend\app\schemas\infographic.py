"""
Pydantic schemas for infographic generation and editing.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any


class InfographicGenerationRequest(BaseModel):
    """Request schema for generating infographics."""
    prompt: str = Field(..., description="Description of the infographic to create")
    size: str = Field(default="1024x1024", description="Image size (1024x1024, 1792x1024, 1024x1792)")


class InfographicGenerationResponse(BaseModel):
    """Response schema for infographic generation."""
    success: bool
    image_url: Optional[str] = None
    revised_prompt: Optional[str] = None
    response_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class MultiTurnEditRequest(BaseModel):
    """Request schema for multi-turn editing."""
    previous_response_id: str = Field(..., description="ID of the previous response to build upon")
    edit_prompt: str = Field(..., description="Description of the changes to make")


class MultiTurnEditResponse(BaseModel):
    """Response schema for multi-turn editing."""
    success: bool
    image_url: Optional[str] = None
    response_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class StreamGenerationRequest(BaseModel):
    """Request schema for streaming generation."""
    prompt: str = Field(..., description="Description of the infographic to create")


class StreamGenerationResponse(BaseModel):
    """Response schema for streaming generation."""
    success: bool
    partial_image: Optional[bool] = None
    image_url: Optional[str] = None
    index: Optional[int] = None
    progress: Optional[int] = None
    error: Optional[str] = None


class ReferenceEditRequest(BaseModel):
    """Request schema for editing with reference images."""
    prompt: str = Field(..., description="Description of the infographic to create")
    # reference_images will be handled as UploadFile in the endpoint


class ReferenceEditResponse(BaseModel):
    """Response schema for reference editing."""
    success: bool
    image_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class MaskEditRequest(BaseModel):
    """Request schema for mask-based editing."""
    prompt: str = Field(..., description="Description of what to put in the masked area")
    # image and mask will be handled as UploadFile in the endpoint


class MaskEditResponse(BaseModel):
    """Response schema for mask editing."""
    success: bool
    image_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


# Frontend response schemas for consistency with other tools
class FrontendInfographicResponse(BaseModel):
    """Frontend response schema for infographic operations."""
    success: bool
    images: Optional[List[str]] = None
    image_url: Optional[str] = None
    revised_prompt: Optional[str] = None
    response_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    @classmethod
    def from_service_response(cls, service_response: Dict[str, Any]) -> "FrontendInfographicResponse":
        """Convert service response to frontend response."""
        if service_response.get("success"):
            image_url = service_response.get("image_url")
            return cls(
                success=True,
                images=[image_url] if image_url else None,
                image_url=image_url,
                revised_prompt=service_response.get("revised_prompt"),
                response_id=service_response.get("response_id"),
                metadata=service_response.get("metadata")
            )
        else:
            return cls(
                success=False,
                error=service_response.get("error", "Unknown error")
            )


class FrontendStreamResponse(BaseModel):
    """Frontend response schema for streaming operations."""
    success: bool
    partial_image: Optional[bool] = None
    image_url: Optional[str] = None
    index: Optional[int] = None
    progress: Optional[int] = None
    final_image: Optional[bool] = None
    error: Optional[str] = None
