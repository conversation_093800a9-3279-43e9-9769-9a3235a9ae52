'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { AutocorrectTextarea } from '@/components/ui/autocorrect-textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import {
  Users,
  AlertCircle,
  Clock,
  MessageCircle,
  Brain,
  Lightbulb,
  Sparkles,
  Target,
  FileBarChart,
  ClipboardList,
  Settings,
  List,
  UserCircle,
  MessageSquarePlus,
  MessagesSquare,
  BarChart,
  ChevronRight,
  FileDown,
  Download
} from 'lucide-react';

// Definición de tipos para el simulador de focus group
interface Participant {
  id: number;
  name: string;
  age_range: string;
  gender: string;
  education: string;
  income_level: string;
  digital_usage: string;
  interests: string[];
  personality_traits: string[];
  buying_behavior: {
    price_sensitivity: string;
    brand_loyalty: string;
    research_level: string;
    decision_speed: string;
    influencer_impact: string;
  };
}

interface Comment {
  participant_id: number;
  participant_name: string;
  comment: string;
  sentiment: 'positivo' | 'neutral' | 'negativo';
}

interface Discussion {
  question: string;
  conversation: Comment[];
}

interface DemographicPattern {
  pattern: string;
  affected_demographics: string[];
}

interface FocusGroupResult {
  status: string;
  error_message?: string;
  focus_group_simulation?: {
    discussions: Discussion[];
    summary: {
      key_insights: string[];
      sentiment_analysis: {
        overall: string;
        breakdown: {
          positive_aspects: string[];
          negative_aspects: string[];
          neutral_observations: string[];
        };
      };
      demographic_patterns: DemographicPattern[];
      recommendations: string[];
    };
  };
  timestamp: string;
}

// Componente principal
export default function FocusGroupSimulator() {
  // Estados para el formulario
  const [content, setContent] = useState('');
  const [context, setContext] = useState('');
  const [productCategory, setProductCategory] = useState('ninguna');
  const [customQuestions, setCustomQuestions] = useState('');
  const [numParticipants, setNumParticipants] = useState('5');
  const [autocorrectEnabled, setAutocorrectEnabled] = useState(true); // Estado para controlar autocorrección

  // Estados para el control de la simulación
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStage, setLoadingStage] = useState(0);
  const [results, setResults] = useState<FocusGroupResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('summary');
  const [viewMode, setViewMode] = useState<'setup' | 'live-simulation' | 'results'>('setup');
  
  // Estados para el asistente de texto
  const [isTextAssistLoading, setIsTextAssistLoading] = useState(false);
  const [textSuggestions, setTextSuggestions] = useState<string[]>([]);
  const [showSuggestionDialog, setShowSuggestionDialog] = useState(false);

  // Estados adicionales para el tiempo de carga
  const [loadingTime, setLoadingTime] = useState(0);
  const [loadingTimerId, setLoadingTimerId] = useState<NodeJS.Timeout | null>(null);
  const MAX_LOADING_TIME = 60; // segundos máximos de espera

  const { toast } = useToast();
  
  // Mensajes para la animación de carga
  const loadingMessages = [
    "Preparando el focus group virtual...",
    "Definiendo perfiles de participantes...",
    "Generando preguntas para la discusión...",
    "Analizando el contenido propuesto...",
    "Simulando interacciones entre participantes...",
    "Procesando respuestas y opiniones...",
    "Identificando insights clave...",
    "Analizando patrones demográficos...",
    "Generando recomendaciones...",
    "Finalizando el informe del focus group..."
  ];

  // Efecto para avanzar en las etapas de carga
  useEffect(() => {
    let stageInterval: ReturnType<typeof setInterval>;
    let timeInterval: ReturnType<typeof setInterval>;
    
    if (isLoading) {
      // Reiniciar estados
      setLoadingStage(0);
      setLoadingTime(0);
      
      // Intervalo para avanzar las etapas de carga
      stageInterval = setInterval(() => {
        setLoadingStage(prev => {
          // Si estamos en la última etapa, quedarse ahí hasta que termine
          if (prev >= loadingMessages.length - 1) return prev;
          return prev + 1;
        });
      }, 2000); // Avanza cada 2 segundos
      
      // Intervalo para contar el tiempo de carga
      timeInterval = setInterval(() => {
        setLoadingTime(prev => {
          const newTime = prev + 1;
          // Si superamos la mitad del tiempo máximo, mostrar mensaje
          if (newTime === Math.floor(MAX_LOADING_TIME / 2)) {
            toast({
              title: "La simulación está en progreso",
              description: "La generación de un focus group virtual puede tomar hasta un minuto. Gracias por tu paciencia.",
              variant: "default",
            });
          }
          return newTime;
        });
      }, 1000); // Contar cada segundo
      
      // Guardar referencia al intervalo de tiempo
      setLoadingTimerId(timeInterval);
    }
    
    return () => {
      if (stageInterval) clearInterval(stageInterval);
      if (timeInterval) clearInterval(timeInterval);
      
      // Limpiar también el timeoutId cuando se desmonta
      if (loadingTimerId) {
        clearInterval(loadingTimerId);
        setLoadingTimerId(null);
      }
    };
  }, [isLoading, loadingMessages.length]);

  // Función para iniciar la simulación
  const runFocusGroupSimulation = async () => {
    if (!content.trim()) {
      toast({
        title: "Contenido requerido",
        description: "Por favor, ingresa el contenido que deseas evaluar en el focus group",
        variant: "destructive",
      });
      return;
    }

    // Reiniciar estados
    setIsLoading(true);
    setLoadingStage(0);
    setLoadingTime(0);
    setError(null);
    setResults(null);
    
    // Cambiar inmediatamente a la vista de simulación en vivo
    setViewMode('live-simulation');

    try {
      // Preparar las preguntas personalizadas si se proporcionaron
      const questions = customQuestions.trim() 
        ? customQuestions.split('\n').filter(q => q.trim().length > 0)
        : undefined;

      // Datos para enviar
      const requestData = {
        content: content.trim(),
        context: context.trim() || undefined,
        product_category: (productCategory && productCategory !== "ninguna") ? productCategory : undefined,
        questions,
        num_participants: parseInt(numParticipants, 10),
        discussion_rounds: 3
      };

      // Llamada a la API
      const response = await fetch('/api/simulate-focus-group', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error_message || 'Error al simular el focus group');
      }

      const data = await response.json();
      setResults(data);
      
      // Cambiar a la pestaña de resumen automáticamente
      setActiveTab('summary');
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Ocurrió un error al simular el focus group');
      toast({
        title: "Error en la simulación",
        description: err instanceof Error ? err.message : 'Ocurrió un error al simular el focus group',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      
      // Si tenemos resultados exitosos, cambiar a la vista de resultados
      if (results && results.status === 'success' && results.focus_group_simulation) {
        setViewMode('results');
      } else {
        setViewMode('setup');
        // Mostrar mensaje de error si los resultados no son utilizables
        if (results && results.status === 'error') {
          toast({
            title: "No se pudieron generar resultados",
            description: results.error_message || "Hubo un problema procesando la simulación. Intenta de nuevo con un texto diferente.",
            variant: "destructive",
          });
        }
      }
    }
  };

  // Función para manejar la asistencia de texto con IA
  const handleTextAssistance = async () => {
    if (!content.trim()) {
      toast({
        title: "Contenido requerido",
        description: "Por favor, ingresa algún texto para mejorar",
        variant: "destructive",
      });
      return;
    }

    setIsTextAssistLoading(true);
    setShowSuggestionDialog(true);
    
    try {
      // Datos para enviar al backend
      const requestData = {
        content: content.trim(),
        context: context.trim() || undefined,
        product_category: (productCategory && productCategory !== "ninguna") ? productCategory : undefined,
        action: "improve_text"
      };

      // Llamada a la API - Usaremos la misma ruta para ambas funcionalidades
      const response = await fetch('/api/text-assist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error_message || 'Error al mejorar el texto');
      }

      const data = await response.json();
      
      // Para fines de demostración, generamos sugerencias de texto simuladas
      // En una implementación real, estas vendrían de la API
      const simulatedSuggestions = [
        `${content.trim()} con un enfoque más persuasivo y emocional, destacando los beneficios clave para el usuario.`,
        `Versión mejorada con lenguaje más claro y directo: "${content.trim()}" optimizado para mayor impacto.`,
        `Alternativa profesional y técnica del texto original, enfatizando la calidad y especificaciones.`
      ];
      
      setTextSuggestions(data.suggestions || simulatedSuggestions);
    } catch (err) {
      console.error('Error:', err);
      toast({
        title: "Error al mejorar el texto",
        description: err instanceof Error ? err.message : 'Ocurrió un error inesperado',
        variant: "destructive",
      });
      setShowSuggestionDialog(false);
    } finally {
      setIsTextAssistLoading(false);
    }
  };

  // Función para aplicar una sugerencia de texto
  const applySuggestion = (suggestion: string) => {
    setContent(suggestion);
    setShowSuggestionDialog(false);
    toast({
      title: "Texto mejorado aplicado",
      description: "Se ha actualizado el contenido con la versión mejorada",
      variant: "default",
    });
  };

  // Función para obtener color según sentimiento
  const getSentimentColor = (sentiment: string): string => {
    if (sentiment === 'positivo') return "text-green-600 bg-green-50 border-green-200";
    if (sentiment === 'negativo') return "text-red-600 bg-red-50 border-red-200";
    return "text-blue-600 bg-blue-50 border-blue-200";
  };
  
  // Referencia para el componente que queremos exportar a PDF
  const resultsRef = useRef<HTMLDivElement>(null);
  
  // Función para generar y descargar PDF de los resultados
  const generatePDF = async () => {
    if (!resultsRef.current || !results?.focus_group_simulation) return;
    
    toast({
      title: "Generando PDF",
      description: "Preparando el documento con los resultados del Focus Group...",
      variant: "default",
    });
    
    try {
      // Opciones de escala para mejorar la calidad
      const scale = 2;
      const pdfOptions = {
        scale: scale,
        useCORS: true,
        logging: false,
        scrollX: 0,
        scrollY: 0
      };
      
      // Capturar el contenido como imagen
      const canvas = await html2canvas(resultsRef.current, pdfOptions);
      
      // Definir el tamaño del PDF (A4)
      const imgWidth = 210; // Ancho de página A4 en mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      // Crear un nuevo PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      // Añadir título y fecha
      pdf.setFontSize(18);
      pdf.setTextColor(0, 0, 255);
      pdf.text('Informe de Focus Group', 105, 15, { align: 'center' });
      
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      const dateStr = new Date().toLocaleDateString('es-ES', { 
        day: '2-digit', 
        month: 'long', 
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      pdf.text(`Generado el: ${dateStr}`, 105, 22, { align: 'center' });
      
      // Añadir información del contenido evaluado
      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Contenido evaluado:', 14, 30);
      
      // Ajustar el contenido a múltiples líneas si es necesario
      const contentLines = pdf.splitTextToSize(content, 180);
      pdf.setFontSize(10);
      pdf.setTextColor(80, 80, 80);
      pdf.text(contentLines, 14, 36);
      
      // Agregar línea separadora
      pdf.setDrawColor(200, 200, 200);
      pdf.line(14, 45, 196, 45);
      
      // Añadir la imagen del informe
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 50, imgWidth, imgHeight);
      
      // Si el contenido es muy largo, dividirlo en varias páginas
      if (imgHeight > 240) {
        let heightLeft = imgHeight;
        let position = 50; // Posición inicial
        
        heightLeft -= 240; // Altura que cabe en la primera página
        position -= 240;
        
        // Añadir nuevas páginas según sea necesario
        while (heightLeft > 0) {
          pdf.addPage();
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
          heightLeft -= 297; // Altura de página A4
          position -= 297;
        }
      }
      
      // Descargar el PDF
      pdf.save(`Focus_Group_Report_${new Date().toISOString().slice(0, 10)}.pdf`);
      
      toast({
        title: "PDF generado con éxito",
        description: "El informe se ha descargado correctamente",
        variant: "default",
      });
    } catch (error) {
      console.error('Error al generar PDF:', error);
      toast({
        title: "Error al generar PDF",
        description: "No se pudo crear el documento. Por favor, intenta nuevamente.",
        variant: "destructive",
      });
    }
  };

  // Componente para la simulación en vivo
  const LiveSimulationView = () => {
    // Estado para controlar las burbujas 
    const [bubbles, setBubbles] = useState<Array<{
      id: number;
      x: number;
      y: number;
      size: number;
      color: string;
      speedX: number;
      speedY: number;
      content: string;
    }>>([]);

    // Inicializar las burbujas
    useEffect(() => {
      if (!isLoading) return;
      
      // Generar burbujas aleatorias
      const newBubbles = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,  // Posición X
        y: Math.random() * 100,  // Posición Y
        size: 20 + Math.random() * 60,  // Tamaño
        color: [
          'bg-blue-100 border-blue-300', 
          'bg-green-100 border-green-300',
          'bg-purple-100 border-purple-300',
          'bg-amber-100 border-amber-300',
          'bg-pink-100 border-pink-300'
        ][Math.floor(Math.random() * 5)],
        speedX: (Math.random() - 0.5) * 2,
        speedY: (Math.random() - 0.5) * 2,
        content: Math.random() > 0.7 ? '💬' : ''
      }));
      
      setBubbles(newBubbles);
    }, [isLoading]);
    // Datos para generar participantes aleatorios
    const randomNames = {
      female: [
        "María", "Ana", "Laura", "Sofía", "Carmen", "Elena", "Lucía", "Isabel",
        "Patricia", "Marta", "Claudia", "Valentina", "Rosa", "Julia", "Sara"
      ],
      male: [
        "Carlos", "Javier", "Miguel", "David", "Daniel", "Alejandro", "Antonio",
        "Juan", "Roberto", "José", "Pablo", "Pedro", "Francisco", "Raúl", "Jorge"
      ]
    };
    
    const randomAvatars = {
      female: ["👩‍💼", "👩‍🔬", "👩‍🏫", "👩‍⚕️", "👩‍🎓", "👩‍💻", "👩‍🦰", "👩‍🦱", "👱‍♀️", "👩"],
      male: ["👨‍💼", "👨‍🔬", "👨‍🏫", "👨‍⚕️", "👨‍🎓", "👨‍💻", "👨‍🦰", "👨‍🦱", "👱‍♂️", "👨"]
    };
    
    const randomRoles = [
      "Profesional de Marketing", "Diseñador UX", "Desarrollador", "Gerente de Producto",
      "Especialista en SEO", "Analista de Datos", "Consultor", "Emprendedor",
      "Estudiante", "Profesional Freelance", "Cliente Potencial", "Usuario Frecuente",
      "Usuario Ocasional", "Experto en Redes Sociales", "Especialista en Conversión",
      "Consumidor Habitual", "Comprador Indeciso", "Early Adopter", "Cliente Fiel",
      "Influencer de Nicho"
    ];

    // Función para generar un participante aleatorio con un ID específico
    const generateRandomParticipant = (id: number) => {
      // Determinar género de forma alternada (para tener diversidad)
      const gender = id % 2 === 0 ? 'male' : 'female';
      const genderNames = randomNames[gender];
      const genderAvatars = randomAvatars[gender];
      
      return {
        id,
        gender,
        name: genderNames[Math.floor(Math.random() * genderNames.length)],
        avatar: genderAvatars[Math.floor(Math.random() * genderAvatars.length)],
        role: id === 1 
          ? "Moderador" + (gender === 'female' ? 'a' : '') 
          : randomRoles[Math.floor(Math.random() * randomRoles.length)]
      };
    };
    
    // Generar participantes aleatorios al inicio
    const [simulatedParticipants, setSimulatedParticipants] = useState(() => {
      return Array.from({length: 5}, (_, i) => generateRandomParticipant(i + 1));
    });
    
    // Estado para controlar qué participante está hablando
    const [activeParticipant, setActiveParticipant] = useState<number>(1);
    const [conversationIndex, setConversationIndex] = useState<number>(0);
    const [typingVisible, setTypingVisible] = useState(false);
    
    // Mensajes simulados de la conversación
    const simulatedMessages = [
      { id: 1, text: "Bienvenidos a este focus group. Hoy evaluaremos el contenido sobre el que nos han pedido opinión." },
      { id: 2, text: "Me parece que este producto tiene características interesantes, especialmente en términos de ahorro de tiempo." },
      { id: 3, text: "Estoy de acuerdo, aunque me gustaría ver más detalles sobre cómo exactamente ayuda a ahorrar tiempo." },
      { id: 4, text: "Desde mi experiencia casual, lo que más valoro es la simplicidad. ¿Este producto es fácil de usar?" },
      { id: 5, text: "Si analizamos el mercado actual, productos como este necesitan destacar más sus beneficios concretos." },
      { id: 1, text: "Excelentes puntos. ¿Qué opinan sobre el lenguaje utilizado en la descripción?" },
      { id: 3, text: "El lenguaje es claro pero podría ser más persuasivo enfatizando los beneficios." },
      { id: 2, text: "Coincido con Ana. Las especificaciones técnicas podrían resaltarse mejor." },
      { id: 5, text: "Estadísticamente, las descripciones que mencionan resultados específicos generan más conversiones." },
      { id: 4, text: "Como usuario ocasional, lo que busco son beneficios claros y directos, sin tecnicismos complicados." }
    ];
    
    // Categorías de mensajes para generar comentarios con más contexto según el rol
    const messageCategoriesByRole = {
      // Moderador/a
      moderator: [
        "Bienvenidos a todos al focus group. ¿Qué impresión inicial les da este contenido?",
        "Interesante punto, ¿alguien más comparte esta opinión?",
        "¿Podrían profundizar un poco más en ese aspecto que mencionaron?",
        "Pasemos a otro tema. ¿Qué opinan sobre la claridad del mensaje?",
        "¿Creen que este contenido conectaría con la audiencia objetivo?",
        "¿Cómo mejorarían ustedes este aspecto específico?",
        "Resumiendo lo que han comentado hasta ahora...",
        "Excelentes aportaciones. Ahora me gustaría saber si...",
        "Teniendo en cuenta sus experiencias previas, ¿consideran que...?",
        "Para finalizar esta sección, ¿algún comentario adicional?"
      ],
      
      // Para roles técnicos (desarrolladores, diseñadores UX, etc)
      technical: [
        "Desde un punto de vista técnico, esto podría optimizarse para mejorar la experiencia.",
        "La arquitectura de información no está bien estructurada, dificulta la comprensión.",
        "Falta consistencia visual con la línea gráfica existente.",
        "Los patrones de interacción no siguen los estándares actuales de UX.",
        "El flujo de navegación no es intuitivo para el usuario promedio.",
        "Habría que considerar el rendimiento en diferentes dispositivos.",
        "La accesibilidad debería ser una prioridad aquí, veo aspectos mejorables.",
        "La densidad de información es excesiva para una primera interacción.",
        "La jerarquía visual no refleja la importancia de cada elemento.",
        "Sugeriría implementar un sistema de feedback más interactivo."
      ],
      
      // Para roles de marketing y ventas
      marketing: [
        "Este mensaje no comunica claramente el valor diferencial del producto.",
        "El copy podría ser más persuasivo enfatizando los beneficios concretos.",
        "Falta un call-to-action más claro y convincente.",
        "La propuesta de valor no destaca lo suficiente frente a la competencia.",
        "El tono no parece alineado con el posicionamiento de la marca.",
        "Según las tendencias actuales, este enfoque podría generar más engagement.",
        "El contenido no aprovecha los triggers emocionales adecuados para la audiencia.",
        "La narrativa debería construirse alrededor del problema que resuelve.",
        "Este mensaje requiere más elementos de prueba social para generar confianza.",
        "Considero que la estrategia de comunicación podría ser más efectiva si..."
      ],
      
      // Para usuarios y consumidores
      consumer: [
        "Como usuario, lo primero que buscaría es entender rápidamente qué problema me soluciona.",
        "No me queda claro cómo funcionaría esto en mi día a día.",
        "La primera impresión es positiva, pero necesitaría más detalles antes de decidirme.",
        "El precio sería un factor decisivo para mí en este caso.",
        "Me gustaría ver ejemplos reales de personas usando este producto/servicio.",
        "Personalmente, lo que más valoro es la simplicidad y esto parece complicado.",
        "No encuentro suficientes razones para cambiar de lo que uso actualmente a esto.",
        "La estética me atrae, pero necesito entender mejor los beneficios concretos.",
        "Como consumidor habitual de productos similares, echo en falta...",
        "Preferiría ver comparativas directas con otras alternativas del mercado."
      ],
      
      // Mensajes generales que pueden aplicar a cualquier rol
      general: [
        "Interesante propuesta, aunque creo que podría mejorarse en algunos aspectos.",
        "¿Han considerado cómo esto se integraría con los hábitos actuales de los usuarios?",
        "El concepto es prometedor pero la ejecución necesita refinamiento.",
        "Me gusta la dirección general, pero falta pulir algunos detalles importantes.",
        "Este enfoque es innovador comparado con lo que se ve habitualmente en el mercado.",
        "Hay un buen equilibrio entre funcionalidad y estética.",
        "La primera impresión es crucial, y esto genera curiosidad inmediata.",
        "Creo que conectaría mejor con una audiencia más joven/mayor.",
        "El contexto de uso no está del todo claro en la presentación.",
        "Sería interesante testarlo en diferentes segmentos demográficos."
      ]
    };
    
    // Función para obtener un mensaje aleatorio basado en el rol del participante
    const getRandomMessageByParticipantRole = (role: string): string => {
      let messageCategory = 'general';
      
      // Determinar la categoría según el rol
      const roleLower = role.toLowerCase();
      if (roleLower.includes('moderador')) {
        messageCategory = 'moderator';
      } else if (
        roleLower.includes('desarrollador') || 
        roleLower.includes('diseñador') || 
        roleLower.includes('técnico') ||
        roleLower.includes('tecnico') ||
        roleLower.includes('ingeniero') ||
        roleLower.includes('ux')
      ) {
        messageCategory = 'technical';
      } else if (
        roleLower.includes('marketing') || 
        roleLower.includes('ventas') || 
        roleLower.includes('especialista') ||
        roleLower.includes('consultor') ||
        roleLower.includes('estrategia') ||
        roleLower.includes('producto')
      ) {
        messageCategory = 'marketing';
      } else if (
        roleLower.includes('usuario') || 
        roleLower.includes('consumidor') || 
        roleLower.includes('cliente') ||
        roleLower.includes('comprador') ||
        roleLower.includes('potencial')
      ) {
        messageCategory = 'consumer';
      }
      
      // Obtener los mensajes para esa categoría
      const messages = messageCategoriesByRole[messageCategory as keyof typeof messageCategoriesByRole];
      
      // Devolver un mensaje aleatorio de esa categoría
      return messages[Math.floor(Math.random() * messages.length)];
    };
    
    // Efecto para mostrar mensajes aleatorios simulados
    useEffect(() => {
      if (!isLoading) return;
      
      // Mostrar mensajes con retraso aleatorio
      const interval = setInterval(() => {
        // Elegir un participante aleatorio
        const randomParticipant = simulatedParticipants[Math.floor(Math.random() * simulatedParticipants.length)];
        setActiveParticipant(randomParticipant.id);
        
        // Simular escritura
        setTypingVisible(true);
        
        // Después de un momento, mostrar un mensaje aleatorio
        setTimeout(() => {
          setTypingVisible(false);
          // Aumentar índice para mostrar más mensajes
          if (conversationIndex < 15) {
            setConversationIndex(prev => prev + 1);
          }
        }, 1500);
      }, 3000);
      
      return () => clearInterval(interval);
    }, [isLoading]);
    
    // Efecto para animar las burbujas
    useEffect(() => {
      if (!isLoading || bubbles.length === 0) return;
      
      // Animación de movimiento de burbujas
      const animationInterval = setInterval(() => {
        setBubbles(prevBubbles => 
          prevBubbles.map(bubble => {
            // Calcular la nueva posición
            let newX = bubble.x + bubble.speedX;
            let newY = bubble.y + bubble.speedY;
            
            // Rebote en los bordes
            if (newX <= 0 || newX >= 100) {
              bubble.speedX = -bubble.speedX;
              newX = bubble.x + bubble.speedX;
            }
            
            if (newY <= 0 || newY >= 100) {
              bubble.speedY = -bubble.speedY;
              newY = bubble.y + bubble.speedY;
            }
            
            return { ...bubble, x: newX, y: newY };
          })
        );
      }, 50); // Actualizar posiciones cada 50ms para una animación fluida
      
      return () => clearInterval(animationInterval);
    }, [isLoading, bubbles]);
    
    // Calcular progreso y mensaje actual para mostrar
    const progressPercentage = Math.min(Math.round((loadingTime / MAX_LOADING_TIME) * 100), 100);
    const currentProgressMessage = loadingMessages[Math.min(loadingStage, loadingMessages.length - 1)];
    
    return (
      <div className="relative w-full h-[600px] overflow-hidden bg-gray-50 border-2 border-blue-200 rounded-lg shadow-lg">
        {/* Capa de burbujas flotantes */}
        <div className="absolute inset-0 z-0">
          {bubbles.map(bubble => (
            <motion.div
              key={bubble.id}
              className={`absolute rounded-full border-2 ${bubble.color} flex items-center justify-center`}
              style={{
                left: `${bubble.x}%`,
                top: `${bubble.y}%`,
                width: `${bubble.size}px`,
                height: `${bubble.size}px`,
              }}
              animate={{
                scale: [1, 1.05, 0.95, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{
                duration: 2 + Math.random() * 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <span className="text-xl">{bubble.content}</span>
            </motion.div>
          ))}
        </div>
        
        {/* Contenido central */}
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center p-6">
          <motion.div 
            className="bg-white/90 backdrop-blur-sm p-8 rounded-xl shadow-xl border-2 border-blue-300 text-center max-w-lg"
            animate={{
              scale: [1, 1.02, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <motion.div
              className="mx-auto mb-6 text-5xl"
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 10, -10, 0],
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              💬
            </motion.div>
            
            <h3 className="text-xl font-bold text-blue-700 mb-3">
              Simulando Focus Group
            </h3>
            
            <p className="text-gray-700 mb-4">
              {currentProgressMessage}
            </p>
            
            <div className="space-y-2 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Progreso</span>
                <span className="text-sm text-blue-600 font-bold">{progressPercentage}%</span>
              </div>
              <Progress value={progressPercentage} className="h-2.5" />
            </div>
            
            <p className="text-sm text-gray-500">
              Estamos procesando tu solicitud utilizando IA para generar insights valiosos.
              <br />
              Este proceso puede tardar hasta un minuto.
            </p>
          </motion.div>
        </div>
      </div>
    );de tardar hasta un minuto.
            </p>
          </motion.div>
        </div>
        
        {/* Participantes (opcional, pueden eliminarse si prefieres solo las burbujas) */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-20 flex justify-center gap-2">
            {simulatedParticipants.map(participant => (
              <motion.div
                key={participant.id}
                className={`flex flex-col items-center p-2 rounded-lg ${
                  activeParticipant === participant.id 
                    ? 'ring-2 ring-blue-400 bg-blue-50' 
                    : 'bg-gray-50'
                }`}
                animate={activeParticipant === participant.id ? {
                  scale: [1, 1.05, 1],
                  transition: { duration: 1, repeat: Infinity }
                } : {}}
              >
                <div className="text-4xl mb-2">{participant.avatar}</div>
                <div className="text-sm font-medium">{participant.name}</div>
                <div className="text-xs text-gray-500">{participant.role}</div>
              </motion.div>
            ))}
          </div>
          
          {/* Conversación */}
          <ScrollArea className="h-80 border rounded-lg p-4 bg-white">
            <div className="space-y-4">
              {/* En lugar de mostrar los mensajes de simulatedMessages, 
              * mostraremos mensajes aleatorios del array randomMessages
              * según vaya aumentando el índice de conversación */}
              {[...Array(conversationIndex + 1)].map((_, i) => {
                // Asignar un participante aleatorio pero consistente para cada índice
                const participantId = ((i % 5) + 1);
                const participant = simulatedParticipants.find(p => p.id === participantId);
                
                // Obtener un mensaje aleatorio basado en el rol del participante
                const randomMessageText = participant ? getRandomMessageByParticipantRole(participant.role) : "Interesante perspectiva...";
                
                return (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7, type: "spring", stiffness: 100 }}
                    className="flex gap-3"
                  >
                    <motion.div 
                      className="flex-shrink-0 text-2xl"
                      animate={activeParticipant === participantId && i === conversationIndex ? {
                        scale: [1, 1.2, 1],
                        transition: { duration: 0.5 }
                      } : {}}
                    >
                      {participant?.avatar}
                    </motion.div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{participant?.name}</div>
                      <motion.div 
                        className={`p-3 rounded-lg text-sm mt-1 shadow-sm ${
                          participantId === 1 
                            ? "bg-blue-50 border border-blue-200" 
                            : participantId === 2 
                              ? "bg-green-50 border border-green-200" 
                              : participantId === 3
                                ? "bg-purple-50 border border-purple-200"
                                : participantId === 4
                                  ? "bg-amber-50 border border-amber-200"
                                  : "bg-pink-50 border border-pink-200"
                        }`}
                        initial={{ scale: 0.8, opacity: 0, x: -15 }}
                        animate={{ 
                          scale: 1, 
                          opacity: 1, 
                          x: 0,
                          transition: { 
                            delay: 0.2,
                            duration: 0.5,
                            type: "spring"
                          }
                        }}
                      >
                        <motion.span
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ 
                            delay: 0.3, 
                            duration: 0.5
                          }}
                          className="block"
                        >
                          {randomMessageText}
                        </motion.span>
                      </motion.div>
                    </div>
                  </motion.div>
                );
              })}
              
              {/* Mostrar indicador de "escribiendo..." cuando typingVisible es true */}
              {isLoading && typingVisible && (
                <motion.div
                  key="typing-indicator"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex gap-3 mt-3"
                >
                  <div className="flex-shrink-0 text-2xl">
                    {simulatedParticipants.find(p => p.id === activeParticipant)?.avatar}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">
                      {simulatedParticipants.find(p => p.id === activeParticipant)?.name}
                    </div>
                    <div className={`p-3 rounded-lg text-sm mt-1 border ${
                      activeParticipant === 1 
                        ? "bg-blue-50 border-blue-200" 
                        : activeParticipant % 2 === 0 
                          ? "bg-green-50 border-green-200" 
                          : "bg-purple-50 border-purple-200"
                    }`}>
                      <motion.div
                        className="flex space-x-2 items-center h-4"
                        animate={{ opacity: [0.4, 1, 0.4] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <motion.div
                          animate={{ y: [0, -5, 0] }}
                          transition={{ duration: 0.5, repeat: Infinity, delay: 0 }}
                          className="w-2 h-2 bg-gray-500 rounded-full"
                        />
                        <motion.div
                          animate={{ y: [0, -5, 0] }}
                          transition={{ duration: 0.5, repeat: Infinity, delay: 0.2 }}
                          className="w-2 h-2 bg-gray-500 rounded-full"
                        />
                        <motion.div
                          animate={{ y: [0, -5, 0] }}
                          transition={{ duration: 0.5, repeat: Infinity, delay: 0.4 }}
                          className="w-2 h-2 bg-gray-500 rounded-full"
                        />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </ScrollArea>
          
          {/* Barra de progreso */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Progreso del análisis</span>
              <span className="text-sm text-gray-500">{Math.min(Math.round((loadingTime / MAX_LOADING_TIME) * 100), 100)}%</span>
            </div>
            <Progress value={Math.min((loadingTime / MAX_LOADING_TIME) * 100, 100)} className="h-2" />
            <p className="text-center text-sm text-muted-foreground mt-2">
              {loadingMessages[Math.min(loadingStage, loadingMessages.length - 1)]}
            </p>
          </div>
          
          <div className="mt-4 text-center text-sm text-gray-500">
            <p>Esta simulación visual se está ejecutando mientras generamos resultados reales con IA.</p>
            <p>Los resultados completos estarán disponibles en breve. Por favor, espera.</p>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Componente para mostrar un comentario individual
  const CommentCard = ({ comment }: { comment: Comment }) => (
    <div className={`p-3 rounded-lg border mb-3 ${getSentimentColor(comment.sentiment)}`}>
      <div className="flex items-center gap-2 mb-2">
        <UserCircle className="h-5 w-5" />
        <span className="font-medium">{comment.participant_name}</span>
        <Badge variant="outline" className={`ml-auto ${
          comment.sentiment === 'positivo' ? 'border-green-300 text-green-700' : 
          comment.sentiment === 'negativo' ? 'border-red-300 text-red-700' : 
          'border-blue-300 text-blue-700'
        }`}>
          {comment.sentiment}
        </Badge>
      </div>
      <p className="text-sm">{comment.comment}</p>
    </div>
  );

  // Componente para mostrar una discusión completa
  const DiscussionBlock = ({ discussion, index }: { discussion: Discussion, index: number }) => (
    <Card className="mb-6 border-2 border-gray-200 shadow-sm">
      <CardHeader className="bg-gray-50 border-b pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <MessageSquarePlus className="h-5 w-5 text-blue-600" />
          <span>Pregunta {index + 1}: {discussion.question}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <ScrollArea className="h-[300px] pr-4">
          {discussion.conversation.map((comment, i) => (
            <CommentCard key={i} comment={comment} />
          ))}
        </ScrollArea>
      </CardContent>
    </Card>
  );

  // Renderizar la interfaz principal
  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Dialog para mostrar sugerencias de texto */}
      <Dialog open={showSuggestionDialog} onOpenChange={setShowSuggestionDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              Mejoras de texto sugeridas por IA
            </DialogTitle>
            <DialogDescription>
              Selecciona una de las siguientes versiones mejoradas para tu texto.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            {isTextAssistLoading ? (
              <div className="flex flex-col items-center justify-center py-8 space-y-4">
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 180, 360],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Sparkles className="h-12 w-12 text-blue-500" />
                </motion.div>
                <p className="text-center text-sm text-muted-foreground">
                  Generando mejoras para tu texto usando IA...
                </p>
              </div>
            ) : (
              <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                {textSuggestions.map((suggestion, index) => (
                  <Card 
                    key={index} 
                    className="cursor-pointer hover:shadow-md transition-all border-2 hover:border-blue-200"
                    onClick={() => applySuggestion(suggestion)}
                  >
                    <CardContent className="p-4">
                      <p className="text-sm">{suggestion}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowSuggestionDialog(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Panel de configuración */}
        {viewMode !== 'live-simulation' && (
          <Card className="w-full lg:w-2/5 border-2 border-gray-200 shadow-md hover:shadow-lg transition-all">
            <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
              <CardTitle className="text-blue-600 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" /> 
                Simulador de Focus Group
              </CardTitle>
              <CardDescription>
                Simula un focus group virtual para evaluar contenido y obtener insights cualitativos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="content">Contenido a evaluar</Label>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center space-x-2 mr-2">
                      <Switch
                        id="autocorrect-switch"
                        checked={autocorrectEnabled}
                        onCheckedChange={setAutocorrectEnabled}
                      />
                      <Label htmlFor="autocorrect-switch" className="text-xs">Auto-corrección</Label>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-7 text-xs px-2 text-blue-600 border-blue-200 hover:bg-blue-50 flex items-center gap-1"
                      onClick={() => handleTextAssistance()}
                      disabled={!content.trim() || isLoading}
                    >
                      <Sparkles className="h-3.5 w-3.5" />
                      Mejorar con IA
                    </Button>
                  </div>
                </div>
                <AutocorrectTextarea
                  id="content"
                  placeholder="Ingresa el texto del anuncio, descripción de producto, concepto, etc."
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="min-h-[120px]"
                  autoCorrectEnabled={autocorrectEnabled}
                  showCorrectionIndicator={true}
                  debounceMs={1200}
                />
                <p className="text-xs text-muted-foreground">
                  Puedes ingresar un anuncio, descripción de producto, pitch o cualquier contenido que quieras evaluar. 
                  Usa "Mejorar con IA" para completar o mejorar tu texto con la ayuda de Gemini.
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="context">Contexto (opcional)</Label>
                <AutocorrectTextarea
                  id="context"
                  placeholder="Información adicional sobre tu marca, empresa o contexto del contenido"
                  value={context}
                  onChange={(e) => setContext(e.target.value)}
                  className="min-h-[80px]"
                  autoCorrectEnabled={autocorrectEnabled}
                  showCorrectionIndicator={true}
                />
                <p className="text-xs text-muted-foreground">
                  Proporcionar contexto mejora la relevancia de los comentarios del focus group.
                </p>
              </div>

              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="advanced">
                  <AccordionTrigger className="text-sm font-medium text-blue-600">
                    <Settings className="h-4 w-4 mr-2" />
                    Opciones avanzadas
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4 pt-2">
                      <div className="space-y-2">
                        <Label htmlFor="category">Categoría del producto/servicio</Label>
                        <Select value={productCategory} onValueChange={setProductCategory}>
                          <SelectTrigger id="category">
                            <SelectValue placeholder="Selecciona una categoría" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ninguna">Sin categoría específica</SelectItem>
                            <SelectItem value="tecnología">Tecnología</SelectItem>
                            <SelectItem value="moda">Moda</SelectItem>
                            <SelectItem value="alimentos">Alimentos y bebidas</SelectItem>
                            <SelectItem value="finanzas">Finanzas</SelectItem>
                            <SelectItem value="salud">Salud y bienestar</SelectItem>
                            <SelectItem value="entretenimiento">Entretenimiento</SelectItem>
                            <SelectItem value="viajes">Viajes y turismo</SelectItem>
                            <SelectItem value="hogar">Hogar y decoración</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="participants">Número de participantes</Label>
                        <Select value={numParticipants} onValueChange={setNumParticipants}>
                          <SelectTrigger id="participants">
                            <SelectValue placeholder="Número de participantes" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="3">3 participantes</SelectItem>
                            <SelectItem value="5">5 participantes</SelectItem>
                            <SelectItem value="7">7 participantes</SelectItem>
                            <SelectItem value="9">9 participantes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="custom-questions">Preguntas personalizadas (opcional)</Label>
                        <AutocorrectTextarea
                          id="custom-questions"
                          placeholder="Ingresa una pregunta por línea"
                          value={customQuestions}
                          onChange={(e) => setCustomQuestions(e.target.value)}
                          className="min-h-[100px]"
                          autoCorrectEnabled={autocorrectEnabled}
                          showCorrectionIndicator={true}
                        />
                        <p className="text-xs text-muted-foreground">
                          Si no se especifican, se utilizarán preguntas predeterminadas según la categoría.
                        </p>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={runFocusGroupSimulation}
                disabled={isLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold"
              >
                {isLoading ? "Simulando..." : "Simular Focus Group"}
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Panel de resultados o simulación en vivo */}
        {viewMode === 'live-simulation' ? (
          <LiveSimulationView />
        ) : (
          // Solo mostrar resultados si no estamos en live-simulation
          <Card className="w-full lg:w-3/5 border-2 border-gray-200 shadow-md hover:shadow-lg transition-all">
            {isLoading ? (
              <CardContent className="pt-6">
                <div className="flex flex-col items-center justify-center space-y-6 py-12">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={loadingStage}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.5 }}
                      className="flex items-center gap-3"
                    >
                      {loadingStage === 0 && <Users className="h-6 w-6 text-blue-500 animate-pulse" />}
                      {loadingStage === 1 && <UserCircle className="h-6 w-6 text-indigo-500 animate-pulse" />}
                      {loadingStage === 2 && <ClipboardList className="h-6 w-6 text-purple-500 animate-pulse" />}
                      {loadingStage === 3 && <Brain className="h-6 w-6 text-red-500 animate-pulse" />}
                      {loadingStage === 4 && <MessagesSquare className="h-6 w-6 text-orange-500 animate-pulse" />}
                      {loadingStage === 5 && <MessageCircle className="h-6 w-6 text-yellow-500 animate-pulse" />}
                      {loadingStage === 6 && <Lightbulb className="h-6 w-6 text-green-500 animate-pulse" />}
                      {loadingStage === 7 && <Target className="h-6 w-6 text-teal-500 animate-pulse" />}
                      {loadingStage === 8 && <Sparkles className="h-6 w-6 text-cyan-500 animate-pulse" />}
                      {loadingStage === 9 && <FileBarChart className="h-6 w-6 text-blue-500 animate-pulse" />}
                      <span className="text-lg font-medium">{loadingMessages[loadingStage]}</span>
                    </motion.div>
                  </AnimatePresence>
                  
                  <div className="w-full max-w-md space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{Math.min(Math.round((loadingTime / MAX_LOADING_TIME) * 100), 100)}% completado</span>
                      <span>{loadingTime}s</span>
                    </div>
                    <Progress value={Math.min((loadingTime / MAX_LOADING_TIME) * 100, 100)} className="h-2" />
                  </div>
                  
                  <Alert variant="default" className="bg-blue-50 mt-6 max-w-md">
                    <AlertCircle className="h-4 w-4 text-blue-500" />
                    <AlertTitle>Procesando con Gemini AI</AlertTitle>
                    <AlertDescription className="text-sm text-muted-foreground">
                      Estamos analizando el contenido y simulando un focus group con diferentes perfiles demográficos.
                      Este proceso puede tomar hasta un minuto.
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            ) : error ? (
              <CardContent className="pt-6">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                
                <div className="mt-4 flex justify-center">
                  <Button 
                    onClick={runFocusGroupSimulation}
                    variant="outline" 
                    className="mr-2"
                  >
                    Intentar nuevamente
                  </Button>
                  <Button 
                    onClick={() => {
                      setContent(content.split(' ').slice(0, 20).join(' '));
                      setError(null);
                    }}
                    variant="default"
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Simplificar contenido
                  </Button>
                </div>
              </CardContent>
            ) : results && results.status === 'success' && results.focus_group_simulation ? (
              <>
                <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="text-blue-600 flex items-center gap-2">
                        <Users className="h-5 w-5 text-blue-600" />
                        Resultados del Focus Group
                      </CardTitle>
                      <CardDescription>
                        Feedback cualitativo de {results.focus_group_simulation.discussions[0]?.conversation.length || 5} participantes virtuales
                      </CardDescription>
                    </div>
                    <Button 
                      onClick={generatePDF}
                      variant="outline" 
                      className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100 hover:text-blue-700"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Exportar a PDF
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  <div ref={resultsRef}>
                  <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid grid-cols-3 mb-6 bg-gray-100">
                      <TabsTrigger value="summary" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                        Resumen
                      </TabsTrigger>
                      <TabsTrigger value="discussion" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                        Discusión
                      </TabsTrigger>
                      <TabsTrigger value="recommendations" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                        Recomendaciones
                      </TabsTrigger>
                    </TabsList>

                    {/* Pestaña Resumen */}
                    <TabsContent value="summary" className="space-y-4">
                      <div className="p-4 border rounded-md mb-4">
                        <p className="font-medium mb-1">Contenido evaluado:</p>
                        <p className="text-sm">{content}</p>
                      </div>

                      <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                        <CardHeader className="pb-2 bg-gray-50">
                          <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                            <Lightbulb className="h-4 w-4 text-blue-600" />
                            Insights Clave
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <ul className="space-y-2">
                            {results.focus_group_simulation.summary.key_insights.map((insight, i) => (
                              <li key={i} className="flex items-start gap-2">
                                <ChevronRight className="h-5 w-5 text-blue-500 shrink-0 mt-0.5" />
                                <span>{insight}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>

                      <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                        <CardHeader className="pb-2 bg-gray-50">
                          <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                            <BarChart className="h-4 w-4 text-blue-600" />
                            Análisis de Sentimiento
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <div className="mb-4 flex items-center justify-center">
                            <Badge className={`text-lg py-2 px-4 ${
                              results.focus_group_simulation.summary.sentiment_analysis.overall === "positivo" 
                                ? "bg-green-100 text-green-800" 
                                : results.focus_group_simulation.summary.sentiment_analysis.overall === "neutral"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-red-100 text-red-800"
                            }`}>
                              Sentimiento general: {results.focus_group_simulation.summary.sentiment_analysis.overall}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-4">
                            <Card className="border-green-200 bg-green-50">
                              <CardHeader className="p-3 pb-1">
                                <CardTitle className="text-sm text-green-700">Aspectos Positivos</CardTitle>
                              </CardHeader>
                              <CardContent className="p-3 pt-2">
                                <ul className="text-xs space-y-1 text-green-800">
                                  {results.focus_group_simulation.summary.sentiment_analysis.breakdown.positive_aspects.map((aspect, i) => (
                                    <li key={i} className="list-disc list-inside">{aspect}</li>
                                  ))}
                                </ul>
                              </CardContent>
                            </Card>

                            <Card className="border-red-200 bg-red-50">
                              <CardHeader className="p-3 pb-1">
                                <CardTitle className="text-sm text-red-700">Aspectos Negativos</CardTitle>
                              </CardHeader>
                              <CardContent className="p-3 pt-2">
                                <ul className="text-xs space-y-1 text-red-800">
                                  {results.focus_group_simulation.summary.sentiment_analysis.breakdown.negative_aspects.map((aspect, i) => (
                                    <li key={i} className="list-disc list-inside">{aspect}</li>
                                  ))}
                                </ul>
                              </CardContent>
                            </Card>

                            <Card className="border-blue-200 bg-blue-50">
                              <CardHeader className="p-3 pb-1">
                                <CardTitle className="text-sm text-blue-700">Observaciones Neutrales</CardTitle>
                              </CardHeader>
                              <CardContent className="p-3 pt-2">
                                <ul className="text-xs space-y-1 text-blue-800">
                                  {results.focus_group_simulation.summary.sentiment_analysis.breakdown.neutral_observations.map((aspect, i) => (
                                    <li key={i} className="list-disc list-inside">{aspect}</li>
                                  ))}
                                </ul>
                              </CardContent>
                            </Card>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                        <CardHeader className="pb-2 bg-gray-50">
                          <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                            <Target className="h-4 w-4 text-blue-600" />
                            Patrones Demográficos
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          {results.focus_group_simulation.summary.demographic_patterns.map((pattern, i) => (
                            <div key={i} className="mb-3 pb-3 border-b border-gray-100 last:border-0">
                              <p className="font-medium mb-1">{pattern.pattern}</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {pattern.affected_demographics.map((demo, j) => (
                                  <Badge key={j} variant="outline" className="bg-gray-50">
                                    {demo}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          ))}
                        </CardContent>
                      </Card>
                    </TabsContent>

                    {/* Pestaña Discusión */}
                    <TabsContent value="discussion" className="space-y-4">
                      {results.focus_group_simulation.discussions.map((discussion, i) => (
                        <DiscussionBlock key={i} discussion={discussion} index={i} />
                      ))}
                    </TabsContent>

                    {/* Pestaña Recomendaciones */}
                    <TabsContent value="recommendations" className="space-y-4">
                      <Card className="shadow-sm hover:shadow-md transition-all border-2 border-gray-100">
                        <CardHeader className="pb-2 bg-gray-50">
                          <CardTitle className="text-base text-blue-600 flex items-center gap-2">
                            <List className="h-4 w-4 text-blue-600" />
                            Recomendaciones para Mejorar
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <ul className="space-y-3">
                            {results.focus_group_simulation.summary.recommendations.map((rec, i) => (
                              <li key={i} className="p-3 bg-blue-50 rounded-lg border border-blue-200 flex items-start gap-3">
                                <Sparkles className="h-5 w-5 text-blue-500 shrink-0 mt-0.5" />
                                <span>{rec}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>

                      <Alert className="bg-green-50 border-green-200">
                        <Lightbulb className="h-4 w-4 text-green-600" />
                        <AlertTitle className="text-green-700">Próximos pasos</AlertTitle>
                        <AlertDescription className="text-green-600">
                          Considera implementar estas recomendaciones e iterar con un focus group más específico enfocado en 
                          las áreas de mejora identificadas. Puedes probar diferentes versiones del contenido para comparar resultados.
                        </AlertDescription>
                      </Alert>
                    </TabsContent>
                  </Tabs>
                  </div>
                </CardContent>
              </>
            ) : (
              <CardContent className="py-16">
                <div className="text-center space-y-4">
                  <Users className="h-16 w-16 text-gray-300 mx-auto" />
                  <h3 className="text-xl font-medium text-gray-700">Simulador de Focus Group</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    Simula un focus group virtual con participantes de diferentes perfiles demográficos para evaluar el impacto 
                    y percepción de tu contenido de marketing.
                  </p>
                  <div className="max-w-md mx-auto bg-blue-50 p-4 rounded-lg border border-blue-100">
                    <p className="text-sm text-blue-700">
                      Ingresa tu contenido en el panel izquierdo y haz clic en "Simular Focus Group" para obtener insights cualitativos 
                      de una variedad de perfiles demográficos virtuales.
                    </p>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        )}
      </div>
      
      {/* Botón para volver desde simulación en vivo */}
      {viewMode === 'live-simulation' && (
        <div className="mt-4 flex justify-center">
          <Button 
            variant="outline" 
            className="border-blue-200 text-blue-600"
            onClick={() => setViewMode('setup')}
          >
            Cancelar simulación
          </Button>
        </div>
      )}
    </div>
  );
}