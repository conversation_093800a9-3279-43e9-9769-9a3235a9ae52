# Emma Studio Backend Architecture

This document outlines the standardized architecture for the Emma Studio backend.

## Overview

Emma Studio is a content generation platform that uses AI agents to create various types of content. The backend is built using FastAPI and integrates with CrewAI for agent orchestration.

## Directory Structure

```
backend/
├── app/                    # Main application code
│   ├── api/                # API endpoints
│   │   └── endpoints/      # API route handlers
│   ├── core/               # Core functionality
│   ├── db/                 # Database models and session
│   ├── schemas/            # Pydantic models
│   ├── services/           # Business logic
│   └── main.py             # FastAPI application
├── config/                 # YAML configuration files
│   ├── agents.yaml         # Agent definitions
│   ├── crew.yaml           # Crew configurations
│   ├── tasks.yaml          # Task definitions
│   └── tools.yaml          # Tool definitions
├── crewai_app/             # CrewAI integration
├── main.py                 # Entry point wrapper
└── pyproject.toml          # Poetry dependencies
```

## API Structure

All API endpoints follow a consistent pattern with the `/api/v1` prefix:

- **Health Check**: `/api/v1/health`
- **Content Generation**: `/api/v1/content/*`
- **Crew Management**: `/api/v1/crew/*`
- **Image Generation**: `/api/v1/images/*`

## Configuration Approach

The application uses a combination of:

1. **Environment Variables**: Loaded from `.env` file for sensitive information like API keys
2. **YAML Configuration Files**: For CrewAI components
   - `config/agents.yaml`: Agent definitions
   - `config/tools.yaml`: Tool definitions
   - `config/tasks.yaml`: Task definitions
   - `config/crew.yaml`: Crew configurations

## Running the Application

The application can be run using:

```bash
# Method 1: Using Poetry and Uvicorn directly
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Method 2: Using the wrapper script
poetry run python -m main
```

## Dependency Management

The project uses Poetry for dependency management. Dependencies are organized into groups:

- **web**: FastAPI, Uvicorn, and other web dependencies
- **database**: SQLAlchemy, Alembic, and database tools
- **ai**: OpenAI, Google Generative AI, CrewAI and other AI-related libraries
- **security**: Authentication and security libraries
- **dev**: Development tools like pytest, black, etc.

## CrewAI Integration

CrewAI is used for agent orchestration. The configuration is loaded from YAML files in the `config/` directory. The `crewai_app/` directory contains the integration code.

## Database

The application uses SQLAlchemy for database access. The database models are defined in `app/db/models.py`. The database session is managed in `app/db/session.py`.

## Caching

Redis is used for caching when available. The Redis connection is configured in `app/core/config.py` and managed in `app/db/session.py`.
