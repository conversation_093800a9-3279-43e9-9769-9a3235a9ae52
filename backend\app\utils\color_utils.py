"""
Color utilities for brand color processing and conversion.
"""

import re
from typing import Optional


def convert_hex_to_color_description(hex_color: str) -> str:
    """
    Convert hex color codes to descriptive color names for Ideogram prompts.
    
    Args:
        hex_color: Hex color code (e.g., "#3018ef", "#dd3a5a")
        
    Returns:
        Descriptive color name for use in image generation prompts
    """
    if not hex_color or not isinstance(hex_color, str):
        return "professional blue"
    
    # Clean hex color (remove # if present)
    hex_color = hex_color.lstrip('#').lower()
    
    # Validate hex format
    if not re.match(r'^[0-9a-f]{6}$', hex_color):
        return "professional blue"
    
    # Convert hex to RGB
    try:
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
    except ValueError:
        return "professional blue"
    
    # Determine color family and characteristics
    return _rgb_to_color_description(r, g, b)


def _rgb_to_color_description(r: int, g: int, b: int) -> str:
    """
    Convert RGB values to descriptive color names.
    
    Args:
        r, g, b: RGB color values (0-255)
        
    Returns:
        Descriptive color name
    """
    # Calculate color properties
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    
    # Brightness (0-1)
    brightness = max_val / 255
    
    # Saturation (0-1)
    saturation = (max_val - min_val) / max_val if max_val > 0 else 0
    
    # Determine base color
    if max_val == min_val:  # Grayscale
        if brightness > 0.8:
            return "bright white"
        elif brightness > 0.6:
            return "light gray"
        elif brightness > 0.4:
            return "medium gray"
        elif brightness > 0.2:
            return "dark gray"
        else:
            return "deep black"
    
    # Determine dominant color channel
    if r >= g and r >= b:  # Red dominant
        if g > b * 1.5:  # Yellow-ish
            return _get_yellow_description(brightness, saturation)
        elif b > g * 1.2:  # Purple-ish
            return _get_purple_description(brightness, saturation)
        else:  # Pure red
            return _get_red_description(brightness, saturation)
    
    elif g >= r and g >= b:  # Green dominant
        if r > b * 1.5:  # Yellow-green
            return _get_yellow_green_description(brightness, saturation)
        elif b > r * 1.2:  # Blue-green
            return _get_cyan_description(brightness, saturation)
        else:  # Pure green
            return _get_green_description(brightness, saturation)
    
    else:  # Blue dominant
        if r > g * 1.5:  # Purple-ish
            return _get_purple_description(brightness, saturation)
        elif g > r * 1.2:  # Cyan-ish
            return _get_cyan_description(brightness, saturation)
        else:  # Pure blue
            return _get_blue_description(brightness, saturation)


def _get_red_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for red colors."""
    if saturation < 0.3:
        return "muted pink" if brightness > 0.7 else "dusty rose"
    elif brightness > 0.8:
        return "bright coral" if saturation > 0.7 else "soft pink"
    elif brightness > 0.6:
        return "vibrant red" if saturation > 0.7 else "warm red"
    elif brightness > 0.4:
        return "deep red" if saturation > 0.7 else "burgundy"
    else:
        return "dark crimson"


def _get_blue_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for blue colors."""
    if saturation < 0.3:
        return "light blue" if brightness > 0.7 else "slate blue"
    elif brightness > 0.8:
        return "bright sky blue" if saturation > 0.7 else "powder blue"
    elif brightness > 0.6:
        return "vibrant blue" if saturation > 0.7 else "professional blue"
    elif brightness > 0.4:
        return "deep blue" if saturation > 0.7 else "navy blue"
    else:
        return "midnight blue"


def _get_green_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for green colors."""
    if saturation < 0.3:
        return "sage green" if brightness > 0.7 else "olive green"
    elif brightness > 0.8:
        return "bright lime" if saturation > 0.7 else "mint green"
    elif brightness > 0.6:
        return "vibrant green" if saturation > 0.7 else "forest green"
    elif brightness > 0.4:
        return "deep emerald" if saturation > 0.7 else "dark green"
    else:
        return "hunter green"


def _get_yellow_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for yellow colors."""
    if saturation < 0.3:
        return "cream yellow" if brightness > 0.7 else "golden beige"
    elif brightness > 0.8:
        return "bright yellow" if saturation > 0.7 else "soft yellow"
    elif brightness > 0.6:
        return "golden yellow" if saturation > 0.7 else "warm gold"
    elif brightness > 0.4:
        return "amber gold" if saturation > 0.7 else "bronze"
    else:
        return "dark gold"


def _get_purple_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for purple colors."""
    if saturation < 0.3:
        return "lavender" if brightness > 0.7 else "dusty purple"
    elif brightness > 0.8:
        return "bright magenta" if saturation > 0.7 else "light purple"
    elif brightness > 0.6:
        return "vibrant purple" if saturation > 0.7 else "royal purple"
    elif brightness > 0.4:
        return "deep violet" if saturation > 0.7 else "dark purple"
    else:
        return "midnight purple"


def _get_cyan_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for cyan colors."""
    if saturation < 0.3:
        return "light teal" if brightness > 0.7 else "dusty teal"
    elif brightness > 0.8:
        return "bright cyan" if saturation > 0.7 else "aqua blue"
    elif brightness > 0.6:
        return "vibrant teal" if saturation > 0.7 else "turquoise"
    elif brightness > 0.4:
        return "deep teal" if saturation > 0.7 else "dark cyan"
    else:
        return "midnight teal"


def _get_yellow_green_description(brightness: float, saturation: float) -> str:
    """Get descriptive name for yellow-green colors."""
    if saturation < 0.3:
        return "pale lime" if brightness > 0.7 else "olive"
    elif brightness > 0.8:
        return "bright lime green" if saturation > 0.7 else "spring green"
    elif brightness > 0.6:
        return "vibrant lime" if saturation > 0.7 else "chartreuse"
    elif brightness > 0.4:
        return "deep lime" if saturation > 0.7 else "dark olive"
    else:
        return "forest olive"


def get_complementary_color_description(primary_color: str) -> str:
    """
    Get complementary color description for design harmony.
    
    Args:
        primary_color: Primary color description
        
    Returns:
        Complementary color description
    """
    complementary_map = {
        "vibrant blue": "warm orange",
        "professional blue": "golden yellow",
        "deep blue": "bright orange",
        "vibrant red": "cool green",
        "warm red": "mint green",
        "deep red": "forest green",
        "vibrant green": "coral red",
        "forest green": "warm red",
        "bright yellow": "deep purple",
        "golden yellow": "royal purple",
        "vibrant purple": "lime green",
        "royal purple": "golden yellow"
    }
    
    return complementary_map.get(primary_color, "neutral gray")


def validate_brand_color(hex_color: str) -> bool:
    """
    Validate if a hex color is suitable for brand use.
    
    Args:
        hex_color: Hex color code to validate
        
    Returns:
        True if color is suitable for branding
    """
    if not hex_color or not isinstance(hex_color, str):
        return False
    
    # Clean and validate format
    hex_color = hex_color.lstrip('#').lower()
    if not re.match(r'^[0-9a-f]{6}$', hex_color):
        return False
    
    try:
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
    except ValueError:
        return False
    
    # Check if color has sufficient contrast and saturation for branding
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    
    # Avoid colors that are too light or too dark
    brightness = max_val / 255
    if brightness < 0.2 or brightness > 0.95:
        return False
    
    # Avoid colors with very low saturation (too gray)
    saturation = (max_val - min_val) / max_val if max_val > 0 else 0
    if saturation < 0.3:
        return False
    
    return True
