/**
 * Simple Upload Test for Visual Complexity Analyzer
 * This script tests the upload functionality after the fixes
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🧪 Testing Upload Fix...');

async function testUploadFix() {
  try {
    // Check if services are available
    if (!window.supabase) {
      console.error('❌ Supabase not available');
      return;
    }

    if (!window.designAnalysisService) {
      console.error('❌ designAnalysisService not available');
      return;
    }

    // Check authentication
    const { data: { user }, error: authError } = await window.supabase.auth.getUser();
    if (authError || !user) {
      console.error('❌ Not authenticated:', authError);
      return;
    }

    console.log('✅ Authenticated as:', user.email);

    // Create a test image file
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    
    // Draw a simple test pattern
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 50, 50);
    ctx.fillStyle = '#00FF00';
    ctx.fillRect(50, 0, 50, 50);
    ctx.fillStyle = '#0000FF';
    ctx.fillRect(0, 50, 50, 50);
    ctx.fillStyle = '#FFFF00';
    ctx.fillRect(50, 50, 50, 50);

    // Convert to blob and create file
    const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
    const testFile = new File([blob], 'test-upload-fix.png', { type: 'image/png' });

    console.log('📄 Created test file:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });

    // Test 1: Direct upload test
    console.log('\n🧪 Test 1: Direct uploadImage test');
    try {
      const uploadResult = await window.designAnalysisService.uploadImage(testFile, user.id);
      console.log('✅ Upload successful:', uploadResult);
      
      // Test 2: Verify file exists in storage
      console.log('\n🧪 Test 2: Verify file exists');
      const fileExists = await window.designAnalysisService.checkImageExists(uploadResult);
      console.log('📁 File exists:', fileExists);
      
      // Test 3: Try to retrieve the image
      console.log('\n🧪 Test 3: Retrieve image');
      const imageUrl = await window.designAnalysisService.getImageUrl(uploadResult);
      if (imageUrl) {
        console.log('✅ Image retrieval successful:', imageUrl.substring(0, 50) + '...');
        
        // Test 4: Verify the image loads
        const img = new Image();
        img.onload = () => {
          console.log('✅ Image loads successfully:', img.naturalWidth + 'x' + img.naturalHeight);
        };
        img.onerror = () => {
          console.error('❌ Image failed to load');
        };
        img.src = imageUrl;
      } else {
        console.error('❌ Image retrieval failed');
      }
      
      // Test 5: Test complete saveAnalysis flow
      console.log('\n🧪 Test 5: Complete saveAnalysis flow');
      const testAnalysisData = {
        user_id: user.id,
        tool_type: 'visual-complexity',
        original_filename: testFile.name,
        file_size: testFile.size,
        file_type: testFile.type,
        overall_score: 85,
        complexity_scores: {
          color: 8,
          layout: 9,
          typography: 8,
          elements: 9
        },
        analysis_areas: [
          {
            name: 'Test Area',
            score: 8,
            description: 'Test description for upload fix verification'
          }
        ],
        recommendations: [
          {
            category: 'Test',
            issue: 'Test issue',
            importance: 'alta',
            recommendation: 'Test recommendation for upload fix'
          }
        ],
        ai_analysis_summary: 'Test analysis summary for upload fix verification',
        agent_message: 'Test agent message for upload fix',
        visuai_insights: 'Test insights for upload fix',
        tags: ['upload-fix-test']
      };

      const savedAnalysis = await window.designAnalysisService.saveAnalysis(testAnalysisData, testFile);
      console.log('✅ Analysis saved successfully:', {
        id: savedAnalysis.id,
        file_url: savedAnalysis.file_url,
        hasFileUrl: !!savedAnalysis.file_url
      });

      if (savedAnalysis.file_url) {
        console.log('🎉 SUCCESS: Upload fix is working! File URL was saved to database.');
      } else {
        console.error('❌ ISSUE: Analysis saved but file_url is null');
      }

      // Cleanup
      console.log('\n🧹 Cleaning up test data...');
      await window.designAnalysisService.deleteAnalysis(savedAnalysis.id);
      console.log('✅ Test analysis deleted');

    } catch (uploadError) {
      console.error('❌ Upload test failed:', uploadError);
      console.error('Error details:', {
        message: uploadError.message,
        stack: uploadError.stack
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testUploadFix().then(() => {
  console.log('\n🏁 Upload fix test completed');
}).catch(error => {
  console.error('❌ Test execution failed:', error);
});

// Export for manual testing
window.testUploadFix = testUploadFix;
