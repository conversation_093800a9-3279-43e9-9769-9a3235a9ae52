"""
Stub module to provide compatibility with crewAI without using LangChain.
This allows CrewAI's tool adapters to work without requiring LangChain dependencies.
"""
from typing import Any, Callable, Dict, Optional, Union, TypeVar, List

T = TypeVar('T')

class BaseTool:
    """Minimal implementation of BaseTool for compatibility."""
    name: str
    description: str
    func: Optional[Callable[..., Any]]
    
    def __init__(self, 
                 name: Optional[str] = None, 
                 description: Optional[str] = None, 
                 func: Optional[Callable[..., Any]] = None) -> None:
        """Initialize a BaseTool.
        
        Args:
            name: The name of the tool
            description: The description of the tool
            func: The function to call when the tool is invoked
        """
        self.name = name if name is not None else ""
        self.description = description if description is not None else ""
        self.func = func
    
    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """Call the tool function with the given arguments.
        
        Args:
            *args: Positional arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of calling the function, or None if no function is set
        """
        if self.func:
            return self.func(*args, **kwargs)
        return None


class StructuredTool(BaseTool):
    """Minimal implementation of StructuredTool for compatibility."""
    
    def __init__(self, 
                 name: Optional[str] = None, 
                 description: Optional[str] = None, 
                 func: Optional[Callable[..., Any]] = None) -> None:
        """Initialize a StructuredTool.
        
        Args:
            name: The name of the tool
            description: The description of the tool
            func: The function to call when the tool is invoked
        """
        super().__init__(name, description, func)
