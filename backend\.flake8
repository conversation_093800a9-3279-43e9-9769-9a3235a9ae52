[flake8]
max-line-length = 88
# Ignore specific warnings/errors globally
ignore = E501, W293, W391, W503, E722, E203, E999, F841, F811, W291, E704, E711, E402, E226

# Per-directory ignores
per-file-ignores =
    # Ignore E501 (line too long) and F841 (unused var) in tests
    tests/*:E501,F841
    # Ignore E501 (line too long) in vendored crewAI code
    crewAI/*:E501

# exclude = 
#    .git,
#    __pycache__
