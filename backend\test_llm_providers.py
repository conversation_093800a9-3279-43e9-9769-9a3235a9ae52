"""
Test LLM Providers
A script to test the LLM providers
"""

import os
import asyncio
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the LLM providers
from agents.llm_providers import GeminiProvider, OpenAIProvider


async def test_gemini():
    """Test the Gemini LLM provider."""
    logger.info("Testing Gemini LLM provider")
    
    # Get API key from environment
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        logger.error("No Gemini API key found in environment. Set GEMINI_API_KEY environment variable.")
        return
    
    # Create provider
    provider = GeminiProvider(api_key=api_key)
    
    # Test prompt
    prompt = "Write a short poem about artificial intelligence."
    
    # Generate text
    logger.info(f"Generating text with prompt: {prompt}")
    response = await provider.generate(prompt)
    
    # Print response
    logger.info(f"Response from Gemini:\n{response}")


async def test_openai():
    """Test the OpenAI LLM provider."""
    logger.info("Testing OpenAI LLM provider")
    
    # Get API key from environment
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        logger.error("No OpenAI API key found in environment. Set OPENAI_API_KEY environment variable.")
        return
    
    # Create provider
    provider = OpenAIProvider(api_key=api_key)
    
    # Test prompt
    prompt = "Write a short poem about artificial intelligence."
    
    # Generate text
    logger.info(f"Generating text with prompt: {prompt}")
    response = await provider.generate(prompt)
    
    # Print response
    logger.info(f"Response from OpenAI:\n{response}")


async def main():
    """Run the tests."""
    # Test Gemini
    await test_gemini()
    
    # Test OpenAI
    await test_openai()


if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
