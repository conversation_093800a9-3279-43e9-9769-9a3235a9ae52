@echo off
echo ========================================
echo Starting Emma Studio Backend Server
echo ========================================
echo.

cd backend

echo Attempting to start backend server...

REM Method 1: Try with direct Python (working method)
echo Starting with Python directly...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m uvicorn app.main:app --reload --port 8000 --host 0.0.0.0
if not errorlevel 1 goto :success

REM Method 2: Try poetry command directly
echo Trying with Poetry...
poetry run uvicorn app.main:app --reload --port 8000
if not errorlevel 1 goto :success

echo ERROR: Could not start backend server
echo Please make sure Python and dependencies are installed correctly
pause
exit /b 1

:success
echo Backend server started successfully!
echo Server running at: http://localhost:8000
echo Frontend proxy: http://localhost:3002/api
echo Press Ctrl+C to stop the server
pause
