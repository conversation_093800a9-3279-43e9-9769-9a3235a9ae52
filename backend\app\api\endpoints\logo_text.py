"""
API endpoints for logo with text generation using OpenAI's gpt-image-1 model.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Form, UploadFile, File, HTTPException
from fastapi.responses import StreamingResponse
from app.services.logo_text_service import LogoTextService
from app.models.responses import FrontendResponse
from pydantic import BaseModel
import json

logger = logging.getLogger(__name__)

router = APIRouter()
logo_text_service = LogoTextService()


class FrontendLogoTextResponse(FrontendResponse):
    """Response model for frontend logo with text operations."""
    
    @classmethod
    def from_service_response(cls, service_response: dict) -> "FrontendLogoTextResponse":
        """Convert service response to frontend response."""
        return cls(
            success=service_response.get("success", False),
            message=service_response.get("error") if not service_response.get("success") else "Logo with text generated successfully",
            data={
                "image_url": service_response.get("image_url"),
                "revised_prompt": service_response.get("revised_prompt"),
                "response_id": service_response.get("response_id"),
                "metadata": service_response.get("metadata", {})
            } if service_response.get("success") else None,
            error=service_response.get("error") if not service_response.get("success") else None
        )


@router.post("/generate")
async def generate_logo_text(
    prompt: str = Form(..., description="Description of the logo with text to create"),
    size: str = Form(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)")
) -> FrontendLogoTextResponse:
    """Generate a logo with text using OpenAI's gpt-image-1 model."""
    
    try:
        logger.info(f"🎨 Generating logo with text: {prompt[:100]}...")
        
        # Call the service
        service_response = await logo_text_service.generate_logo_text(
            prompt=prompt,
            size=size
        )
        
        # Convert to frontend response
        return FrontendLogoTextResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in generate_logo_text endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during logo with text generation: {e}"
        )


@router.post("/multi-turn-edit")
async def multi_turn_edit_logo_text(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendLogoTextResponse:
    """Edit an existing logo with text using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing logo with text: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await logo_text_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendLogoTextResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit_logo_text endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn edit: {e}"
        )


@router.post("/edit-with-references")
async def edit_logo_text_with_references(
    prompt: str = Form(..., description="Description of the logo with text to create"),
    reference_images: List[UploadFile] = File(..., description="Reference images to use"),
    size: str = Form(default="auto", description="Image size")
) -> FrontendLogoTextResponse:
    """Generate logo with text using reference images."""
    
    try:
        logger.info(f"🖼️ Generating logo with text using {len(reference_images)} references: {prompt[:100]}...")
        
        # Validate reference images count
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        # Call the service
        service_response = await logo_text_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            size=size
        )
        
        # Convert to frontend response
        return FrontendLogoTextResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_logo_text_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference-based generation: {e}"
        )


@router.post("/edit-with-mask")
async def edit_logo_text_with_mask(
    prompt: str = Form(..., description="Description of the changes to make"),
    image: UploadFile = File(..., description="Original image file"),
    mask: UploadFile = File(..., description="Mask image file (white areas will be edited)")
) -> FrontendLogoTextResponse:
    """Edit logo with text using mask-based editing."""
    
    try:
        logger.info(f"🎭 Editing logo with text using mask: {prompt[:100]}...")
        
        # Call the service
        service_response = await logo_text_service.edit_with_mask(
            prompt=prompt,
            image=image,
            mask=mask
        )
        
        # Convert to frontend response
        return FrontendLogoTextResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in edit_logo_text_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask-based edit: {e}"
        )


@router.post("/stream-generate")
async def stream_generate_logo_text(
    prompt: str = Form(..., description="Description of the logo with text to create")
):
    """Generate logo with text with streaming partial images."""
    
    try:
        logger.info(f"🌊 Streaming logo with text generation: {prompt[:100]}...")
        
        async def generate_stream():
            """Generator function for streaming response."""
            try:
                async for chunk in logo_text_service.stream_generate(prompt):
                    # Convert chunk to JSON and yield
                    yield f"data: {json.dumps(chunk)}\n\n"
                    
                # Send completion signal
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"Error in stream generation: {e}")
                error_chunk = {
                    "success": False,
                    "error": f"Stream error: {str(e)}"
                }
                yield f"data: {json.dumps(error_chunk)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in stream_generate_logo_text endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during streaming generation: {e}"
        )


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for logo with text service."""
    return {"status": "healthy", "service": "logo_text"}
