"""
API endpoints for upscale (image quality enhancement) functionality using Stability AI v2beta API.
"""
import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from typing import Optional

from app.schemas.upscale import (
    UpscaleRequest, 
    UpscaleResponse, 
    UpscaleInitialResponse,
    UpscaleStatusResponse,
    UpscaleMode,
    StylePreset
)
from app.services.upscale_service import (
    upscale_image_stability,
    start_upscale_creative_stability,
    get_upscale_status_stability
)

logger = logging.getLogger(__name__)

router = APIRouter()


def create_upscale_request(
    mode: str = Form(default="fast"),
    prompt: Optional[str] = Form(default=None),
    negative_prompt: Optional[str] = Form(default=None),
    seed: Optional[int] = Form(default=None),
    output_format: str = Form(default="webp"),
    creativity: Optional[float] = Form(default=0.3),
    style_preset: Optional[str] = Form(default=None)
) -> UpscaleRequest:
    """Create UpscaleRequest from form data"""
    return UpscaleRequest(
        mode=mode,
        prompt=prompt,
        negative_prompt=negative_prompt,
        seed=seed,
        output_format=output_format,
        creativity=creativity,
        style_preset=style_preset
    )


@router.post("/upscale", response_model=UpscaleResponse)
async def upscale_image(
    image: UploadFile = File(..., description="Image file to upscale"),
    request: UpscaleRequest = Depends(create_upscale_request)
):
    """
    Upscale an image using Stability AI v2beta API.
    
    Supports three modes:
    - **fast**: Quick 4x upscale (~1 second, no prompt needed)
    - **conservative**: Up to 4K, preserves original aspects (requires prompt)
    - **creative**: Up to 4K, reimagines image (requires prompt, async for complex operations)
    
    **Note**: Creative mode with complex prompts may take longer and should use the async endpoints.
    """
    try:
        logger.info(f"Upscaling image with mode: {request.mode}")
        
        # Para modo creativo complejo, recomendar usar endpoint async
        if request.mode == UpscaleMode.CREATIVE:
            logger.warning("Creative mode detected - consider using /upscale/creative/start for better performance")
        
        result = await upscale_image_stability(image, request)
        
        logger.info(f"Upscale completed successfully - Mode: {result.mode}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upscale endpoint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upscale image: {str(e)}"
        )


@router.post("/upscale/creative/start", response_model=UpscaleInitialResponse)
async def start_creative_upscale(
    image: UploadFile = File(..., description="Image file to upscale"),
    prompt: str = Form(..., description="Description for the upscaled image"),
    negative_prompt: Optional[str] = Form(default=None, description="What you do NOT want to see"),
    seed: Optional[int] = Form(default=None, description="Seed for randomness (0 for random)"),
    output_format: str = Form(default="webp", description="Output format"),
    creativity: Optional[float] = Form(default=0.3, description="Creativity level (0.1-0.5)"),
    style_preset: Optional[str] = Form(default=None, description="Style preset")
):
    """
    Start a creative upscale operation (async).
    
    This endpoint starts the upscale process and returns a generation ID.
    Use the status endpoint to poll for results.
    
    Creative upscale can take images from 64x64 to 1MP and upscale them to 4K resolution,
    reimagining the image with enhanced details based on the prompt.
    """
    try:
        # Crear request para modo creativo
        request = UpscaleRequest(
            mode=UpscaleMode.CREATIVE,
            prompt=prompt,
            negative_prompt=negative_prompt,
            seed=seed,
            output_format=output_format,
            creativity=creativity,
            style_preset=style_preset
        )
        
        logger.info(f"Starting creative upscale with prompt: '{prompt[:50]}...'")
        
        result = await start_upscale_creative_stability(image, request)
        
        logger.info(f"Creative upscale started - Generation ID: {result.generation_id}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error starting creative upscale: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start creative upscale: {str(e)}"
        )


@router.get("/upscale/status/{generation_id}", response_model=UpscaleStatusResponse)
async def get_upscale_status(generation_id: str):
    """
    Get the status of a creative upscale operation.
    
    Poll this endpoint to check if your creative upscale is complete.
    
    **Status codes:**
    - `IN_PROGRESS`: Still processing
    - `COMPLETED`: Finished successfully, image_url available
    - `FAILED`: Failed, error message available
    """
    try:
        logger.info(f"Checking status for generation ID: {generation_id}")
        
        result = await get_upscale_status_stability(generation_id)
        
        if result.status == "COMPLETED":
            logger.info(f"Generation {generation_id} completed successfully")
        elif result.status == "FAILED":
            logger.warning(f"Generation {generation_id} failed: {result.error}")
        else:
            logger.info(f"Generation {generation_id} still in progress")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error checking upscale status: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check upscale status: {str(e)}"
        )


@router.get("/upscale/modes")
async def get_upscale_modes():
    """
    Get available upscale modes and their descriptions.
    """
    return {
        "modes": [
            {
                "id": "fast",
                "name": "Rápido",
                "description": "Mejora 4x la resolución en ~1 segundo. Ideal para imágenes comprimidas.",
                "credits": 1,
                "max_resolution": "4x original",
                "requires_prompt": False,
                "processing_time": "~1 segundo"
            },
            {
                "id": "conservative", 
                "name": "Conservador",
                "description": "Mejora hasta 4K preservando todos los aspectos originales. Mínimas alteraciones.",
                "credits": 25,
                "max_resolution": "4K (4 megapíxeles)",
                "requires_prompt": True,
                "processing_time": "~30-60 segundos"
            },
            {
                "id": "creative",
                "name": "Creativo", 
                "description": "Mejora hasta 4K reimaginando la imagen. Ideal para imágenes muy degradadas.",
                "credits": 25,
                "max_resolution": "4K (4 megapíxeles)",
                "requires_prompt": True,
                "processing_time": "~2-5 minutos"
            }
        ],
        "style_presets": [
            {"id": "enhance", "name": "Mejorar"},
            {"id": "photographic", "name": "Fotográfico"},
            {"id": "digital-art", "name": "Arte Digital"},
            {"id": "cinematic", "name": "Cinematográfico"},
            {"id": "anime", "name": "Anime"},
            {"id": "fantasy-art", "name": "Arte Fantástico"},
            {"id": "3d-model", "name": "Modelo 3D"},
            {"id": "analog-film", "name": "Película Analógica"},
            {"id": "comic-book", "name": "Cómic"},
            {"id": "isometric", "name": "Isométrico"},
            {"id": "line-art", "name": "Arte Lineal"},
            {"id": "low-poly", "name": "Low Poly"},
            {"id": "neon-punk", "name": "Neon Punk"},
            {"id": "origami", "name": "Origami"},
            {"id": "pixel-art", "name": "Pixel Art"},
            {"id": "tile-texture", "name": "Textura de Azulejo"}
        ]
    }
