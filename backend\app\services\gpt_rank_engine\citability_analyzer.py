"""
Citability Analyzer Module
Analyzes how citable content is for AI models
"""

import logging
import re
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class CitabilityAnalyzer:
    """Analyzes how citable content is for AI models."""
    
    def __init__(self):
        logger.info("✅ Citability Analyzer initialized successfully")
    
    def calculate_citability_score(self, content: str) -> float:
        """
        Calculate how citable the content is for AI models.
        
        Args:
            content: Content to analyze
            
        Returns:
            Citability score (0-100)
        """
        try:
            score = 40.0  # Base score
            
            # Check for clear, quotable statements
            sentences = [s.strip() for s in content.split('.') if s.strip()]
            clear_statements = 0
            
            for sentence in sentences:
                if (20 <= len(sentence) <= 150 and 
                    not sentence.startswith(('Por ejemplo', 'Como', 'Si', 'Cuando')) and
                    any(word in sentence.lower() for word in ['es', 'son', 'significa', 'define', 'consiste'])):
                    clear_statements += 1
            
            score += min(clear_statements * 2, 25)
            
            # Check for definitions
            definition_patterns = [
                'se define como', 'es un', 'es una', 'consiste en',
                'significa', 'se refiere a', 'se entiende por'
            ]
            definition_count = sum(1 for pattern in definition_patterns if pattern in content.lower())
            score += min(definition_count * 5, 20)
            
            # Check for factual statements
            factual_indicators = [
                'según', 'de acuerdo con', 'los datos muestran', 'la investigación indica',
                'estudios revelan', 'se ha demostrado', 'está comprobado'
            ]
            factual_count = sum(1 for indicator in factual_indicators if indicator in content.lower())
            score += min(factual_count * 3, 15)
            
            return min(score, 100)
            
        except Exception as e:
            logger.error(f"❌ Citability score calculation failed: {str(e)}")
            return 40.0
    
    def analyze_quotable_content(self, content: str) -> Dict[str, Any]:
        """Analyze quotable content segments."""
        try:
            sentences = [s.strip() for s in content.split('.') if s.strip()]
            
            quotable_segments = {
                "definitions": [],
                "facts": [],
                "statistics": [],
                "key_statements": []
            }
            
            for sentence in sentences:
                sentence_lower = sentence.lower()
                
                # Check for definitions
                if any(pattern in sentence_lower for pattern in ['se define como', 'es un', 'consiste en']):
                    quotable_segments["definitions"].append(sentence)
                
                # Check for facts
                elif any(pattern in sentence_lower for pattern in ['según', 'los datos', 'la investigación']):
                    quotable_segments["facts"].append(sentence)
                
                # Check for statistics
                elif re.search(r'\d+%|\d+\.\d+%', sentence):
                    quotable_segments["statistics"].append(sentence)
                
                # Check for key statements (clear, concise, informative)
                elif (20 <= len(sentence) <= 100 and 
                      any(word in sentence_lower for word in ['importante', 'clave', 'fundamental'])):
                    quotable_segments["key_statements"].append(sentence)
            
            # Calculate quotability score
            total_quotable = sum(len(segments) for segments in quotable_segments.values())
            quotability_score = min(total_quotable * 5, 100)
            
            return {
                "quotable_segments": quotable_segments,
                "total_quotable_segments": total_quotable,
                "quotability_score": quotability_score,
                "best_quotes": self._select_best_quotes(quotable_segments)
            }
            
        except Exception as e:
            logger.error(f"❌ Quotable content analysis failed: {str(e)}")
            return {
                "quotability_score": 40,
                "error": str(e)
            }
    
    def _select_best_quotes(self, quotable_segments: Dict[str, List[str]]) -> List[str]:
        """Select the best quotes from quotable segments."""
        best_quotes = []
        
        # Prioritize definitions and facts
        for category in ["definitions", "facts", "statistics", "key_statements"]:
            segments = quotable_segments.get(category, [])
            # Sort by length (prefer medium-length quotes)
            sorted_segments = sorted(segments, key=lambda x: abs(len(x) - 60))
            best_quotes.extend(sorted_segments[:2])  # Take top 2 from each category
        
        return best_quotes[:5]  # Return top 5 overall
