"""
Schemas for outpaint (expand image) functionality using Stability AI v2beta API.
"""

from typing import Optional, Literal
from pydantic import BaseModel, Field


class OutpaintRequest(BaseModel):
    """Request schema for Stability AI outpaint API."""
    
    left: Optional[int] = Field(
        default=0,
        ge=0,
        le=2000,
        description="Number of pixels to outpaint on the left side (0-2000)"
    )
    
    right: Optional[int] = Field(
        default=0,
        ge=0,
        le=2000,
        description="Number of pixels to outpaint on the right side (0-2000)"
    )
    
    up: Optional[int] = Field(
        default=0,
        ge=0,
        le=2000,
        description="Number of pixels to outpaint on the top (0-2000)"
    )
    
    down: Optional[int] = Field(
        default=0,
        ge=0,
        le=2000,
        description="Number of pixels to outpaint on the bottom (0-2000)"
    )
    
    prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="Text prompt describing what to generate in the expanded areas"
    )
    
    creativity: Optional[float] = Field(
        default=0.5,
        ge=0.0,
        le=1.0,
        description="Controls how creative the expansion should be (0.0-1.0)"
    )
    
    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation (0 = random)"
    )
    
    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="png",
        description="Dictates the content-type of the generated image"
    )

    style_preset: Optional[Literal[
        "3d-model", "analog-film", "anime", "cinematic", "comic-book", 
        "digital-art", "enhance", "fantasy-art", "isometric", "line-art", 
        "low-poly", "modeling-compound", "neon-punk", "origami", 
        "photographic", "pixel-art", "tile-texture"
    ]] = Field(
        default=None,
        description="Guides the image model towards a particular style"
    )

    def has_outpaint_direction(self) -> bool:
        """Check if at least one outpaint direction is specified."""
        return any([self.left > 0, self.right > 0, self.up > 0, self.down > 0])


class OutpaintResponse(BaseModel):
    """Response schema from Stability AI outpaint API."""
    
    image: str = Field(description="Base64 encoded image data")
    seed: Optional[int] = Field(description="The seed used for generation")
    finish_reason: str = Field(description="Reason the generation finished")


class FrontendOutpaintResponse(BaseModel):
    """Response schema for frontend outpaint requests."""
    
    success: bool = Field(description="Whether the operation was successful")
    image_url: Optional[str] = Field(default=None, description="Data URL of the generated image")
    seed: Optional[int] = Field(default=None, description="The seed used for generation")
    finish_reason: Optional[str] = Field(default=None, description="Reason the generation finished")
    metadata: Optional[dict] = Field(default=None, description="Additional metadata")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")
