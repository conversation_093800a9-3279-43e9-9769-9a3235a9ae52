#!/usr/bin/env python3
"""
Test script for the image generator API
"""

import requests
import json

def test_health():
    """Test the health endpoint"""
    
    url = "http://localhost:8000/api/image-generator/health"
    
    print("Testing Health Endpoint...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Health check passed!")
            print(f"Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_image_generator():
    """Test the image generator API endpoint"""
    
    url = "http://localhost:8000/api/image-generator/generate"
    
    # Test data
    data = {
        "prompt": "a beautiful sunset over mountains",
        "resolution": "1024x1024",
        "rendering_speed": "DEFAULT",
        "magic_prompt": "AUTO",
        "num_images": "1",
        "style_type": "GENERAL"
    }
    
    print("\nTesting Image Generator API...")
    print(f"URL: {url}")
    print(f"Data: {data}")
    
    try:
        # Make the request
        response = requests.post(url, data=data, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if result.get("success") and result.get("image_url"):
                print(f"🎨 Image generated successfully!")
                print(f"Image URL: {result['image_url']}")
                return True
            else:
                print(f"❌ API returned success=False: {result.get('error', 'Unknown error')}")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out (this is normal for image generation)")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Image Generator API Integration")
    print("=" * 50)
    
    # Test health first
    health_ok = test_health()
    
    if health_ok:
        # Test image generation
        test_image_generator()
    else:
        print("❌ Health check failed, skipping image generation test")
