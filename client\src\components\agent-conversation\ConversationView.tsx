import React, { useEffect, useRef } from "react";
import { Agent, AgentMessage as AgentMessageType } from "@shared/agent-types";
import { AgentMessage } from "./AgentMessage";
import { Card } from "@/components/ui/card";
import { Loader2, Users } from "lucide-react";

interface ConversationViewProps {
  messages: AgentMessageType[];
  agents: Agent[];
  isLoading: boolean;
}

export function ConversationView({
  messages,
  agents,
  isLoading,
}: ConversationViewProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Desplazarse al final cuando llegan nuevos mensajes
  useEffect(() => {
    if (messagesEndRef.current && messages.length > 0) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  useEffect(() => {
    console.log("ConversationView - Agentes recibidos:", agents.length);
  }, [agents]);

  // Encontrar un agente por su ID
  const findAgent = (agentId: string): Agent => {
    // Para los mensajes del sistema
    if (agentId === "system") {
      return {
        id: "system",
        name: "<PERSON><PERSON><PERSON>",
        role: "coordinador",
        description: "Coordina el trabajo entre los agentes",
        systemPrompt: "",
        color: "#4b5563",
        emoji: "⚙️",
        strengths: [],
        expertise: [],
      };
    }

    const agent = agents.find((a) => a.id === agentId);
    if (!agent) {
      console.log(`Agente no encontrado: ${agentId}`);
      // Asignar un color basado en el ID para mantener consistencia
      const hashCode =
        agentId.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0) %
        360;
      const hue = hashCode;
      const color = `hsl(${hue}, 70%, 60%)`;

      // Agente genérico si no se encuentra el específico
      return {
        id: agentId,
        name: "Agente " + agentId.substring(0, 4),
        role: "especialista",
        description: "Un agente en el sistema",
        systemPrompt: "",
        color: color,
        emoji: "👤",
        strengths: [],
        expertise: [],
      };
    }
    return agent;
  };

  return (
    <Card className="p-4 h-full overflow-y-auto bg-gray-50 border-2 border-black shadow-neo">
      <div className="space-y-4">
        <div className="text-center text-2xl font-bold mb-4 border-b-2 border-black pb-2">
          Conversación entre agentes
        </div>

        {messages.length === 0 && !isLoading ? (
          <div className="text-center p-8 space-y-6">
            <div className="text-gray-500">
              Aún no hay mensajes. Cuando inicie la conversación, aquí
              aparecerán los mensajes entre agentes.
            </div>

            {/* Mostrar los agentes disponibles aún cuando no hay mensajes */}
            {agents.length > 0 ? (
              <div className="bg-white rounded-lg p-4 border-2 border-black shadow-neo">
                <h3 className="font-medium mb-3 text-xl">
                  Agentes listos para colaborar:
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {agents.map((agent) => (
                    <div
                      key={agent.id}
                      className="flex items-center gap-2 p-2 border-2 rounded-md shadow-neo"
                      style={{ borderColor: agent.color || "#000" }}
                    >
                      <div
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white text-sm"
                        style={{ backgroundColor: agent.color || "#333" }}
                      >
                        {agent.emoji || agent.name.charAt(0)}
                      </div>
                      <div>
                        <div className="font-bold text-sm">{agent.name}</div>
                        <div className="text-xs">{agent.role}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="bg-red-50 p-4 rounded-lg border-2 border-red-500">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-red-600" />
                  <p className="text-red-700 font-bold">
                    ¡ALERTA! No se encontraron agentes
                  </p>
                </div>
                <p className="text-red-600 text-sm mt-2">
                  ERROR CRÍTICO: Los agentes no se han cargado correctamente.
                  Intenta lo siguiente:
                </p>
                <ul className="list-disc list-inside text-red-600 text-sm mt-2">
                  <li>
                    Vuelve atrás y completa todos los campos del formulario
                  </li>
                  <li>
                    Asegúrate de ingresar información válida en todos los campos
                  </li>
                  <li>Si el problema persiste, contacta a soporte técnico</li>
                </ul>
              </div>
            )}
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <AgentMessage
                key={message.id}
                message={message}
                agent={findAgent(message.agentId)}
              />
            ))}

            {isLoading && (
              <div className="flex justify-center p-4">
                <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              </div>
            )}

            {/* Elemento invisible para scroll automático */}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
    </Card>
  );
}
