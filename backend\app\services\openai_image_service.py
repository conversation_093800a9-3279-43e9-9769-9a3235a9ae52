"""
Service for OpenAI image generation using gpt-image-1 model.
Handles image generation, editing with references, and mask-based editing.
"""

import logging
import base64
import httpx
import io
import tempfile
import os
import json
from typing import List, Optional, Dict, Any
from fastapi import UploadFile
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OpenAIImageService:
    """Service for OpenAI image operations using gpt-image-1."""

    def __init__(self):
        self.api_key = settings.OPENAI_API_KEY
        self.base_url = "https://api.openai.com/v1"

    async def generate_image(self, prompt: str, size: str = "auto") -> Dict[str, Any]:
        """
        Generate an image using gpt-image-1.

        Args:
            prompt: Description of the image to create
            size: Image size (1024x1024, 1792x1024, 1024x1792, auto)

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("OpenAI API key not configured")
            return {"success": False, "error": "OpenAI API key not configured"}

        try:
            # Mejorar el prompt para generación de imágenes
            enhanced_prompt = f"""Crea una imagen profesional, de alta calidad y visualmente atractiva: {prompt}.

La imagen debe tener:
- Excelente composición y iluminación
- Detalles nítidos y bien definidos
- Colores vibrantes y armoniosos
- Estilo profesional y moderno
- Alta calidad visual
- Diseño atractivo y memorable

Estilo: Profesional, moderno, alta calidad, visualmente impactante."""

            payload = {
                "model": "gpt-image-1",
                "prompt": enhanced_prompt,
                "n": 1,
                "size": size,
                "quality": "auto",  # auto, low, medium, high
                "output_format": "png",  # png, jpeg, webp
                "background": "auto"  # auto, transparent, opaque
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            logger.info(f"🎨 Generating image: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/images/generations",
                    json=payload,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"OpenAI error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"OpenAI API error: {response.status_code} - {error_text}"}

                result = response.json()

                # Para gpt-image-1, la respuesta puede venir en diferentes formatos
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    # Intentar obtener la imagen en diferentes formatos
                    b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                    image_url_direct = image_data.get("url")

                    if b64_image:
                        # Guardar imagen base64 como archivo temporal
                        image_bytes = base64.b64decode(b64_image)
                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
                        temp_file.write(image_bytes)
                        temp_file.close()

                        # Devolver la ruta del archivo temporal
                        return {
                            "success": True,
                            "image_url": f"/temp/{os.path.basename(temp_file.name)}",
                            "revised_prompt": result.get("revised_prompt", enhanced_prompt),
                            "response_id": result.get("id"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "size": size,
                                "quality": "auto",
                                "format": "png"
                            }
                        }
                    elif image_url_direct:
                        return {
                            "success": True,
                            "image_url": image_url_direct,
                            "revised_prompt": result.get("revised_prompt", enhanced_prompt),
                            "response_id": result.get("id"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "size": size,
                                "quality": "auto",
                                "format": "png"
                            }
                        }
                    else:
                        logger.error("No image data found in response")
                        return {"success": False, "error": "No image data in response"}
                else:
                    logger.error(f"Unexpected response format: {result}")
                    return {"success": False, "error": "Unexpected response format"}

        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    def encode_image(self, file_content: bytes) -> str:
        """
        Encode image content to base64.

        Args:
            file_content: The image content as bytes

        Returns:
            Base64 encoded string
        """
        return base64.b64encode(file_content).decode("utf-8")
    
    async def edit_with_references(self, prompt: str, reference_images: List[UploadFile], size: str = "auto") -> Dict[str, Any]:
        """
        Generate image using reference images.
        First analyzes the reference images with GPT-4 Vision, then generates with gpt-image-1.

        Args:
            prompt: Description of the image to create
            reference_images: List of reference images to use
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            logger.info(f"🖼️ Analyzing {len(reference_images)} reference images...")

            # Paso 1: Analizar imágenes de referencia con GPT-4 Vision
            vision_messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"""Analiza estas imágenes de referencia y crea un prompt mejorado para generar una imagen profesional basada en: {prompt}

Considera:
- Estilo visual de las referencias
- Esquemas de color
- Composición y elementos
- Iluminación y atmósfera
- Calidad y detalles
- Estilo profesional y moderno

Genera un prompt detallado en español para crear una imagen que incorpore los mejores elementos de las referencias."""
                        }
                    ]
                }
            ]

            # Agregar imágenes a los mensajes
            for i, image_file in enumerate(reference_images):
                try:
                    image_content = await image_file.read()
                    await image_file.seek(0)  # Reset file pointer

                    base64_image = self.encode_image(image_content)

                    vision_messages[0]["content"].append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                            "detail": "high"
                        }
                    })

                    logger.info(f"✅ Reference image {i+1} processed successfully")

                except Exception as e:
                    logger.error(f"Error processing reference image {i+1}: {e}")
                    continue

            # Llamar a GPT-4 Vision
            vision_payload = {
                "model": "gpt-4-vision-preview",
                "messages": vision_messages,
                "max_tokens": 500
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=60.0) as client:
                vision_response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=vision_payload,
                    headers=headers
                )

                if vision_response.status_code != 200:
                    logger.error(f"Vision API error: {vision_response.status_code} - {vision_response.text}")
                    # Fallback to original prompt
                    enhanced_prompt = f"""Crea una imagen profesional de alta calidad: {prompt}. Diseño moderno, limpio y memorable."""
                else:
                    vision_result = vision_response.json()
                    enhanced_prompt = vision_result["choices"][0]["message"]["content"]

                logger.info(f"✨ Enhanced prompt created from references: {enhanced_prompt[:200]}...")

                # Paso 2: Generar imagen con el prompt mejorado
                generation_payload = {
                    "model": "gpt-image-1",
                    "prompt": enhanced_prompt,
                    "n": 1,
                    "size": size,
                    "quality": "auto",
                    "output_format": "png",
                    "background": "auto"
                }

                generation_response = await client.post(
                    f"{self.base_url}/images/generations",
                    json=generation_payload,
                    headers=headers
                )

                if generation_response.status_code != 200:
                    error_text = generation_response.text
                    logger.error(f"Generation error: {generation_response.status_code} - {error_text}")
                    return {"success": False, "error": f"Generation error: {error_text}"}

                result = generation_response.json()

                # Procesar resultado
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                    image_url_direct = image_data.get("url")

                    if b64_image:
                        # Guardar imagen base64 como archivo temporal
                        image_bytes = base64.b64decode(b64_image)
                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
                        temp_file.write(image_bytes)
                        temp_file.close()

                        return {
                            "success": True,
                            "image_url": f"/temp/{os.path.basename(temp_file.name)}",
                            "revised_prompt": enhanced_prompt,
                            "response_id": result.get("id"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "size": size,
                                "type": "reference_based",
                                "reference_count": len(reference_images)
                            }
                        }
                    elif image_url_direct:
                        return {
                            "success": True,
                            "image_url": image_url_direct,
                            "revised_prompt": enhanced_prompt,
                            "response_id": result.get("id"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "size": size,
                                "type": "reference_based",
                                "reference_count": len(reference_images)
                            }
                        }
                    else:
                        return {"success": False, "error": "No image data in generation response"}
                else:
                    return {"success": False, "error": "Unexpected generation response format"}

        except Exception as e:
            logger.error(f"Error in reference-based generation: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}
    
    async def edit_with_mask(self, prompt: str, image: UploadFile, mask: UploadFile) -> Dict[str, Any]:
        """
        Edit image using mask-based editing with gpt-image-1.

        Args:
            prompt: Description of the changes to make
            image: Original image file
            mask: Mask image file (white areas will be edited)

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Leer archivos
            image_content = await image.read()
            mask_content = await mask.read()

            # Codificar a base64
            image_b64 = self.encode_image(image_content)
            mask_b64 = self.encode_image(mask_content)

            # Mejorar prompt para edición de imágenes
            enhanced_prompt = f"""Edita esta imagen de manera profesional: {prompt}.

Mantén:
- La calidad y estilo original
- Coherencia visual
- Detalles nítidos
- Iluminación natural
- Composición equilibrada

Aplica los cambios solo en las áreas marcadas en blanco de la máscara."""

            payload = {
                "model": "gpt-image-1",
                "prompt": enhanced_prompt,
                "image": f"data:image/png;base64,{image_b64}",
                "mask": f"data:image/png;base64,{mask_b64}",
                "n": 1,
                "size": "auto",
                "quality": "auto",
                "output_format": "png"
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            logger.info(f"🎭 Editing image with mask: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/images/edits",
                    json=payload,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"OpenAI edit error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"OpenAI edit error: {error_text}"}

                result = response.json()

                # Procesar resultado
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                    image_url_direct = image_data.get("url")

                    if b64_image:
                        # Guardar imagen base64 como archivo temporal
                        image_bytes = base64.b64decode(b64_image)
                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
                        temp_file.write(image_bytes)
                        temp_file.close()

                        return {
                            "success": True,
                            "image_url": f"/temp/{os.path.basename(temp_file.name)}",
                            "revised_prompt": enhanced_prompt,
                            "response_id": result.get("id"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "type": "mask_edit"
                            }
                        }
                    elif image_url_direct:
                        return {
                            "success": True,
                            "image_url": image_url_direct,
                            "revised_prompt": enhanced_prompt,
                            "response_id": result.get("id"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "type": "mask_edit"
                            }
                        }
                    else:
                        return {"success": False, "error": "No image data in edit response"}
                else:
                    return {"success": False, "error": "Unexpected edit response format"}

        except Exception as e:
            logger.error(f"Error editing image with mask: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}
    
    async def multi_turn_edit(self, previous_response_id: str, edit_prompt: str) -> Dict[str, Any]:
        """
        Edit an existing image using multi-turn generation.

        Args:
            previous_response_id: ID of the previous response to build upon
            edit_prompt: Description of the changes to make

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            payload = {
                "model": "gpt-4.1-mini",
                "previous_response_id": previous_response_id,
                "input": edit_prompt,
                "tools": [{"type": "image_generation"}]
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            logger.info(f"🔄 Multi-turn editing image: {edit_prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"OpenAI error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"OpenAI error: {error_text}"}

                result = response.json()

                # Procesar respuesta de multi-turn
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    message = choice.get("message", {})

                    # Buscar herramientas de generación de imágenes
                    tool_calls = message.get("tool_calls", [])
                    for tool_call in tool_calls:
                        if tool_call.get("type") == "image_generation":
                            # Extraer datos de imagen
                            function_args = json.loads(tool_call.get("function", {}).get("arguments", "{}"))

                            if "image_url" in function_args:
                                return {
                                    "success": True,
                                    "image_url": function_args["image_url"],
                                    "revised_prompt": edit_prompt,
                                    "response_id": result.get("id"),
                                    "metadata": {
                                        "model": "gpt-4.1-mini",
                                        "type": "multi_turn_edit"
                                    }
                                }

                logger.error("No image generation found in multi-turn response")
                return {"success": False, "error": "No image generation in response"}

        except Exception as e:
            logger.error(f"Error in multi-turn edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
openai_image_service = OpenAIImageService()
