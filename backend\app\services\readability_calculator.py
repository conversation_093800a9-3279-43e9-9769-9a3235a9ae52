"""
Real Readability Calculator for Emma Studio
Implements Flesch-Kincaid, SMOG, and other proven readability algorithms
"""

import re
import math
import logging
from typing import Dict, List, Tuple
from dataclasses import dataclass
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize

logger = logging.getLogger(__name__)

@dataclass
class ReadabilityResult:
    """Readability analysis results with multiple metrics"""
    flesch_reading_ease: float
    flesch_kincaid_grade: float
    smog_index: float
    automated_readability_index: float
    coleman_liau_index: float
    gunning_fog_index: float
    overall_score: float
    reading_level: str
    target_audience: str
    suggestions: List[str]
    text_statistics: Dict[str, int]

class RealReadabilityCalculator:
    """
    Real Readability Calculator using proven algorithms
    Implements multiple readability formulas for accurate assessment
    """
    
    def __init__(self):
        # Reading level mappings
        self.reading_levels = {
            (0, 30): ("Muy Difícil", "Graduado universitario"),
            (30, 50): ("<PERSON><PERSON><PERSON><PERSON><PERSON>", "Universitario"),
            (50, 60): ("Moderadamente Difícil", "Bachillerato"),
            (60, 70): ("Estándar", "8º-9º grado"),
            (70, 80): ("Moderadamente Fácil", "7º grado"),
            (80, 90): ("Fácil", "6º grado"),
            (90, 100): ("Muy Fácil", "5º grado")
        }
        
        # Spanish syllable patterns for better accuracy
        self.spanish_vowels = set('aeiouáéíóúü')
        self.spanish_diphthongs = {
            'ai', 'au', 'ei', 'eu', 'oi', 'ou', 'ia', 'ie', 'io', 'iu',
            'ua', 'ue', 'ui', 'uo', 'ay', 'ey', 'oy', 'uy'
        }
    
    def calculate_readability(self, content: str, language: str = 'es') -> ReadabilityResult:
        """
        Calculate comprehensive readability metrics
        
        Args:
            content: Text content to analyze
            language: Content language ('es' or 'en')
            
        Returns:
            ReadabilityResult with multiple readability metrics
        """
        try:
            # Clean and prepare text
            clean_text = self._clean_text(content)
            
            # Calculate basic text statistics
            stats = self._calculate_text_statistics(clean_text, language)
            
            # Calculate readability scores
            flesch_ease = self._calculate_flesch_reading_ease(stats, language)
            flesch_grade = self._calculate_flesch_kincaid_grade(stats)
            smog = self._calculate_smog_index(stats)
            ari = self._calculate_automated_readability_index(stats)
            coleman_liau = self._calculate_coleman_liau_index(stats)
            gunning_fog = self._calculate_gunning_fog_index(stats)
            
            # Calculate overall score (weighted average)
            overall_score = self._calculate_overall_score(
                flesch_ease, flesch_grade, smog, ari, coleman_liau, gunning_fog
            )
            
            # Determine reading level and target audience
            reading_level, target_audience = self._get_reading_level(overall_score)
            
            # Generate improvement suggestions
            suggestions = self._generate_suggestions(stats, overall_score, language)
            
            return ReadabilityResult(
                flesch_reading_ease=flesch_ease,
                flesch_kincaid_grade=flesch_grade,
                smog_index=smog,
                automated_readability_index=ari,
                coleman_liau_index=coleman_liau,
                gunning_fog_index=gunning_fog,
                overall_score=overall_score,
                reading_level=reading_level,
                target_audience=target_audience,
                suggestions=suggestions,
                text_statistics=stats
            )
            
        except Exception as e:
            logger.error(f"Readability calculation failed: {str(e)}")
            raise
    
    def _clean_text(self, content: str) -> str:
        """Clean HTML and normalize text"""
        # Remove HTML tags
        clean_text = re.sub(r'<[^>]+>', ' ', content)
        # Normalize whitespace
        clean_text = re.sub(r'\s+', ' ', clean_text)
        # Keep punctuation for sentence detection
        return clean_text.strip()
    
    def _calculate_text_statistics(self, text: str, language: str) -> Dict[str, int]:
        """Calculate basic text statistics needed for readability formulas"""
        # Tokenize sentences and words
        sentences = sent_tokenize(text)
        words = word_tokenize(text)
        
        # Filter out punctuation and numbers for word count
        actual_words = [word for word in words if word.isalpha()]
        
        # Count characters (letters only)
        characters = sum(len(word) for word in actual_words)
        
        # Count syllables
        total_syllables = sum(self._count_syllables(word, language) for word in actual_words)
        
        # Count complex words (3+ syllables)
        complex_words = sum(1 for word in actual_words 
                          if self._count_syllables(word, language) >= 3)
        
        # Count long words (6+ characters)
        long_words = sum(1 for word in actual_words if len(word) >= 6)
        
        return {
            'sentences': len(sentences),
            'words': len(actual_words),
            'characters': characters,
            'syllables': total_syllables,
            'complex_words': complex_words,
            'long_words': long_words,
            'avg_sentence_length': len(actual_words) / len(sentences) if sentences else 0,
            'avg_word_length': characters / len(actual_words) if actual_words else 0,
            'avg_syllables_per_word': total_syllables / len(actual_words) if actual_words else 0
        }
    
    def _count_syllables(self, word: str, language: str) -> int:
        """Count syllables in a word with language-specific rules"""
        word = word.lower()
        
        if language == 'es':
            return self._count_syllables_spanish(word)
        else:
            return self._count_syllables_english(word)
    
    def _count_syllables_spanish(self, word: str) -> int:
        """Count syllables in Spanish words"""
        if len(word) <= 1:
            return 1
        
        # Count vowel groups
        syllable_count = 0
        prev_was_vowel = False
        
        i = 0
        while i < len(word):
            char = word[i]
            
            if char in self.spanish_vowels:
                if not prev_was_vowel:
                    # Check for diphthongs
                    if i < len(word) - 1:
                        diphthong = word[i:i+2]
                        if diphthong in self.spanish_diphthongs:
                            syllable_count += 1
                            i += 2
                            prev_was_vowel = False
                            continue
                    
                    syllable_count += 1
                prev_was_vowel = True
            else:
                prev_was_vowel = False
            
            i += 1
        
        return max(syllable_count, 1)
    
    def _count_syllables_english(self, word: str) -> int:
        """Count syllables in English words (simplified)"""
        word = word.lower()
        vowels = 'aeiouy'
        syllable_count = 0
        prev_was_vowel = False
        
        for char in word:
            if char in vowels:
                if not prev_was_vowel:
                    syllable_count += 1
                prev_was_vowel = True
            else:
                prev_was_vowel = False
        
        # Handle silent 'e'
        if word.endswith('e') and syllable_count > 1:
            syllable_count -= 1
        
        return max(syllable_count, 1)
    
    def _calculate_flesch_reading_ease(self, stats: Dict[str, int], language: str) -> float:
        """
        Calculate Flesch Reading Ease score
        Higher scores = easier to read (0-100 scale)
        """
        if stats['sentences'] == 0 or stats['words'] == 0:
            return 0.0
        
        avg_sentence_length = stats['words'] / stats['sentences']
        avg_syllables_per_word = stats['syllables'] / stats['words']
        
        if language == 'es':
            # Spanish Flesch formula (Fernández-Huerta adaptation)
            score = 206.84 - (1.02 * avg_sentence_length) - (0.60 * avg_syllables_per_word * 100)
        else:
            # English Flesch formula
            score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)
        
        return max(0, min(100, score))
    
    def _calculate_flesch_kincaid_grade(self, stats: Dict[str, int]) -> float:
        """Calculate Flesch-Kincaid Grade Level"""
        if stats['sentences'] == 0 or stats['words'] == 0:
            return 0.0
        
        avg_sentence_length = stats['words'] / stats['sentences']
        avg_syllables_per_word = stats['syllables'] / stats['words']
        
        grade = (0.39 * avg_sentence_length) + (11.8 * avg_syllables_per_word) - 15.59
        return max(0, grade)
    
    def _calculate_smog_index(self, stats: Dict[str, int]) -> float:
        """Calculate SMOG (Simple Measure of Gobbledygook) Index"""
        if stats['sentences'] == 0:
            return 0.0
        
        # SMOG formula: 1.0430 * sqrt(complex_words * (30/sentences)) + 3.1291
        complex_words_per_30_sentences = stats['complex_words'] * (30 / stats['sentences'])
        smog = 1.0430 * math.sqrt(complex_words_per_30_sentences) + 3.1291
        
        return max(0, smog)
    
    def _calculate_automated_readability_index(self, stats: Dict[str, int]) -> float:
        """Calculate Automated Readability Index (ARI)"""
        if stats['sentences'] == 0 or stats['words'] == 0:
            return 0.0
        
        avg_chars_per_word = stats['characters'] / stats['words']
        avg_words_per_sentence = stats['words'] / stats['sentences']
        
        ari = (4.71 * avg_chars_per_word) + (0.5 * avg_words_per_sentence) - 21.43
        return max(0, ari)
    
    def _calculate_coleman_liau_index(self, stats: Dict[str, int]) -> float:
        """Calculate Coleman-Liau Index"""
        if stats['words'] == 0:
            return 0.0
        
        # Characters per 100 words
        chars_per_100_words = (stats['characters'] / stats['words']) * 100
        # Sentences per 100 words
        sentences_per_100_words = (stats['sentences'] / stats['words']) * 100
        
        cli = (0.0588 * chars_per_100_words) - (0.296 * sentences_per_100_words) - 15.8
        return max(0, cli)
    
    def _calculate_gunning_fog_index(self, stats: Dict[str, int]) -> float:
        """Calculate Gunning Fog Index"""
        if stats['sentences'] == 0 or stats['words'] == 0:
            return 0.0
        
        avg_sentence_length = stats['words'] / stats['sentences']
        percentage_complex_words = (stats['complex_words'] / stats['words']) * 100
        
        fog = 0.4 * (avg_sentence_length + percentage_complex_words)
        return max(0, fog)
    
    def _calculate_overall_score(self, flesch_ease: float, flesch_grade: float,
                                smog: float, ari: float, coleman_liau: float,
                                gunning_fog: float) -> float:
        """Calculate weighted overall readability score"""
        # Convert grade-level scores to 0-100 scale (inverse relationship)
        grade_scores = [flesch_grade, smog, ari, coleman_liau, gunning_fog]
        avg_grade = sum(grade_scores) / len(grade_scores)
        
        # Convert to 0-100 scale (lower grade = higher score)
        grade_score = max(0, 100 - (avg_grade * 8))  # Rough conversion
        
        # Weighted average: Flesch Reading Ease (60%) + Grade Level Average (40%)
        overall = (flesch_ease * 0.6) + (grade_score * 0.4)
        
        return max(0, min(100, overall))
    
    def _get_reading_level(self, score: float) -> Tuple[str, str]:
        """Get reading level description based on score"""
        for (min_score, max_score), (level, audience) in self.reading_levels.items():
            if min_score <= score < max_score:
                return level, audience
        
        return "Muy Fácil", "5º grado"  # Default for scores >= 100
    
    def _generate_suggestions(self, stats: Dict[str, int], score: float, 
                            language: str) -> List[str]:
        """Generate actionable suggestions to improve readability"""
        suggestions = []
        
        # Sentence length suggestions
        if stats['avg_sentence_length'] > 20:
            suggestions.append("Reduce la longitud promedio de las oraciones (actual: {:.1f} palabras, óptimo: 15-20)".format(stats['avg_sentence_length']))
        elif stats['avg_sentence_length'] < 8:
            suggestions.append("Las oraciones son muy cortas, considera combinar algunas para mejor fluidez")
        
        # Word complexity suggestions
        complex_percentage = (stats['complex_words'] / stats['words']) * 100 if stats['words'] > 0 else 0
        if complex_percentage > 15:
            suggestions.append("Reduce el uso de palabras complejas (actual: {:.1f}%, óptimo: <15%)".format(complex_percentage))
        
        # Syllable suggestions
        if stats['avg_syllables_per_word'] > 2.0:
            suggestions.append("Usa palabras más simples con menos sílabas")
        
        # Overall score suggestions
        if score < 50:
            suggestions.append("El texto es difícil de leer. Simplifica el vocabulario y acorta las oraciones")
        elif score > 80:
            suggestions.append("El texto puede ser demasiado simple para tu audiencia objetivo")
        
        # Content length suggestions
        if stats['words'] < 100:
            suggestions.append("El contenido es muy corto para un análisis preciso de legibilidad")
        
        return suggestions

# Singleton instance
readability_calculator = RealReadabilityCalculator()
