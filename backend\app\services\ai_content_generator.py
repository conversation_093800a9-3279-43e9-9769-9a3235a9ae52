"""
Real AI Content Generation Service for Emma Studio
Uses actual LLM APIs (OpenAI GPT-4, Google Gemini) for genuine content creation
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import openai
import google.generativeai as genai
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ContentGenerationRequest:
    """Content generation request parameters"""
    topic: str
    keywords: List[str]
    content_type: str  # 'blog', 'article', 'social', 'email'
    tone: str         # 'professional', 'casual', 'technical', 'friendly'
    length: str       # 'short', 'medium', 'long'
    target_audience: str
    include_images: bool = True
    seo_optimized: bool = True
    saio_optimized: bool = True

@dataclass
class GeneratedContent:
    """Generated content result"""
    title: str
    content: str
    meta_description: str
    keywords: List[str]
    image_prompts: List[Dict[str, str]]
    seo_score: float
    saio_score: float
    word_count: int

class RealAIContentGenerator:
    """
    Real AI Content Generator using actual LLM APIs
    Generates SEO and SAIO optimized content for ranking improvements
    """
    
    def __init__(self, openai_api_key: str = None, gemini_api_key: str = None):
        self.openai_api_key = openai_api_key
        self.gemini_api_key = gemini_api_key
        
        # Initialize OpenAI
        if openai_api_key:
            openai.api_key = openai_api_key
        
        # Initialize Gemini
        if gemini_api_key:
            genai.configure(api_key=gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Content length mappings
        self.length_mappings = {
            'short': (300, 600),
            'medium': (600, 1200),
            'long': (1200, 2500)
        }
        
        # SEO-optimized prompts based on proven strategies
        self.seo_prompts = {
            'blog': self._get_blog_prompt(),
            'article': self._get_article_prompt(),
            'social': self._get_social_prompt(),
            'email': self._get_email_prompt()
        }
    
    async def generate_content(self, request: ContentGenerationRequest) -> GeneratedContent:
        """
        Generate SEO and SAIO optimized content using real AI models
        
        Args:
            request: Content generation parameters
            
        Returns:
            GeneratedContent with optimized content for ranking
        """
        try:
            # Choose the best model for the task
            if self.gemini_api_key and request.content_type in ['blog', 'article']:
                # Use Gemini for long-form content
                content_result = await self._generate_with_gemini(request)
            elif self.openai_api_key:
                # Use GPT-4 for all content types
                content_result = await self._generate_with_openai(request)
            else:
                raise ValueError("No AI API keys configured")
            
            # Generate image prompts
            image_prompts = await self._generate_image_prompts(
                content_result['content'], request.include_images
            )
            
            # Calculate estimated scores (would be calculated by analysis engines)
            seo_score = self._estimate_seo_score(content_result['content'], request.keywords)
            saio_score = self._estimate_saio_score(content_result['content'])
            
            return GeneratedContent(
                title=content_result['title'],
                content=content_result['content'],
                meta_description=content_result['meta_description'],
                keywords=request.keywords,
                image_prompts=image_prompts,
                seo_score=seo_score,
                saio_score=saio_score,
                word_count=len(content_result['content'].split())
            )
            
        except Exception as e:
            logger.error(f"Content generation failed: {str(e)}")
            raise
    
    async def _generate_with_gemini(self, request: ContentGenerationRequest) -> Dict[str, str]:
        """Generate content using Google Gemini"""
        try:
            prompt = self._build_optimized_prompt(request)
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=2048,
                )
            )
            
            return self._parse_gemini_response(response.text, request)
            
        except Exception as e:
            logger.error(f"Gemini generation failed: {str(e)}")
            raise
    
    async def _generate_with_openai(self, request: ContentGenerationRequest) -> Dict[str, str]:
        """Generate content using OpenAI GPT-4"""
        try:
            prompt = self._build_optimized_prompt(request)
            
            response = await asyncio.to_thread(
                openai.ChatCompletion.create,
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "Eres un experto en SEO y creación de contenido optimizado para motores de búsqueda y IA."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2048
            )
            
            return self._parse_openai_response(response.choices[0].message.content, request)
            
        except Exception as e:
            logger.error(f"OpenAI generation failed: {str(e)}")
            raise
    
    def _build_optimized_prompt(self, request: ContentGenerationRequest) -> str:
        """Build SEO and SAIO optimized prompt"""
        min_words, max_words = self.length_mappings[request.length]
        keywords_str = ", ".join(request.keywords)
        
        base_prompt = f"""
Crea un {request.content_type} optimizado para SEO y SAIO sobre "{request.topic}".

REQUISITOS OBLIGATORIOS:
- Palabras clave principales: {keywords_str}
- Longitud: {min_words}-{max_words} palabras
- Tono: {request.tone}
- Audiencia: {request.target_audience}

OPTIMIZACIÓN SEO:
- Densidad de palabras clave: 1-3%
- Título H1 con palabra clave principal
- Subtítulos H2 y H3 con palabras clave secundarias
- Meta descripción de 150-160 caracteres
- Estructura clara con párrafos de 50-150 palabras

OPTIMIZACIÓN SAIO (IA):
- Incluir 3-5 preguntas frecuentes con respuestas
- Usar listas numeradas y con viñetas
- Formato pregunta-respuesta
- Información estructurada y fácil de procesar
- Incluir datos específicos y estadísticas

ESTRUCTURA REQUERIDA:
1. Título principal (H1)
2. Introducción (100-150 palabras)
3. 3-4 secciones principales (H2)
4. Subsecciones con H3 cuando sea necesario
5. Listas y elementos estructurados
6. Sección de preguntas frecuentes
7. Conclusión

FORMATO DE RESPUESTA:
TÍTULO: [título optimizado]
META_DESCRIPCIÓN: [meta descripción]
CONTENIDO: [contenido HTML completo]
"""
        
        if request.content_type == 'blog':
            base_prompt += self.seo_prompts['blog']
        elif request.content_type == 'article':
            base_prompt += self.seo_prompts['article']
        
        return base_prompt
    
    def _get_blog_prompt(self) -> str:
        """SEO-optimized blog prompt"""
        return """
ADICIONAL PARA BLOG:
- Incluir call-to-action al final
- Añadir enlaces internos sugeridos
- Incluir datos y estadísticas relevantes
- Usar storytelling cuando sea apropiado
- Optimizar para featured snippets
"""
    
    def _get_article_prompt(self) -> str:
        """SEO-optimized article prompt"""
        return """
ADICIONAL PARA ARTÍCULO:
- Enfoque más técnico y detallado
- Incluir fuentes y referencias
- Análisis profundo del tema
- Casos de estudio o ejemplos
- Conclusiones basadas en datos
"""
    
    def _get_social_prompt(self) -> str:
        """Social media optimized prompt"""
        return """
ADICIONAL PARA SOCIAL:
- Contenido conciso y engaging
- Incluir hashtags relevantes
- Call-to-action claro
- Formato fácil de compartir
"""
    
    def _get_email_prompt(self) -> str:
        """Email optimized prompt"""
        return """
ADICIONAL PARA EMAIL:
- Subject line optimizado
- Personalización
- Call-to-action prominente
- Formato mobile-friendly
"""
    
    def _parse_gemini_response(self, response: str, request: ContentGenerationRequest) -> Dict[str, str]:
        """Parse Gemini response into structured format"""
        lines = response.split('\n')
        
        title = ""
        meta_description = ""
        content = ""
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('TÍTULO:'):
                title = line.replace('TÍTULO:', '').strip()
                current_section = 'title'
            elif line.startswith('META_DESCRIPCIÓN:'):
                meta_description = line.replace('META_DESCRIPCIÓN:', '').strip()
                current_section = 'meta'
            elif line.startswith('CONTENIDO:'):
                current_section = 'content'
            elif current_section == 'content' and line:
                content += line + '\n'
        
        # Fallback if parsing fails
        if not title:
            title = f"Guía Completa: {request.topic}"
        if not meta_description:
            meta_description = f"Descubre todo sobre {request.topic}. Guía completa con estrategias probadas."
        if not content:
            content = response  # Use full response as content
        
        return {
            'title': title,
            'meta_description': meta_description,
            'content': content
        }
    
    def _parse_openai_response(self, response: str, request: ContentGenerationRequest) -> Dict[str, str]:
        """Parse OpenAI response into structured format"""
        return self._parse_gemini_response(response, request)  # Same parsing logic
    
    async def _generate_image_prompts(self, content: str, include_images: bool) -> List[Dict[str, str]]:
        """Generate image prompts for the content"""
        if not include_images:
            return []
        
        # Extract key concepts for image generation
        # This would use AI to analyze content and suggest relevant images
        prompts = [
            {
                "prompt": "Professional office environment with modern technology and natural lighting",
                "alt": "Oficina profesional moderna",
                "placement": "header"
            },
            {
                "prompt": "Infographic showing data visualization and charts",
                "alt": "Infografía con datos y gráficos",
                "placement": "inline"
            },
            {
                "prompt": "Team collaboration in modern workspace",
                "alt": "Equipo colaborando en oficina moderna",
                "placement": "inline"
            }
        ]
        
        return prompts
    
    def _estimate_seo_score(self, content: str, keywords: List[str]) -> float:
        """Estimate SEO score based on content analysis"""
        score = 70  # Base score
        
        # Check keyword presence
        content_lower = content.lower()
        for keyword in keywords:
            if keyword.lower() in content_lower:
                score += 5
        
        # Check structure
        if '<h1>' in content:
            score += 5
        if '<h2>' in content:
            score += 5
        if '<ul>' in content or '<ol>' in content:
            score += 5
        
        return min(score, 100)
    
    def _estimate_saio_score(self, content: str) -> float:
        """Estimate SAIO score based on AI optimization factors"""
        score = 60  # Base score
        
        # Check for Q&A format
        if '?' in content:
            score += 10
        
        # Check for lists
        if '<ul>' in content or '<ol>' in content:
            score += 10
        
        # Check for structured content
        if '<h2>' in content and '<h3>' in content:
            score += 10
        
        return min(score, 100)

# Factory function to create configured instance
def create_ai_content_generator(openai_key: str = None, gemini_key: str = None) -> RealAIContentGenerator:
    """Create AI content generator with API keys"""
    return RealAIContentGenerator(openai_key, gemini_key)
