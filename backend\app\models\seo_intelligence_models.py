"""
Database models for SEO Intelligence System
Stores analysis results, content data, and optimization history
"""

from sqlalchemy import Column, Integer, String, Float, Text, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class SEOAnalysisResult(Base):
    """Store SEO analysis results for content"""
    __tablename__ = "seo_analysis_results"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    content_hash = Column(String, nullable=False, index=True)  # Hash of content for caching
    
    # SEO Metrics
    overall_score = Column(Float, nullable=False)
    keyword_density = Column(JSON, nullable=True)  # {"keyword": density}
    tf_idf_scores = Column(JSON, nullable=True)
    readability_score = Column(Float, nullable=True)
    readability_grade = Column(Float, nullable=True)
    structure_score = Column(Float, nullable=True)
    
    # Content Statistics
    word_count = Column(Integer, nullable=False)
    sentence_count = Column(Integer, nullable=False)
    paragraph_count = Column(Integer, nullable=False)
    heading_analysis = Column(JSON, nullable=True)  # {"h1": count, "h2": count, ...}
    
    # Analysis Results
    suggestions = Column(JSON, nullable=True)  # List of suggestions
    issues = Column(JSON, nullable=True)  # List of critical issues
    target_keywords = Column(JSON, nullable=True)  # List of target keywords
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class SAIOAnalysisResult(Base):
    """Store SAIO/GEO analysis results for AI search optimization"""
    __tablename__ = "saio_analysis_results"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    content_hash = Column(String, nullable=False, index=True)
    
    # SAIO Metrics
    saio_score = Column(Float, nullable=False)
    ai_readiness = Column(Float, nullable=False)
    qa_score = Column(Float, nullable=False)
    list_score = Column(Float, nullable=False)
    eat_score = Column(Float, nullable=False)
    freshness_score = Column(Float, nullable=False)
    multimedia_score = Column(Float, nullable=False)
    source_authority_score = Column(Float, nullable=False)
    
    # Detailed Analysis
    qa_optimization = Column(JSON, nullable=True)
    list_optimization = Column(JSON, nullable=True)
    eat_compliance = Column(JSON, nullable=True)
    source_authority = Column(JSON, nullable=True)
    recommendations = Column(JSON, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class GeneratedContent(Base):
    """Store AI-generated content and metadata"""
    __tablename__ = "generated_content"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    user_id = Column(String, nullable=True, index=True)
    
    # Content Data
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    meta_description = Column(String, nullable=True)
    content_type = Column(String, nullable=False)  # blog, article, social, email
    
    # Generation Parameters
    topic = Column(String, nullable=False)
    keywords = Column(JSON, nullable=True)  # List of keywords
    tone = Column(String, nullable=True)
    length = Column(String, nullable=True)
    target_audience = Column(String, nullable=True)
    
    # Quality Metrics
    seo_score = Column(Float, nullable=True)
    saio_score = Column(Float, nullable=True)
    word_count = Column(Integer, nullable=True)
    
    # AI Model Info
    ai_model_used = Column(String, nullable=True)  # gemini, openai
    generation_time = Column(Float, nullable=True)  # seconds
    
    # Image Data
    image_prompts = Column(JSON, nullable=True)  # List of image prompts
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class KeywordAnalysisResult(Base):
    """Store keyword analysis results"""
    __tablename__ = "keyword_analysis_results"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    content_hash = Column(String, nullable=False, index=True)
    
    # Keyword Data
    primary_keywords = Column(JSON, nullable=True)
    secondary_keywords = Column(JSON, nullable=True)
    semantic_keywords = Column(JSON, nullable=True)
    keyword_density = Column(JSON, nullable=True)
    tf_idf_scores = Column(JSON, nullable=True)
    keyword_distribution = Column(JSON, nullable=True)  # positions in text
    
    # Competitive Analysis
    competitive_score = Column(Float, nullable=False)
    optimization_suggestions = Column(JSON, nullable=True)
    
    # Metadata
    language = Column(String, default='es')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ReadabilityAnalysisResult(Base):
    """Store readability analysis results"""
    __tablename__ = "readability_analysis_results"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    content_hash = Column(String, nullable=False, index=True)
    
    # Readability Metrics
    flesch_reading_ease = Column(Float, nullable=False)
    flesch_kincaid_grade = Column(Float, nullable=False)
    smog_index = Column(Float, nullable=False)
    automated_readability_index = Column(Float, nullable=False)
    coleman_liau_index = Column(Float, nullable=False)
    gunning_fog_index = Column(Float, nullable=False)
    overall_score = Column(Float, nullable=False)
    
    # Reading Level
    reading_level = Column(String, nullable=False)
    target_audience = Column(String, nullable=False)
    
    # Text Statistics
    text_statistics = Column(JSON, nullable=True)
    suggestions = Column(JSON, nullable=True)
    
    # Metadata
    language = Column(String, default='es')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ContentOptimizationHistory(Base):
    """Track content optimization history and improvements"""
    __tablename__ = "content_optimization_history"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, nullable=False, index=True)
    content_id = Column(String, nullable=True)  # Reference to original content
    
    # Before/After Metrics
    before_seo_score = Column(Float, nullable=True)
    after_seo_score = Column(Float, nullable=True)
    before_saio_score = Column(Float, nullable=True)
    after_saio_score = Column(Float, nullable=True)
    before_readability = Column(Float, nullable=True)
    after_readability = Column(Float, nullable=True)
    
    # Optimization Actions
    optimizations_applied = Column(JSON, nullable=True)  # List of optimizations
    suggestions_followed = Column(JSON, nullable=True)
    time_to_optimize = Column(Float, nullable=True)  # minutes
    
    # Results
    improvement_percentage = Column(Float, nullable=True)
    ranking_improvement = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)

class APIUsageStats(Base):
    """Track API usage for SaaS billing and analytics"""
    __tablename__ = "api_usage_stats"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    project_id = Column(String, nullable=True, index=True)
    
    # API Endpoint
    endpoint = Column(String, nullable=False)  # analyze, generate, etc.
    method = Column(String, nullable=False)    # POST, GET
    
    # Usage Data
    request_size = Column(Integer, nullable=True)  # bytes
    response_size = Column(Integer, nullable=True) # bytes
    processing_time = Column(Float, nullable=True) # seconds
    
    # AI Model Usage
    ai_model_used = Column(String, nullable=True)
    tokens_used = Column(Integer, nullable=True)
    
    # Status
    status_code = Column(Integer, nullable=False)
    success = Column(Boolean, nullable=False)
    error_message = Column(String, nullable=True)
    
    # Billing
    cost = Column(Float, nullable=True)  # USD
    
    # Metadata
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
