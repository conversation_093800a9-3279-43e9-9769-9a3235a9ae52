"use client"
import { motion } from "framer-motion";
import { BarChart3, Eye, Target, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function ActivitySection() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 2.2 }}
      >
        <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent flex items-center">
                <BarChart3 className="w-5 h-5 text-[#3018ef] mr-2" />
                Actividad Reciente
              </h3>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="ghost" size="sm" className="hover:bg-gray-100 rounded-lg">
                  <Eye className="h-4 w-4 mr-2" />
                  Ver todo
                </Button>
              </motion.div>
            </div>

            <div className="space-y-4">
              {[
                { action: "Post generado", time: "Hace 2 horas", type: "success", icon: "📝" },
                { action: "Análisis completado", time: "Hace 4 horas", type: "info", icon: "📊" },
                { action: "Imagen creada", time: "Ayer", type: "success", icon: "🖼️" },
                { action: "Campaña optimizada", time: "Hace 2 días", type: "warning", icon: "🎯" }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-4 p-4 rounded-xl hover:bg-white/60 transition-all duration-300 border border-transparent hover:border-white/30"
                  whileHover={{ scale: 1.02, x: 4 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <div className="text-2xl">{item.icon}</div>
                  <div className="flex-1">
                    <p className="font-semibold text-gray-900">{item.action}</p>
                    <p className="text-sm text-gray-500">{item.time}</p>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${
                    item.type === 'success' ? 'bg-green-500' :
                    item.type === 'info' ? 'bg-blue-500' :
                    item.type === 'warning' ? 'bg-yellow-500' : 'bg-gray-500'
                  }`}></div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 2.4 }}
      >
        <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent flex items-center">
                <Target className="w-5 h-5 text-[#dd3a5a] mr-2" />
                Proyectos Activos
              </h3>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="ghost" size="sm" className="hover:bg-gray-100 rounded-lg">
                  <Plus className="h-4 w-4 mr-2" />
                  Nuevo
                </Button>
              </motion.div>
            </div>

            <div className="space-y-4">
              {[
                { name: "Campaña Q1 2024", progress: 75, status: "En progreso", color: "green", icon: "🚀" },
                { name: "Rediseño de marca", progress: 45, status: "Planificación", color: "blue", icon: "🎨" },
                { name: "Contenido redes sociales", progress: 90, status: "Casi listo", color: "purple", icon: "📱" }
              ].map((project, index) => (
                <motion.div
                  key={index}
                  className="p-5 border border-white/20 rounded-xl hover:border-white/40 transition-all duration-300 bg-white/60 backdrop-blur-md hover:bg-white/80"
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{project.icon}</span>
                      <h4 className="font-bold text-gray-900">{project.name}</h4>
                    </div>
                    <Badge
                      className={`
                        ${project.color === 'green' ? 'bg-green-100 text-green-700 border-green-200' : ''}
                        ${project.color === 'blue' ? 'bg-blue-100 text-blue-700 border-blue-200' : ''}
                        ${project.color === 'purple' ? 'bg-purple-100 text-purple-700 border-purple-200' : ''}
                      `}
                    >
                      {project.status}
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <motion.div
                      className={`h-2 rounded-full ${
                        project.color === 'green' ? 'bg-green-500' :
                        project.color === 'blue' ? 'bg-blue-500' :
                        project.color === 'purple' ? 'bg-purple-500' : 'bg-gray-500'
                      }`}
                      initial={{ width: 0 }}
                      animate={{ width: `${project.progress}%` }}
                      transition={{ duration: 1, delay: 2.6 + index * 0.2 }}
                    />
                  </div>
                  <p className="text-sm text-gray-600">{project.progress}% completado</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
