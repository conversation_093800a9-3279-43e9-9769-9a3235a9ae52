"""
Image generation service for creating contextual image prompts and managing image providers.
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class ImageGenerationService:
    """Service for generating contextual image prompts and managing image providers."""
    
    def __init__(self):
        self.model = None
        self._initialize_ai()
    
    def _initialize_ai(self):
        """Initialize AI model for image prompt generation."""
        try:
            import google.generativeai as genai
            from app.core.config import settings
            
            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Image Generation Service initialized successfully")
            else:
                logger.warning("⚠️ GEMINI_API_KEY not configured - using fallback mode")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Image Generation Service: {e}")
    
    def select_image_provider(self, template: str, content_description: str) -> str:
        """
        Intelligently select image provider based on template and content.
        
        PRIORITY ORDER:
        1. LOCAL MEME: Template-based memes (cost-effective)
        2. IDEOGRAM 3.0 QUALITY: All other content types (PRIMARY)
        
        Args:
            template: The selected template name
            content_description: Description of the content to generate
            
        Returns:
            Provider name: 'meme' or 'ideogram'
        """
        content_lower = content_description.lower()
        
        # LOCAL MEME service for template-based memes (cost optimization)
        if template == "Meme" or any(keyword in content_lower for keyword in [
            "meme", "humor", "gracioso", "divertido", "vs", "antes", "después", "template"
        ]):
            return "meme"
        
        # IDEOGRAM 3.0 QUALITY for all other content (text-heavy, visual, professional)
        # This includes: quotes, comics, landscapes, products, scenes, graphics, professional photography
        return "ideogram"
    
    async def generate_image_prompt_strategic(self, visual_hook: str, content_strategy: Dict[str, Any], platform: str, brand_color: str = None) -> str:
        """
        Generate SIMPLE and DIRECT image prompt for Ideogram AI.
        Uses proven format that works consistently with Ideogram 3.0 Quality.

        Args:
            visual_hook: The text that will appear in the image
            content_strategy: Strategic content plan with context analysis
            platform: Target platform
            brand_color: Brand color (hex code like #3018ef)

        Returns:
            Simple, direct prompt optimized for Ideogram's best practices
        """
        try:
            topic = content_strategy.get("topic", "marketing digital")
            context_analysis = content_strategy.get("context_analysis", {})

            # Get context-specific information
            nicho = context_analysis.get("nicho", "general")
            tipo_contenido = context_analysis.get("tipo_contenido", "educational")

            # Optimize text for Ideogram rendering (remove emojis, shorten long words)
            optimized_hook = self._optimize_text_for_ideogram(visual_hook)

            # Simple, proven format that works with Ideogram
            # Following official Ideogram documentation: "A [context] with text that reads: '[text]'"

            # Context mapping based on content type and topic
            context_map = {
                "educational": "professional social media graphic",
                "promotional": "modern marketing poster",
                "inspirational": "motivational design",
                "informational": "clean infographic style",
                "entertainment": "engaging social media post",
                "news": "professional news graphic"
            }

            # Topic-specific context refinement
            if "fitness" in topic.lower() or "salud" in topic.lower():
                context = "fitness-themed social media graphic"
            elif "comida" in topic.lower() or "receta" in topic.lower():
                context = "food-themed social media graphic"
            elif "tecnología" in topic.lower() or "tech" in topic.lower():
                context = "tech-themed social media graphic"
            elif "negocio" in topic.lower() or "business" in topic.lower():
                context = "business-themed social media graphic"
            else:
                context = context_map.get(tipo_contenido, "professional social media graphic")

            # Platform-specific adjustments
            if platform == "LinkedIn":
                context = f"professional {context}"
            elif platform == "Instagram":
                context = f"modern {context}"

            # Create simple, direct prompt following Ideogram best practices
            simple_prompt = f'A {context} with text that reads: "{optimized_hook}"'

            logger.info(f"✅ Generated SIMPLE prompt for Ideogram: {simple_prompt[:100]}...")
            return simple_prompt

        except Exception as e:
            logger.error(f"Error generating image prompt: {e}")
            return self._get_fallback_image_prompt(visual_hook, content_strategy, platform, brand_color)

    def _optimize_text_for_ideogram(self, text: str) -> str:
        """
        Optimize text for better Ideogram rendering.
        Removes emojis, shortens long words, limits length.
        """
        import re

        # Remove emojis
        emoji_pattern = re.compile("["
                                   u"\U0001F600-\U0001F64F"  # emoticons
                                   u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                   u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                   u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                   u"\U00002702-\U000027B0"
                                   u"\U000024C2-\U0001F251"
                                   "]+", flags=re.UNICODE)
        text = emoji_pattern.sub('', text)

        # Remove hashtags
        text = re.sub(r'#\w+', '', text)

        # Shorten common long words for better rendering
        word_replacements = {
            "transformación": "cambio",
            "estrategias": "tips",
            "resultados": "éxito",
            "marketing": "marketing",
            "digital": "digital",
            "profesional": "pro",
            "increíble": "genial",
            "extraordinario": "único",
            "innovación": "innovar",
            "tradicional": "clásico"
        }

        for long_word, short_word in word_replacements.items():
            text = text.replace(long_word, short_word)

        # Limit length for better rendering (Ideogram works best with shorter text)
        if len(text) > 60:
            words = text.split()
            if len(words) > 8:
                text = ' '.join(words[:8])

        return text.strip()
    
    def _get_fallback_image_prompt(self, visual_hook: str, content_strategy: Dict[str, Any], platform: str, brand_color: str = None) -> str:
        """Generate simple fallback image prompt when AI fails"""
        topic = content_strategy.get("topic", "marketing digital")

        # Optimize text for Ideogram rendering
        optimized_hook = self._optimize_text_for_ideogram(visual_hook)

        # Simple fallback based on topic using proven Ideogram format
        if any(word in topic.lower() for word in ["receta", "cocina", "comida", "food"]):
            return f'A food-themed social media graphic with text that reads: "{optimized_hook}"'

        elif any(word in topic.lower() for word in ["fitness", "gym", "ejercicio", "workout"]):
            return f'A fitness-themed social media graphic with text that reads: "{optimized_hook}"'

        elif any(word in topic.lower() for word in ["gaming", "juego", "gamer"]):
            return f'A gaming-themed social media graphic with text that reads: "{optimized_hook}"'

        elif any(word in topic.lower() for word in ["belleza", "maquillaje", "beauty"]):
            return f'A beauty-themed social media graphic with text that reads: "{optimized_hook}"'

        else:
            # Business/professional simple fallback
            return f'A professional social media graphic with text that reads: "{optimized_hook}"'
    
    def generate_image_prompt_legacy(self, template: str, brand_info: Dict[str, Any], post_text: str, platform: str) -> str:
        """
        Legacy function for backward compatibility with the /generate endpoint.
        Creates basic image prompts - the new strategic system uses generate_image_prompt_strategic.
        """
        business_name = brand_info.get("businessName", "Business")
        industry = brand_info.get("industry", "business")
        
        # Basic image prompts for backward compatibility
        basic_prompts = {
            "Balance": f"Professional business graphic for {business_name} in {industry}, clean modern design, corporate aesthetic",
            "Educational": f"Educational infographic style for {business_name}, clean layout, professional {industry} context",
            "Motivational": f"Inspiring motivational design for {business_name}, uplifting colors, success-oriented imagery",
            "Informativo": f"Informational graphic for {business_name}, data visualization, {industry} professional design"
        }
        
        base_prompt = basic_prompts.get(template, basic_prompts["Balance"])
        
        # Add platform optimization
        platform_additions = {
            "Instagram": "square format, Instagram-optimized, social media aesthetic",
            "LinkedIn": "professional business setting, corporate aesthetic, LinkedIn-appropriate",
            "Facebook": "engaging social media style, Facebook-optimized, community feel",
            "X": "dynamic composition, Twitter-style visual, concise visual impact"
        }
        
        platform_addition = platform_additions.get(platform, platform_additions["Instagram"])
        
        return f"{base_prompt}, {platform_addition}"

    async def generate_similar_image_prompt(self, visual_hook: str, enhanced_strategy: Dict[str, Any], platform: str, brand_color: str = None, reference_metadata: Dict[str, Any] = None) -> str:
        """
        Generate image prompt similar to reference post style.

        Args:
            visual_hook: The text that will appear in the image
            enhanced_strategy: Strategy from similarity analysis
            platform: Target platform
            brand_color: Brand color (hex code like #3018ef)
            reference_metadata: Metadata from reference post

        Returns:
            Simple, direct prompt optimized for similar style
        """
        try:
            # Get similarity strategy details
            tono = enhanced_strategy.get("tono", "profesional")
            tipo_contenido = enhanced_strategy.get("tipo_contenido", "educational")
            elementos_clave = enhanced_strategy.get("elementos_clave", [])

            # Optimize text for Ideogram rendering
            optimized_hook = self._optimize_text_for_ideogram(visual_hook)

            # Context mapping based on similarity strategy
            if tono == "educativo":
                context = "educational social media graphic"
            elif tono == "promocional":
                context = "promotional marketing poster"
            elif tono == "inspiracional":
                context = "motivational design"
            else:
                context = "professional social media graphic"

            # Platform-specific adjustments
            if platform == "LinkedIn":
                context = f"professional {context}"
            elif platform == "Instagram":
                context = f"modern {context}"

            # Create simple, direct prompt following Ideogram best practices
            # Keep it similar to reference style but unique
            similar_prompt = f'A {context} with text that reads: "{optimized_hook}"'

            logger.info(f"✅ Generated SIMILAR prompt for Ideogram: {similar_prompt[:100]}...")
            return similar_prompt

        except Exception as e:
            logger.error(f"Error generating similar image prompt: {e}")
            return self._get_fallback_image_prompt(visual_hook, enhanced_strategy, platform, brand_color)
