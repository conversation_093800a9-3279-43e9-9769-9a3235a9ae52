"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, CheckCircle2, XCircle, AlertTriangle } from "lucide-react";

export default function SEODebugComponent() {
  const [isTestingAPI, setIsTestingAPI] = useState(false);
  const [isTestingPersistent, setIsTestingPersistent] = useState(false);
  const [apiResult, setApiResult] = useState<any>(null);
  const [persistentResult, setPersistentResult] = useState<any>(null);
  const { toast } = useToast();

  const testAPIConnection = async () => {
    setIsTestingAPI(true);
    setApiResult(null);

    try {
      console.log("Testing API connection...");
      const response = await fetch("/api/seo/test");
      const data = await response.json();
      
      console.log("API test response:", data);
      setApiResult(data);
      
      if (data.status === "success") {
        toast({
          title: "API Test Successful",
          description: "SEO analysis service is working correctly",
        });
      } else {
        toast({
          title: "API Test Failed",
          description: data.message || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("API test error:", error);
      const errorResult = {
        status: "error",
        message: error instanceof Error ? error.message : "Network error"
      };
      setApiResult(errorResult);
      
      toast({
        title: "API Test Failed",
        description: errorResult.message,
        variant: "destructive",
      });
    } finally {
      setIsTestingAPI(false);
    }
  };

  const testPersistentSystem = async () => {
    setIsTestingPersistent(true);
    setPersistentResult(null);

    try {
      console.log("Testing persistent system...");
      const response = await fetch("/api/seo/debug/persistent");
      const data = await response.json();
      
      console.log("Persistent test response:", data);
      setPersistentResult(data);
      
      if (data.status === "success") {
        toast({
          title: "Persistent System Test Successful",
          description: "Persistent SEO analysis system is working correctly",
        });
      } else {
        toast({
          title: "Persistent System Test Failed",
          description: data.message || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Persistent test error:", error);
      const errorResult = {
        status: "error",
        message: error instanceof Error ? error.message : "Network error"
      };
      setPersistentResult(errorResult);
      
      toast({
        title: "Persistent System Test Failed",
        description: errorResult.message,
        variant: "destructive",
      });
    } finally {
      setIsTestingPersistent(false);
    }
  };

  const testFullAnalysis = async () => {
    try {
      console.log("Testing full analysis...");
      
      const response = await fetch("/api/seo/analyze-website", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: "https://example.com",
          mode: "website",
          enable_progress: true,
        }),
      });

      const data = await response.json();
      console.log("Full analysis response:", data);
      
      if (data.status === "started") {
        toast({
          title: "Analysis Started",
          description: `Analysis ID: ${data.analysis_id}`,
        });
      } else {
        toast({
          title: "Analysis Failed",
          description: data.error_message || "Unknown error",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Full analysis error:", error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Network error",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === "success" ? "default" : "destructive";
    return <Badge variant={variant}>{status}</Badge>;
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>SEO System Debug Panel</CardTitle>
          <p className="text-sm text-muted-foreground">
            Test the SEO analysis system components to identify issues
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API Connection Test */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">API Connection Test</h3>
              <Button
                onClick={testAPIConnection}
                disabled={isTestingAPI}
                size="sm"
              >
                {isTestingAPI && <Loader2 className="h-3 w-3 mr-2 animate-spin" />}
                Test API
              </Button>
            </div>
            
            {apiResult && (
              <div className="p-3 border rounded-lg bg-muted/50">
                <div className="flex items-center gap-2 mb-2">
                  {getStatusIcon(apiResult.status)}
                  {getStatusBadge(apiResult.status)}
                </div>
                <p className="text-sm">{apiResult.message}</p>
                {apiResult.test_url && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Test URL: {apiResult.test_url}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Persistent System Test */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Persistent System Test</h3>
              <Button
                onClick={testPersistentSystem}
                disabled={isTestingPersistent}
                size="sm"
              >
                {isTestingPersistent && <Loader2 className="h-3 w-3 mr-2 animate-spin" />}
                Test Persistent
              </Button>
            </div>
            
            {persistentResult && (
              <div className="p-3 border rounded-lg bg-muted/50">
                <div className="flex items-center gap-2 mb-2">
                  {getStatusIcon(persistentResult.status)}
                  {getStatusBadge(persistentResult.status)}
                </div>
                <p className="text-sm">{persistentResult.message}</p>
                {persistentResult.database && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Database: {persistentResult.database}
                  </p>
                )}
                {persistentResult.persistent_service && (
                  <p className="text-xs text-muted-foreground">
                    Service: {persistentResult.persistent_service}
                  </p>
                )}
                {persistentResult.active_analyses !== undefined && (
                  <p className="text-xs text-muted-foreground">
                    Active Analyses: {persistentResult.active_analyses}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Full Analysis Test */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Full Analysis Test</h3>
              <Button
                onClick={testFullAnalysis}
                size="sm"
                variant="outline"
              >
                Test Full Analysis
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              This will start a real analysis with example.com
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
