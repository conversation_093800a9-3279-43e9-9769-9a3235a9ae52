"""
API endpoints for replace background and relight functionality.
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from app.core.auth import verify_api_key
from app.schemas.replace_background import (
    ReplaceBackgroundRequest,
    FrontendReplaceBackgroundInitialResponse,
    FrontendReplaceBackgroundStatusResponse
)
from app.services.replace_background_service import (
    start_replace_background_stability,
    check_replace_background_status_stability
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/replace",
    response_model=FrontendReplaceBackgroundInitialResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Start replace background and relight operation",
    description="""
    Start replace background and relight operation using Stability AI v2beta API.

    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Aspect Ratio:** Between 1:2.5 and 2.5:1
    **Cost:** 8 credits per successful generation

    This endpoint starts an asynchronous process to replace the background and adjust lighting.
    Use the returned generation ID to poll for results using the status endpoint.

    **Required:**
    - subject_image: The image containing the subject
    - background_prompt OR background_reference: Description or reference image for the new background

    **Optional:**
    - foreground_prompt: Description of the subject to prevent background bleeding
    - negative_prompt: What NOT to include in the result
    - preserve_original_subject: How much to preserve the original subject (0-1)
    - light_source_direction: Direction of light (above, below, left, right)
    - light_source_strength: Strength of light source (0-1)
    - output_format: Output format (jpeg, png, webp)
    """
)
async def start_replace_background_endpoint(
    subject_image: UploadFile = File(..., description="Image containing the subject to process"),
    background_prompt: Optional[str] = Form(None, description="Description of the desired background"),
    background_reference: Optional[UploadFile] = File(None, description="Reference image for background style"),
    light_reference: Optional[UploadFile] = File(None, description="Reference image for lighting"),
    foreground_prompt: Optional[str] = Form(None, description="Description of the subject"),
    negative_prompt: Optional[str] = Form(None, description="What NOT to include in the result"),
    preserve_original_subject: Optional[float] = Form(0.6, description="How much to preserve original subject (0-1)"),
    original_background_depth: Optional[float] = Form(0.5, description="Background depth control (0-1)"),
    keep_original_background: Optional[bool] = Form(False, description="Whether to keep original background"),
    light_source_direction: Optional[str] = Form(None, description="Light direction (above, below, left, right)"),
    light_source_strength: Optional[float] = Form(0.3, description="Light strength (0-1)"),
    seed: Optional[int] = Form(0, description="Random seed (0 = random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)")
) -> FrontendReplaceBackgroundInitialResponse:
    """
    Start replace background and relight operation using Stability AI v2beta API.

    This endpoint starts an asynchronous process. Use the returned generation ID
    to poll for results using the status endpoint.
    """
    try:
        logger.info(f"Received replace background request for file: {subject_image.filename}")
        logger.info(f"Background prompt: {background_prompt}")
        logger.info(f"Foreground prompt: {foreground_prompt}")

        # Validar archivo de imagen
        if not subject_image.filename:
            raise HTTPException(status_code=400, detail="Subject image filename is required")

        # Validar que se proporcione background_prompt O background_reference
        if not background_prompt and not background_reference:
            raise HTTPException(
                status_code=400,
                detail="Either background_prompt or background_reference must be provided"
            )

        # Validar preserve_original_subject
        if preserve_original_subject is not None and (preserve_original_subject < 0 or preserve_original_subject > 1):
            raise HTTPException(
                status_code=400,
                detail="preserve_original_subject must be between 0 and 1"
            )

        # Validar original_background_depth
        if original_background_depth is not None and (original_background_depth < 0 or original_background_depth > 1):
            raise HTTPException(
                status_code=400,
                detail="original_background_depth must be between 0 and 1"
            )

        # Validar light_source_direction
        if light_source_direction and light_source_direction not in ["above", "below", "left", "right"]:
            raise HTTPException(
                status_code=400,
                detail="light_source_direction must be 'above', 'below', 'left', or 'right'"
            )

        # Validar light_source_strength
        if light_source_strength is not None and (light_source_strength < 0 or light_source_strength > 1):
            raise HTTPException(
                status_code=400,
                detail="light_source_strength must be between 0 and 1"
            )

        # Validar seed
        if seed is not None and (seed < 0 or seed > 4294967294):
            raise HTTPException(
                status_code=400,
                detail="seed must be between 0 and 4294967294"
            )

        # Validar output_format
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="output_format must be 'jpeg', 'png', or 'webp'"
            )

        # Crear request object
        replace_bg_request = ReplaceBackgroundRequest(
            background_prompt=background_prompt.strip() if background_prompt and background_prompt.strip() else None,
            foreground_prompt=foreground_prompt.strip() if foreground_prompt and foreground_prompt.strip() else None,
            negative_prompt=negative_prompt.strip() if negative_prompt and negative_prompt.strip() else None,
            preserve_original_subject=preserve_original_subject,
            original_background_depth=original_background_depth,
            keep_original_background=keep_original_background,
            light_source_direction=light_source_direction,
            light_source_strength=light_source_strength,
            seed=seed or 0,
            output_format=output_format or "png"
        )

        # Llamar al servicio de Stability AI
        service_response = await start_replace_background_stability(
            image_file=subject_image,
            background_reference_file=background_reference,
            light_reference_file=light_reference,
            request=replace_bg_request
        )

        logger.info(f"Replace background operation started successfully. Generation ID: {service_response.id}")

        return FrontendReplaceBackgroundInitialResponse(
            success=True,
            id=service_response.id,
            message="Replace background operation started successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error starting replace background operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error starting replace background operation: {str(e)}"
        )


@router.get(
    "/status/{generation_id}",
    response_model=FrontendReplaceBackgroundStatusResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Check replace background operation status",
    description="""
    Check the status of a replace background and relight operation.

    **Returns:**
    - 202: Operation still in progress
    - 200: Operation completed successfully with image data
    - 4xx/5xx: Operation failed with error details

    **Usage:**
    Poll this endpoint every 10 seconds until you receive a 200 response
    with the final image, or an error response.
    """
)
async def check_replace_background_status_endpoint(
    generation_id: str
) -> FrontendReplaceBackgroundStatusResponse:
    """
    Check the status of a replace background and relight operation.

    Returns the current status and final result if completed.
    """
    try:
        logger.info(f"Checking replace background status for generation ID: {generation_id}")

        # Validar generation_id
        if not generation_id or not generation_id.strip():
            raise HTTPException(status_code=400, detail="Generation ID is required")

        # Llamar al servicio de Stability AI
        try:
            service_response = await check_replace_background_status_stability(generation_id.strip())

            # Si llegamos aquí, la operación se completó exitosamente
            # Crear data URL para el frontend
            mime_type = "image/png"  # Por defecto PNG
            image_data_url = f"data:{mime_type};base64,{service_response.image}"

            logger.info(f"Replace background operation completed successfully. Result size: {len(image_data_url)} chars")

            return FrontendReplaceBackgroundStatusResponse(
                success=True,
                status="COMPLETED",
                image_url=image_data_url,
                seed=service_response.seed,
                finish_reason=service_response.finish_reason,
                metadata={
                    "generation_id": generation_id,
                    "finish_reason": service_response.finish_reason,
                    "seed": service_response.seed
                }
            )

        except HTTPException as http_exc:
            # Si es 202 (en progreso), devolver status apropiado
            if http_exc.status_code == 202:
                logger.info(f"Replace background operation still in progress for ID: {generation_id}")
                return FrontendReplaceBackgroundStatusResponse(
                    success=True,
                    status="IN_PROGRESS"
                )
            else:
                # Otros errores HTTP
                logger.error(f"Replace background operation failed for ID {generation_id}: {http_exc.detail}")
                return FrontendReplaceBackgroundStatusResponse(
                    success=False,
                    status="FAILED",
                    error=http_exc.detail
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error checking replace background status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error checking replace background status: {str(e)}"
        )
