"""
Content optimization utilities for social media posts.
"""

import re
from typing import Dict, Any


def optimize_content_for_platform(content: str, platform: str, char_limits: Dict[str, int]) -> str:
    """
    Optimize content for platform-specific character limits and formatting.
    
    Args:
        content: Raw content text
        platform: Target platform
        char_limits: Character limits for the platform
        
    Returns:
        Optimized content that fits platform requirements
    """
    optimal_limit = char_limits["optimal"]
    max_limit = char_limits["maximum"]
    
    # If content is within optimal range, return as-is
    if len(content) <= optimal_limit:
        return content
    
    # If content exceeds maximum, truncate intelligently
    if len(content) > max_limit:
        content = intelligent_truncate(content, max_limit - 10)  # Leave room for "..."
        content += "..."
    
    # If content is between optimal and max, try to optimize
    elif len(content) > optimal_limit:
        # Try to find natural break points
        optimized = find_natural_break(content, optimal_limit)
        if optimized:
            return optimized
    
    return content


def intelligent_truncate(text: str, max_length: int) -> str:
    """
    Intelligently truncate text at natural break points.
    
    Args:
        text: Text to truncate
        max_length: Maximum allowed length
        
    Returns:
        Truncated text at a natural break point
    """
    if len(text) <= max_length:
        return text
    
    # Try to break at sentence end
    sentences = text.split('. ')
    truncated = ""
    for sentence in sentences:
        if len(truncated + sentence + '. ') <= max_length:
            truncated += sentence + '. '
        else:
            break
    
    if truncated:
        return truncated.strip()
    
    # If no sentence break works, break at word boundary
    words = text.split()
    truncated = ""
    for word in words:
        if len(truncated + word + ' ') <= max_length:
            truncated += word + ' '
        else:
            break
    
    return truncated.strip()


def find_natural_break(text: str, target_length: int) -> str:
    """
    Find natural break points in text to optimize for target length.
    
    Args:
        text: Text to optimize
        target_length: Target character length
        
    Returns:
        Optimized text or None if no good break point found
    """
    # Look for natural break points near target length
    break_points = ['. ', '! ', '? ', '\n\n', '\n']
    
    for break_point in break_points:
        parts = text.split(break_point)
        current_length = 0
        result_parts = []
        
        for part in parts:
            if current_length + len(part) + len(break_point) <= target_length:
                result_parts.append(part)
                current_length += len(part) + len(break_point)
            else:
                break
        
        if result_parts and current_length > target_length * 0.8:  # At least 80% of target
            return break_point.join(result_parts)
    
    return None


def extract_hashtags(text: str) -> tuple:
    """
    Extract hashtags from text and return clean text and hashtags separately.
    
    Args:
        text: Text containing hashtags
        
    Returns:
        Tuple of (clean_text, hashtags_list)
    """
    hashtag_pattern = r'#\w+'
    hashtags = re.findall(hashtag_pattern, text)
    clean_text = re.sub(hashtag_pattern, '', text).strip()
    
    # Clean up extra spaces
    clean_text = ' '.join(clean_text.split())
    
    return clean_text, hashtags


def optimize_text_for_ideogram(text: str) -> str:
    """
    Optimize text for Ideogram image generation.
    Ensures text is concise and will render well in images.
    
    Args:
        text: Text to optimize
        
    Returns:
        Optimized text for image generation
    """
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    # Limit to reasonable length for image text (max 8 words typically)
    words = text.split()
    if len(words) > 8:
        text = ' '.join(words[:8])
    
    # Remove special characters that might not render well
    text = re.sub(r'[^\w\s\-\!\?\.\,]', '', text)
    
    # Ensure it's not empty
    if not text.strip():
        text = "Contenido Profesional"
    
    return text.strip()


def generate_strategic_fallback_content(content_type: str, visual_hook: str, brand_info: Dict[str, Any], platform: str) -> str:
    """
    Generate fallback content when AI generation fails.
    
    Args:
        content_type: Type of content (educational, entertainment, etc.)
        visual_hook: The visual hook text
        brand_info: Brand information
        platform: Target platform
        
    Returns:
        Fallback content appropriate for the content type
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")
    
    # Content templates based on type
    templates = {
        "educational": f"Descubre cómo {business_name} puede transformar tu experiencia en {industry}. Conocimiento que marca la diferencia. #Educación #{industry.replace(' ', '')}",
        
        "entertainment": f"¡Contenido que te va a encantar! {business_name} trae lo mejor de {industry} con un toque especial. #Entretenimiento #{industry.replace(' ', '')}",
        
        "lifestyle": f"Vive la experiencia {business_name}. Porque tu estilo de vida merece lo mejor en {industry}. #Lifestyle #{industry.replace(' ', '')}",
        
        "business": f"Estrategias profesionales de {business_name} para destacar en {industry}. Resultados que hablan por sí solos. #Negocios #{industry.replace(' ', '')}"
    }
    
    # Get template or use default
    template = templates.get(content_type, templates["educational"])
    
    # Optimize for platform
    from app.utils.platform_utils import get_platform_character_limits
    char_limits = get_platform_character_limits(platform)
    
    return optimize_content_for_platform(template, platform, char_limits)


def validate_content_quality(content: str, visual_hook: str) -> Dict[str, Any]:
    """
    Validate content quality and provide improvement suggestions.
    
    Args:
        content: Generated content
        visual_hook: Visual hook text
        
    Returns:
        Validation results with quality score and suggestions
    """
    issues = []
    score = 100
    
    # Check for duplication with visual hook
    if visual_hook.lower() in content.lower():
        issues.append("Content duplicates visual hook")
        score -= 20
    
    # Check length
    if len(content) < 50:
        issues.append("Content too short")
        score -= 15
    elif len(content) > 500:
        issues.append("Content too long for social media")
        score -= 10
    
    # Check for engagement elements
    if not any(char in content for char in ['?', '!', '#']):
        issues.append("Missing engagement elements (questions, exclamations, hashtags)")
        score -= 10
    
    # Check for call to action
    cta_indicators = ['descubre', 'aprende', 'síguenos', 'comparte', 'comenta', 'únete']
    if not any(indicator in content.lower() for indicator in cta_indicators):
        issues.append("Missing clear call to action")
        score -= 15
    
    return {
        "score": max(0, score),
        "issues": issues,
        "quality": "excellent" if score >= 90 else "good" if score >= 70 else "needs_improvement"
    }


def enhance_content_with_emojis(content: str, industry: str) -> str:
    """
    Add relevant emojis to content based on industry and context.
    
    Args:
        content: Content text
        industry: Business industry
        
    Returns:
        Content enhanced with appropriate emojis
    """
    # Industry-specific emoji mappings
    industry_emojis = {
        "fitness": ["💪", "🏋️", "🔥", "⚡", "🎯"],
        "food": ["🍽️", "👨‍🍳", "🌟", "😋", "🔥"],
        "beauty": ["✨", "💄", "🌟", "💅", "😍"],
        "business": ["📈", "💼", "🎯", "⚡", "🚀"],
        "technology": ["💻", "🚀", "⚡", "🔧", "📱"],
        "education": ["📚", "🎓", "💡", "🧠", "✨"],
        "travel": ["✈️", "🌍", "📸", "🗺️", "🌟"],
        "fashion": ["👗", "✨", "💫", "🌟", "👑"]
    }
    
    # Get relevant emojis for industry
    emojis = industry_emojis.get(industry.lower(), ["✨", "🌟", "💫", "🚀", "💡"])
    
    # Add emojis strategically (not overwhelming)
    if not any(emoji in content for emoji in emojis):
        # Add one emoji at the end if none present
        content += f" {emojis[0]}"
    
    return content
