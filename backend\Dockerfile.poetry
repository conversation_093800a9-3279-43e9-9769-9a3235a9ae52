# Poetry-based Dockerfile for Plataforma Backend
# Using multi-stage build for smaller, more secure final image

# ---- Build Stage ----
FROM python:3.11-slim AS builder

# Set working directory
WORKDIR /app

# Install Poetry and system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends gcc libpq-dev build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir poetry==2.1.2

# Configure Poetry - Don't create virtual environment in container
RUN poetry config virtualenvs.create false

# Copy pyproject.toml and poetry.lock
COPY pyproject.toml poetry.lock* ./

# Install dependencies only (no dev dependencies)
RUN poetry install --no-interaction --no-root --without dev

# ---- Runtime Stage ----
FROM python:3.11-slim

# Create non-root user for better security
RUN addgroup --system app && adduser --system --group app

# Set working directory
WORKDIR /app

# Install system dependencies only
RUN apt-get update \
    && apt-get install -y --no-install-recommends libpq5 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy installed dependencies from builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Copy application code
COPY . .

# Set permissions
RUN chown -R app:app /app

# Switch to non-root user
USER app

# Set the Python path to include the app directory
ENV PYTHONPATH=/app:$PYTHONPATH

# Set environment variables
ENV PORT=8000
ENV HOST=0.0.0.0
ENV PYTHONUNBUFFERED=1

# Expose the port
EXPOSE 8000

# Start the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1