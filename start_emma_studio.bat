@echo off
echo ========================================
echo Emma Studio - Complete Application Launcher
echo ========================================
echo.

echo Starting Emma Studio...
echo.
echo This will open two terminal windows:
echo 1. Backend Server (Port 8000)
echo 2. Frontend Development Server (Port 3002)
echo.

REM Start backend in new window
echo Starting Backend Server...
start "Emma Studio Backend" cmd /k "cd /d %~dp0 && start_backend.bat"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in new window
echo Starting Frontend Server...
start "Emma Studio Frontend" cmd /k "cd /d %~dp0\client && npm run dev"

echo.
echo ========================================
echo Emma Studio is starting up!
echo ========================================
echo.
echo Frontend: http://localhost:3002
echo Backend:  http://localhost:8000
echo API Proxy: http://localhost:3002/api
echo.
echo Both servers are running in separate windows.
echo Close those windows to stop the servers.
echo.
pause
