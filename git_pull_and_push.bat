@echo off
echo ========================================
echo Emma Studio Git Pull and Push
echo ========================================
echo.

REM Add Git to PATH for this session
set PATH=%PATH%;C:\Program Files\Git\bin

echo Checking Git status...
git status
if errorlevel 1 (
    echo ERROR: Git repository not found or not initialized
    pause
    exit /b 1
)

echo.
echo Current commit log:
git log --oneline -5

echo.
echo ========================================
echo IMPORTANT: Remote Repository Setup
echo ========================================
echo.
echo Before we can pull and push, you need to:
echo 1. Create a repository on GitHub/GitLab/etc.
echo 2. Add the remote origin
echo.
echo Example commands:
echo git remote add origin https://github.com/yourusername/emma-studio.git
echo git branch -M main
echo.

REM Check if remote origin exists
git remote -v >nul 2>&1
if errorlevel 1 (
    echo No remote repository configured.
    echo.
    set /p REMOTE_URL="Enter your remote repository URL (or press Enter to skip): "
    if not "!REMOTE_URL!"=="" (
        echo Adding remote origin...
        git remote add origin !REMOTE_URL!
        if errorlevel 1 (
            echo ERROR: Failed to add remote origin
            pause
            exit /b 1
        )
        echo Remote origin added successfully!
    ) else (
        echo Skipping remote setup. You can add it later with:
        echo git remote add origin [your-repository-url]
        pause
        exit /b 0
    )
) else (
    echo Remote repository found:
    git remote -v
)

echo.
echo Setting main branch...
git branch -M main
if errorlevel 1 (
    echo ERROR: Failed to set main branch
    pause
    exit /b 1
)

echo.
echo Attempting to pull from remote...
git pull origin main --allow-unrelated-histories
if errorlevel 1 (
    echo WARNING: Pull failed or no remote content found
    echo This is normal for a new repository
)

echo.
echo Pushing to remote repository...
git push -u origin main
if errorlevel 1 (
    echo ERROR: Failed to push to remote repository
    echo Make sure:
    echo 1. The remote repository exists
    echo 2. You have push permissions
    echo 3. Your authentication is set up correctly
    pause
    exit /b 1
)

echo.
echo ========================================
echo Git pull and push completed successfully!
echo ========================================
echo.
echo Your Emma Studio project has been:
echo - Committed locally with message "Setting windows"
echo - Pushed to the remote repository
echo.
pause
