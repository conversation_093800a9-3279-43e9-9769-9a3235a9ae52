#!/usr/bin/env python3
"""
Test rápido para verificar que el Creative Genius funciona sin loops infinitos.
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.post_generation_service import PostGenerationService


async def test_quick_generation():
    """Test rápido de generación de posts."""
    print("🚀 Test RÁPIDO - Creative Genius sin loops infinitos...")
    
    # Initialize Post Generation Service
    post_service = PostGenerationService()
    
    # Test case simple
    brand_info = {
        "businessName": "Wouf Suplementos",
        "industry": "suplementos para mascotas",
        "target_audience": "dueños de perros",
        "brandColor": "#3018ef"
    }
    
    design_config = {
        "template": "Educativo",
        "platform": "Instagram",
        "userTopic": "suplementos para perros"
    }
    
    generation_config = {
        "postCount": 1  # Solo 1 post para test rápido
    }
    
    print(f"\n🎯 Generando 1 post para Wouf Suplementos...")
    
    try:
        response = await post_service.generate_posts_batch(
            brand_info=brand_info,
            design_config=design_config,
            generation_config=generation_config
        )
        
        if response.success:
            print(f"✅ ÉXITO: Post generado!")
            
            post = response.posts[0]
            print(f"\n🎨 POST GENERADO:")
            print(f"   📝 Texto: {post.text}")
            print(f"   🖼️ Imagen: {'✅ SÍ' if post.image_url else '❌ NO'}")
            
            if post.image_url:
                print(f"   🔗 URL: {post.image_url}")
            
            # Verificar metadatos del Creative Genius
            if hasattr(post, 'metadata'):
                hook = post.metadata.get('visual_hook', 'N/A')
                viral_score = post.metadata.get('viral_score', 'N/A')
                creative_concept = post.metadata.get('creative_concept', 'N/A')
                
                print(f"\n   🧠 CREATIVE GENIUS:")
                print(f"      Hook: '{hook}'")
                print(f"      Viral score: {viral_score}")
                print(f"      Concepto: {creative_concept[:80]}...")
            
            # Verificar relevancia
            text_lower = post.text.lower()
            wouf_keywords = ["wouf", "perro", "suplemento", "mascota", "salud"]
            relevant = any(keyword in text_lower for keyword in wouf_keywords)
            
            if relevant:
                print(f"   ✅ TEXTO RELEVANTE: Menciona el producto")
            else:
                print(f"   ❌ TEXTO IRRELEVANTE: No menciona el producto")
            
            # Resultado final
            has_image = post.image_url is not None
            has_relevant_text = relevant
            
            if has_image and has_relevant_text:
                print(f"\n🎯 RESULTADO: ✅ POST COMPLETO CHINGÓN")
                print(f"   - Imagen generada con Ideogram ✅")
                print(f"   - Texto relevante al producto ✅")
                print(f"   - Creative Genius funcionando ✅")
            else:
                print(f"\n🎯 RESULTADO: ⚠️ POST INCOMPLETO")
                if not has_image:
                    print(f"   - Sin imagen ❌")
                if not has_relevant_text:
                    print(f"   - Texto irrelevante ❌")
                    
        else:
            print(f"❌ ERROR: {response.error}")
            
    except Exception as e:
        print(f"❌ EXCEPCIÓN: {e}")
    
    print(f"\n🎯 CONCLUSIÓN:")
    print(f"✅ Este test verifica que el Creative Genius no se quede en loop infinito")
    print(f"✅ Genera posts completos con imagen + texto")
    print(f"✅ El contenido es relevante al producto")


if __name__ == "__main__":
    asyncio.run(test_quick_generation())
