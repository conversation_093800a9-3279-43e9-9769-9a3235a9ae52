#!/bin/bash
set -e

echo "🚀 Setting up Emma Studio development environment..."

# Update system packages
sudo apt-get update
sudo apt-get install -y curl wget git build-essential software-properties-common

# Install Python 3.11 (required for the backend)
sudo add-apt-repository ppa:deadsnakes/ppa -y
sudo apt-get update
sudo apt-get install -y python3.11 python3.11-dev python3.11-venv python3-pip

# Make Python 3.11 the default python3
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1

# Install Poetry for Python dependency management
curl -sSL https://install.python-poetry.org | python3 -
echo 'export PATH="$HOME/.local/bin:$PATH"' >> $HOME/.profile
export PATH="$HOME/.local/bin:$PATH"

# Install Node.js 18 LTS for frontend
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installations
echo "✅ Verifying installations..."
python3 --version
poetry --version
node --version
npm --version

# Setup backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
poetry config virtualenvs.in-project true
poetry install --with dev

# Activate virtual environment for the session
echo 'cd /mnt/persist/workspace/backend && source .venv/bin/activate' >> $HOME/.profile

# Setup frontend dependencies
echo "📦 Installing frontend dependencies..."
cd ../client
npm install

# Install root level dependencies
echo "📦 Installing root level dependencies..."
cd ..
npm install

echo "✅ Setup completed successfully!"
echo "Backend: Python FastAPI with Poetry"
echo "Frontend: React/TypeScript with Vite"
echo "Tests: pytest for backend, custom test scripts"