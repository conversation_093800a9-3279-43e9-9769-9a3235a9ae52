/**
 * Final Comprehensive Image Display Test
 * This script tests the complete image loading flow with enhanced debugging
 * Run in browser console on Visual Complexity Analyzer page
 */

console.log('🧪 Final Image Display Test - Enhanced Version');

class FinalImageDisplayTest {
  constructor() {
    this.results = {};
    this.errors = [];
    this.debugLogs = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    this.debugLogs.push(logEntry);
    
    if (type === 'error') {
      console.error(message);
      this.errors.push(message);
    } else if (type === 'warn') {
      console.warn(message);
    } else {
      console.log(message);
    }
  }

  async init() {
    try {
      this.supabase = (await import('/src/lib/supabase.ts')).supabase;
      this.designAnalysisService = (await import('/src/services/designAnalysisService.ts')).designAnalysisService;
      this.log('✅ Modules imported successfully');
      return true;
    } catch (error) {
      this.log(`❌ Failed to import modules: ${error.message}`, 'error');
      return false;
    }
  }

  async testCompleteFlow() {
    this.log('🚀 Starting Complete Image Display Flow Test');
    
    // Step 1: Check authentication
    const authResult = await this.checkAuth();
    if (!authResult) return this.generateReport();

    // Step 2: Get a saved analysis with image
    const analysis = await this.getSavedAnalysisWithImage();
    if (!analysis) return this.generateReport();

    // Step 3: Test the service method directly
    await this.testServiceMethod(analysis.file_url);

    // Step 4: Test the complete UI flow
    await this.testUIFlow(analysis);

    return this.generateReport();
  }

  async checkAuth() {
    this.log('🔐 Checking authentication...');
    
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        this.log(`Authentication error: ${error.message}`, 'error');
        return false;
      }

      if (!session) {
        this.log('No active session', 'error');
        return false;
      }

      this.log(`✅ Authenticated as: ${session.user.email}`);
      this.results.auth = { success: true, userId: session.user.id };
      return true;
    } catch (error) {
      this.log(`Auth check failed: ${error.message}`, 'error');
      return false;
    }
  }

  async getSavedAnalysisWithImage() {
    this.log('📋 Getting saved analysis with image...');
    
    try {
      const { data: analyses, error } = await this.supabase
        .schema('api')
        .from('design_analyses')
        .select('*')
        .eq('user_id', this.results.auth.userId)
        .not('file_url', 'is', null)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        this.log(`Error fetching analyses: ${error.message}`, 'error');
        return null;
      }

      if (!analyses || analyses.length === 0) {
        this.log('No saved analyses with images found', 'warn');
        return null;
      }

      const analysis = analyses[0];
      this.log(`✅ Found analysis: ${analysis.id} - ${analysis.original_filename}`);
      this.results.analysis = analysis;
      return analysis;
    } catch (error) {
      this.log(`Failed to get saved analysis: ${error.message}`, 'error');
      return null;
    }
  }

  async testServiceMethod(filePath) {
    this.log('🛠️ Testing designAnalysisService.getImageUrl method...');
    
    try {
      this.log(`Testing with file path: ${filePath}`);
      
      const startTime = Date.now();
      const imageUrl = await this.designAnalysisService.getImageUrl(filePath);
      const endTime = Date.now();
      
      if (!imageUrl) {
        this.log('Service method returned null', 'error');
        this.results.serviceMethod = { success: false, error: 'Returned null' };
        return false;
      }

      this.log(`✅ Service method returned URL in ${endTime - startTime}ms`);
      this.log(`URL type: ${imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL'}`);
      this.log(`URL preview: ${imageUrl.substring(0, 100)}...`);

      // Test if the URL actually works
      const imageValid = await this.testImageUrl(imageUrl);
      
      this.results.serviceMethod = {
        success: true,
        url: imageUrl,
        type: imageUrl.startsWith('blob:') ? 'object' : 'http',
        responseTime: endTime - startTime,
        imageValid: imageValid
      };

      return imageValid;
    } catch (error) {
      this.log(`Service method failed: ${error.message}`, 'error');
      this.results.serviceMethod = { success: false, error: error.message };
      return false;
    }
  }

  async testImageUrl(url) {
    this.log('🖼️ Testing image URL validity...');
    
    return new Promise((resolve) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        this.log('Image load timeout (10s)', 'warn');
        resolve(false);
      }, 10000);

      img.onload = () => {
        clearTimeout(timeout);
        this.log(`✅ Image loaded successfully: ${img.naturalWidth}x${img.naturalHeight}`);
        resolve(true);
      };

      img.onerror = (error) => {
        clearTimeout(timeout);
        this.log(`❌ Image failed to load: ${error}`, 'error');
        resolve(false);
      };

      img.src = url;
    });
  }

  async testUIFlow(analysis) {
    this.log('⚛️ Testing complete UI flow...');
    
    try {
      // Check if we're on the correct page
      const analyzeTab = document.querySelector('[value="analyze"]');
      const historyTab = document.querySelector('[value="history"]');
      
      if (!analyzeTab || !historyTab) {
        this.log('Not on Visual Complexity Analyzer page', 'error');
        return false;
      }

      // Go to history tab
      this.log('📋 Switching to history tab...');
      historyTab.click();
      await this.wait(1000);

      // Find analysis cards
      const analysisCards = document.querySelectorAll('[class*="analysis-card"], [class*="AnalysisCard"]');
      this.log(`Found ${analysisCards.length} analysis cards`);

      if (analysisCards.length === 0) {
        this.log('No analysis cards found in UI', 'error');
        return false;
      }

      // Find the specific analysis card
      let targetCard = null;
      for (const card of analysisCards) {
        const cardText = card.textContent || '';
        if (cardText.includes(analysis.original_filename) || cardText.includes(analysis.id)) {
          targetCard = card;
          break;
        }
      }

      if (!targetCard) {
        this.log('Target analysis card not found, using first card', 'warn');
        targetCard = analysisCards[0];
      }

      // Click the card to load the analysis
      this.log('🔄 Loading analysis from history...');
      targetCard.click();
      await this.wait(3000); // Wait longer for loading

      // Check if we switched to analyze tab
      const analyzeTabActive = document.querySelector('[value="analyze"][data-state="active"], [value="analyze"].active');
      if (!analyzeTabActive) {
        this.log('Did not switch to analyze tab', 'error');
        return false;
      }

      this.log('✅ Switched to analyze tab');

      // Check if image is displayed
      await this.wait(2000); // Wait for image loading
      const previewImages = document.querySelectorAll('img[src*="blob:"], img[src*="http"]');
      
      this.log(`Found ${previewImages.length} preview images`);
      
      let imageDisplayed = false;
      for (const img of previewImages) {
        if (img.src && img.complete && img.naturalHeight > 0) {
          this.log(`✅ Image displayed successfully: ${img.naturalWidth}x${img.naturalHeight}`);
          this.log(`Image source: ${img.src.substring(0, 50)}...`);
          imageDisplayed = true;
          break;
        }
      }

      if (!imageDisplayed) {
        this.log('❌ No images displayed in UI', 'error');
        // Log all images for debugging
        previewImages.forEach((img, index) => {
          this.log(`Image ${index + 1}: src=${img.src.substring(0, 50)}..., complete=${img.complete}, naturalHeight=${img.naturalHeight}`);
        });
      }

      this.results.uiFlow = {
        success: imageDisplayed,
        cardsFound: analysisCards.length,
        imagesFound: previewImages.length,
        imageDisplayed: imageDisplayed
      };

      return imageDisplayed;
    } catch (error) {
      this.log(`UI flow test failed: ${error.message}`, 'error');
      this.results.uiFlow = { success: false, error: error.message };
      return false;
    }
  }

  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateReport() {
    console.log('\n' + '='.repeat(70));
    console.log('📊 FINAL IMAGE DISPLAY TEST REPORT');
    console.log('='.repeat(70));
    
    // Authentication
    console.log(`\n🔐 Authentication: ${this.results.auth?.success ? '✅ PASSED' : '❌ FAILED'}`);
    
    // Analysis Data
    console.log(`📋 Analysis Data: ${this.results.analysis ? '✅ FOUND' : '❌ NOT FOUND'}`);
    if (this.results.analysis) {
      console.log(`   - File: ${this.results.analysis.original_filename}`);
      console.log(`   - Path: ${this.results.analysis.file_url}`);
    }
    
    // Service Method
    console.log(`🛠️ Service Method: ${this.results.serviceMethod?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.serviceMethod?.success) {
      console.log(`   - Type: ${this.results.serviceMethod.type}`);
      console.log(`   - Response Time: ${this.results.serviceMethod.responseTime}ms`);
      console.log(`   - Image Valid: ${this.results.serviceMethod.imageValid ? '✅' : '❌'}`);
    }
    
    // UI Flow
    console.log(`⚛️ UI Flow: ${this.results.uiFlow?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.uiFlow) {
      console.log(`   - Cards Found: ${this.results.uiFlow.cardsFound}`);
      console.log(`   - Images Found: ${this.results.uiFlow.imagesFound}`);
      console.log(`   - Image Displayed: ${this.results.uiFlow.imageDisplayed ? '✅' : '❌'}`);
    }
    
    // Overall Result
    const overallSuccess = this.results.auth?.success && 
                          this.results.analysis && 
                          this.results.serviceMethod?.success && 
                          this.results.serviceMethod?.imageValid &&
                          this.results.uiFlow?.success;
    
    console.log(`\n🎯 OVERALL RESULT: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILURE'}`);
    
    // Errors
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (overallSuccess) {
      console.log('   🎉 Image display is working correctly!');
    } else {
      if (!this.results.auth?.success) {
        console.log('   🔐 Check authentication - user must be logged in');
      }
      if (!this.results.analysis) {
        console.log('   📋 Upload and save an analysis first to test loading');
      }
      if (!this.results.serviceMethod?.success) {
        console.log('   🛠️ Check Supabase Storage configuration and permissions');
      }
      if (!this.results.serviceMethod?.imageValid) {
        console.log('   🖼️ Check image URL generation and accessibility');
      }
      if (!this.results.uiFlow?.success) {
        console.log('   ⚛️ Check React component state management and rendering');
      }
    }
    
    console.log('\n📝 Debug logs available in: finalTest.debugLogs');
    console.log('='.repeat(70));
    
    return {
      success: overallSuccess,
      results: this.results,
      errors: this.errors,
      debugLogs: this.debugLogs
    };
  }
}

// Create global instance
window.finalTest = new FinalImageDisplayTest();

console.log('🔧 Final Image Display Test loaded');
console.log('📝 Run: finalTest.testCompleteFlow() to start comprehensive testing');
