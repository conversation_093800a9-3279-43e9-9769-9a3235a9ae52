# Emma Studio Backend Setup Script for Windows
# This script helps set up the development environment for Emma Studio backend

# Function to check if a command exists
function Test-Command {
    param (
        [string]$Command
    )
    
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

Write-Host "=== Emma Studio Backend Setup ===" -ForegroundColor Green

# Check if Poetry is installed
if (-not (Test-Command "poetry")) {
    Write-Host "Poetry not found. Installing Poetry..." -ForegroundColor Yellow
    
    # Install Poetry using PowerShell
    (Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -
    
    # Add Poetry to PATH for the current session
    $env:PATH += ";$env:USERPROFILE\AppData\Roaming\Python\Scripts"
    
    Write-Host "Poetry installed successfully!" -ForegroundColor Green
} else {
    Write-Host "Poetry is already installed." -ForegroundColor Green
}

# Check Python version
$pythonVersion = (python --version).Split(" ")[1]
$pythonMajor = [int]($pythonVersion.Split(".")[0])
$pythonMinor = [int]($pythonVersion.Split(".")[1])

Write-Host "Detected Python version: $pythonVersion" -ForegroundColor Green

if ($pythonMajor -lt 3 -or ($pythonMajor -eq 3 -and $pythonMinor -lt 10)) {
    Write-Host "Error: Python 3.10 or higher is required." -ForegroundColor Red
    Write-Host "Please install Python 3.10+ and try again." -ForegroundColor Yellow
    exit 1
}

if ($pythonMajor -eq 3 -and $pythonMinor -gt 12) {
    Write-Host "Warning: Python 3.13+ detected. This project is tested with Python 3.10-3.12." -ForegroundColor Yellow
    Write-Host "Some dependencies may not work correctly with Python 3.13+." -ForegroundColor Yellow
}

# Configure Poetry to create virtual environment in the project directory
Write-Host "Configuring Poetry..." -ForegroundColor Green
poetry config virtualenvs.in-project true

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Green
poetry install

# Create .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Host "Creating .env file..." -ForegroundColor Yellow
    
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
    } else {
        @"
# Emma Studio Environment Variables
ENVIRONMENT=development
LOG_LEVEL=DEBUG
"@ | Out-File -FilePath ".env" -Encoding utf8
    }
    
    Write-Host ".env file created. Please update it with your configuration." -ForegroundColor Green
} else {
    Write-Host ".env file already exists." -ForegroundColor Green
}

# Initialize the database
Write-Host "Initializing database..." -ForegroundColor Green
poetry run alembic upgrade head

Write-Host "=== Setup Complete! ===" -ForegroundColor Green
Write-Host "To activate the virtual environment, run:" -ForegroundColor Green
Write-Host "poetry shell" -ForegroundColor Yellow
Write-Host "To run the application, use:" -ForegroundColor Green
Write-Host "poetry run uvicorn app.main:app --reload" -ForegroundColor Yellow
Write-Host "To run tests:" -ForegroundColor Green
Write-Host "poetry run pytest" -ForegroundColor Yellow
