"""
Custom Agent System Example
Demonstrates how to use the custom agent system in Emma Studio
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the agent system
# Use relative imports since we're inside the backend package
from ..agents import (
    BaseAgent,
    AgentContext,
    AgentAction,
    ActionResult,
    ContextType,
    AgentOrchestrator,
    TaskStatus,
    TaskPriority,
    AgentTask
)
from ..agents.specialized import EmmaAgent, SEOAgent


class SimpleLLMProvider:
    """Simple LLM provider for demonstration purposes."""

    async def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from a prompt."""
        logger.info(f"Generating text for prompt: {prompt[:100]}...")

        # In a real implementation, this would call an actual LLM
        return f"This is a simulated response to: {prompt[:50]}..."


async def run_example():
    """Run the example."""
    logger.info("Starting custom agent example")

    # Create LLM provider
    llm_provider = SimpleLLMProvider()

    # Create agents
    emma = EmmaAgent("emma", "<PERSON>", llm_provider)
    seo_agent = SEOAgent("seo", "SEO Specialist", llm_provider)

    # Create orchestrator
    orchestrator = AgentOrchestrator("Emma Orchestrator")

    # Register agents
    orchestrator.register_agent(emma)
    orchestrator.register_agent(seo_agent)

    # Create a workflow
    workflow = orchestrator.create_workflow(
        name="Content Creation Workflow",
        description="Create and optimize content for a blog post",
        initial_query="Create a blog post about artificial intelligence",
        priority=TaskPriority.MEDIUM
    )

    # Create tasks
    content_task = orchestrator.create_task(
        description="Create a blog post about artificial intelligence",
        priority=TaskPriority.HIGH,
        assigned_to=emma.id
    )

    seo_task = orchestrator.create_task(
        description="Optimize the blog post for SEO",
        priority=TaskPriority.MEDIUM,
        assigned_to=seo_agent.id,
        dependencies=[content_task.id]
    )

    # Add tasks to workflow
    orchestrator.add_task_to_workflow(workflow.id, content_task)
    orchestrator.add_task_to_workflow(workflow.id, seo_task)

    # Execute workflow
    logger.info(f"Executing workflow: {workflow.name}")
    result = await orchestrator.execute_workflow(workflow.id)

    # Print results
    logger.info(f"Workflow completed with status: {result.status}")
    logger.info(f"Workflow execution time: {result.end_time - result.start_time}ms")

    for task_result in result.task_results:
        logger.info(f"Task {task_result.task_id} completed with status: {task_result.status}")
        if task_result.data:
            logger.info(f"Task result: {task_result.data}")
        if task_result.error:
            logger.error(f"Task error: {task_result.error}")


async def run_direct_agent_example():
    """Run a direct agent example without the orchestrator."""
    logger.info("Starting direct agent example")

    # Create LLM provider
    llm_provider = SimpleLLMProvider()

    # Create Emma agent
    emma = EmmaAgent("emma", "Emma", llm_provider)

    # Create a task
    task = AgentTask(
        id="task-1",
        description="Create a marketing campaign for a new smartphone",
        priority=5,
        status="pending"
    )

    # Create context
    context = AgentContext(
        context_type=ContextType.TASK,
        data={"task": task}
    )

    # Get next action
    logger.info("Getting next action")
    action = await emma.get_next_action(context)
    logger.info(f"Next action: {action.type}")

    # Execute action
    logger.info("Executing action")
    result = await emma.execute_action(action)
    logger.info(f"Action result: {result.success}")

    if result.result:
        logger.info(f"Result: {result.result}")
    if result.error:
        logger.error(f"Error: {result.error}")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(run_example())
    asyncio.run(run_direct_agent_example())
