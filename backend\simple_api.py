"""
Enhanced API for Emma Studio Backend

This module provides a simplified but functional API for the Emma Studio backend
when there are issues with CrewAI integration. It implements the essential endpoints
needed by the frontend to function properly.
"""
import logging
import os
import sys
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager
from pydantic import BaseModel, Field

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("backend_error.log")
    ]
)
logger = logging.getLogger(__name__)

# Try to import FastAPI
try:
    from fastapi import FastAPI, Request, HTTPException, Query, Body, Path, Depends
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse
    from fastapi.openapi.docs import get_swagger_ui_html
    from fastapi.openapi.utils import get_openapi
except ImportError as e:
    logger.error(f"Error importing FastAPI: {e}")
    print("ERROR: FastAPI could not be imported. Please install the dependencies:")
    print("pip install fastapi uvicorn")
    sys.exit(1)

# Define Pydantic models for request/response
class GenerateContentRequest(BaseModel):
    prompt: str
    user_id: Optional[str] = None
    mode: str = "copy"

class GenerateContentResponse(BaseModel):
    request_id: str
    content: str
    status: str = "success"
    reasoning_trace: Optional[Dict[str, Any]] = None

class ImageGenerationRequest(BaseModel):
    prompt: str
    style: str = "realistic"
    width: int = 1024
    height: int = 1024

class ImageGenerationResponse(BaseModel):
    request_id: str
    image_url: str
    status: str = "success"

class ErrorResponse(BaseModel):
    status: str = "error"
    error: Dict[str, Any]

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Enhanced API starting...")
    yield
    logger.info("Enhanced API shutting down...")

# Create the FastAPI application
app = FastAPI(
    title="Emma Studio API",
    description="Enhanced API for Emma Studio backend with limited functionality",
    version="1.0.0",
    docs_url="/api/v1/docs",
    openapi_url="/api/v1/openapi.json",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Main entry point
@app.get("/")
async def root():
    return {
        "status": "warning",
        "message": "Emma Studio API running in limited mode due to CrewAI configuration issues",
        "docs_url": "/api/v1/docs"
    }

# Health check endpoint
@app.get("/api/v1/health")
async def health_check():
    return {
        "status": "ok",
        "details": "API is running in limited mode",
        "warnings": [
            "CrewAI integration is disabled",
            "Some features may not be available"
        ]
    }

# Content generation endpoints
@app.post("/api/v1/content/generate", response_model=GenerateContentResponse)
async def generate_content(request: GenerateContentRequest):
    """
    Generate content based on the provided prompt.
    This is a simplified implementation that returns mock content.
    """
    request_id = str(uuid.uuid4())
    logger.info(f"Content generation request: {request_id}")

    # Create a simple mock response
    content = f"Generated content for prompt: '{request.prompt}'\n\n"
    content += "This is a placeholder response since CrewAI integration is currently disabled."

    # Create a simple mock reasoning trace
    reasoning_trace = {
        "steps": [
            {
                "step_id": 1,
                "agent": "Content Generator",
                "type": "message",
                "content": f"Analyzing prompt: {request.prompt}",
                "timestamp": datetime.now().timestamp()
            },
            {
                "step_id": 2,
                "agent": "Content Generator",
                "type": "message",
                "content": "Generating response based on prompt analysis",
                "timestamp": datetime.now().timestamp()
            }
        ],
        "metadata": {
            "request_id": request_id,
            "mode": request.mode,
            "timestamp": datetime.now().isoformat()
        }
    }

    return GenerateContentResponse(
        request_id=request_id,
        content=content,
        reasoning_trace=reasoning_trace
    )

@app.get("/api/v1/content/history")
async def get_content_history():
    """
    Get content generation history.
    This is a simplified implementation that returns mock history.
    """
    return {
        "status": "success",
        "history": [
            {
                "request_id": str(uuid.uuid4()),
                "prompt": "Sample prompt 1",
                "timestamp": (datetime.now().timestamp() - 3600),
                "mode": "copy"
            },
            {
                "request_id": str(uuid.uuid4()),
                "prompt": "Sample prompt 2",
                "timestamp": datetime.now().timestamp(),
                "mode": "copy"
            }
        ]
    }

# Image generation endpoints
@app.post("/api/v1/images/generate", response_model=ImageGenerationResponse)
async def generate_image(request: ImageGenerationRequest):
    """
    Generate an image based on the provided prompt.
    This is a simplified implementation that returns a mock image URL.
    """
    request_id = str(uuid.uuid4())
    logger.info(f"Image generation request: {request_id}")

    # In a real implementation, this would call an image generation service
    # For now, we'll return a placeholder image URL
    image_url = f"https://via.placeholder.com/{request.width}x{request.height}.png?text=Generated+Image"

    return ImageGenerationResponse(
        request_id=request_id,
        image_url=image_url
    )

# Crew endpoints
@app.post("/api/v1/crew/run")
async def run_crew(request: Dict[str, Any] = Body(...)):
    """
    Run a crew of AI agents.
    This is a simplified implementation that returns a mock response.
    """
    request_id = str(uuid.uuid4())
    logger.info(f"Crew run request: {request_id}")

    # Create a simple mock response
    return {
        "status": "success",
        "result": "This is a placeholder response since CrewAI integration is currently disabled.",
        "metadata": {
            "request_id": request_id,
            "execution_time_seconds": 0.5,
            "crew_id": request.get("crew_id", "default")
        }
    }

@app.post("/api/v1/crew/chat")
async def chat_with_agent(request: Dict[str, Any] = Body(...)):
    """
    Chat with a specific agent.
    This is a simplified implementation that returns a mock response.
    """
    return {
        "status": "success",
        "response": "Hello! I'm a simulated agent. CrewAI integration is currently disabled, so I can only provide this placeholder response.",
        "agent_id": request.get("agent_id", "default")
    }

@app.get("/api/v1/external/status")
async def check_external_services():
    """
    Check the status of external services.
    This is a simplified implementation that returns mock statuses.
    """
    return {
        "status": "success",
        "services": {
            "gemini": True,
            "stability": False,
            "elevenlabs": True
        }
    }

# Error handling for 404 Not Found
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=404,
        content={
            "status": "error",
            "error": {
                "code": "not_found",
                "message": "The requested resource was not found",
                "path": request.url.path
            }
        }
    )

# Error handling for 500 Internal Server Error
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "error": {
                "code": "internal_server_error",
                "message": "An internal server error occurred",
                "details": str(exc) if os.getenv("DEBUG") == "true" else None
            }
        }
    )

if __name__ == "__main__":
    import uvicorn
    try:
        print("\n==== STARTING EMMA STUDIO ENHANCED API ====")
        print("This API provides essential functionality while CrewAI integration is being fixed")
        print("API documentation available at: http://localhost:8000/api/v1/docs")
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    except Exception as e:
        logger.error(f"Error starting the API: {e}", exc_info=True)
        sys.exit(1)