"""
GPT Reference Generator Module
Generates reference responses using AI to understand how LLMs would respond
"""

import logging
import asyncio
import re
from typing import Dict, Any, List

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class GPTReferenceGenerator:
    """Generates GPT reference responses using Gemini as a proxy."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ GPT Reference Generator - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. GPT reference generation will be limited.")
    
    async def get_gpt_reference_response(self, topic: str, language: str = "es") -> Dict[str, Any]:
        """
        Get GPT reference response using Gemini as a proxy.
        
        Args:
            topic: Topic to generate reference response for
            language: Target language for the response
            
        Returns:
            GPT reference response with analysis
        """
        try:
            if not self.gemini_model:
                return {"status": "unavailable", "message": "Gemini AI not configured"}
            
            prompt = self._build_gpt_reference_prompt(topic, language)
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            gpt_response_text = response.text.strip()
            
            # Analyze the response structure
            response_analysis = await self._analyze_gpt_response_structure(gpt_response_text, topic)
            
            # Extract key information
            key_phrases = self._extract_key_phrases(gpt_response_text)
            response_tone = self._analyze_response_tone(gpt_response_text)
            citability_analysis = self._analyze_citability(gpt_response_text)
            
            logger.info(f"✅ GPT reference response generated for '{topic}'")
            return {
                "status": "success",
                "topic": topic,
                "response_text": gpt_response_text,
                "response_length": len(gpt_response_text),
                "word_count": len(gpt_response_text.split()),
                "structure_analysis": response_analysis,
                "key_phrases": key_phrases,
                "response_tone": response_tone,
                "citability_analysis": citability_analysis,
                "llm_patterns": self._identify_llm_patterns(gpt_response_text)
            }
            
        except Exception as e:
            logger.error(f"❌ GPT reference response failed: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def _build_gpt_reference_prompt(self, topic: str, language: str) -> str:
        """Build the prompt for GPT reference response generation."""
        return f"""
        Actúa como GPT-4 y responde a la siguiente consulta como lo haría un modelo de lenguaje entrenado con datos hasta 2024: "{topic}"
        
        Proporciona una respuesta completa y estructurada que incluya:
        1. Definición o explicación principal
        2. Puntos clave importantes
        3. Ejemplos relevantes
        4. Consideraciones adicionales
        5. Información práctica y útil
        
        Responde en español de manera informativa y neutral, como lo haría un asistente de IA.
        Usa un tono profesional pero accesible, y estructura la información de manera clara y lógica.
        """
    
    async def _analyze_gpt_response_structure(self, response_text: str, topic: str) -> Dict[str, Any]:
        """Analyze the structure of GPT response."""
        try:
            paragraphs = [p.strip() for p in response_text.split('\n\n') if p.strip()]
            sentences = [s.strip() for s in response_text.split('.') if s.strip()]
            
            # Analyze structure patterns
            has_numbered_list = bool(re.search(r'\d+\.', response_text))
            has_bullet_points = bool(re.search(r'[•\-\*]', response_text))
            has_headers = bool(re.search(r'^[A-Z][^.]*:$', response_text, re.MULTILINE))
            
            # Check for typical LLM response patterns
            starts_with_topic = topic.lower() in paragraphs[0].lower() if paragraphs else False
            has_conclusion = len(paragraphs) > 2 and any(
                word in paragraphs[-1].lower() 
                for word in ['conclusión', 'resumen', 'importante', 'recordar']
            )
            
            return {
                "paragraph_count": len(paragraphs),
                "sentence_count": len(sentences),
                "avg_paragraph_length": sum(len(p) for p in paragraphs) / len(paragraphs) if paragraphs else 0,
                "avg_sentence_length": sum(len(s.split()) for s in sentences) / len(sentences) if sentences else 0,
                "has_introduction": starts_with_topic,
                "has_examples": any('ejemplo' in p.lower() or 'por ejemplo' in p.lower() for p in paragraphs),
                "has_conclusion": has_conclusion,
                "has_numbered_list": has_numbered_list,
                "has_bullet_points": has_bullet_points,
                "has_headers": has_headers,
                "structure_type": self._classify_structure_type(paragraphs, has_numbered_list, has_bullet_points)
            }
        except Exception as e:
            logger.error(f"❌ Response structure analysis failed: {str(e)}")
            return {"error": "Could not analyze structure"}
    
    def _classify_structure_type(self, paragraphs: List[str], has_numbered_list: bool, has_bullet_points: bool) -> str:
        """Classify the type of response structure."""
        if has_numbered_list:
            return "numbered_list"
        elif has_bullet_points:
            return "bullet_points"
        elif len(paragraphs) >= 4:
            return "detailed_explanation"
        elif len(paragraphs) >= 2:
            return "structured"
        else:
            return "simple"
    
    def _extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases from text that are likely to be cited."""
        try:
            sentences = text.split('.')
            key_phrases = []
            
            for sentence in sentences:
                sentence = sentence.strip()
                # Look for sentences that are likely to be cited
                if (30 <= len(sentence) <= 150 and 
                    any(indicator in sentence.lower() for indicator in [
                        'es importante', 'clave', 'fundamental', 'esencial', 'principal',
                        'se define como', 'consiste en', 'significa que', 'es un',
                        'los estudios muestran', 'la investigación indica', 'según'
                    ])):
                    key_phrases.append(sentence)
            
            return key_phrases[:5]  # Return top 5 key phrases
        except Exception as e:
            logger.error(f"❌ Key phrase extraction failed: {str(e)}")
            return []
    
    def _analyze_response_tone(self, text: str) -> Dict[str, Any]:
        """Analyze the tone of the response."""
        try:
            text_lower = text.lower()
            
            # Define tone indicators
            formal_indicators = ['debe', 'es importante', 'se recomienda', 'es necesario', 'conviene']
            informal_indicators = ['puedes', 'podrías', 'es genial', 'está bien', 'vale la pena']
            technical_indicators = ['proceso', 'método', 'técnica', 'procedimiento', 'mecanismo']
            conversational_indicators = ['imagina', 'piensa en', 'por ejemplo', 'digamos que']
            
            # Count indicators
            formal_count = sum(1 for indicator in formal_indicators if indicator in text_lower)
            informal_count = sum(1 for indicator in informal_indicators if indicator in text_lower)
            technical_count = sum(1 for indicator in technical_indicators if indicator in text_lower)
            conversational_count = sum(1 for indicator in conversational_indicators if indicator in text_lower)
            
            # Determine primary tone
            tone_scores = {
                "formal": formal_count,
                "informal": informal_count,
                "technical": technical_count,
                "conversational": conversational_count
            }
            
            primary_tone = max(tone_scores, key=tone_scores.get) if any(tone_scores.values()) else "neutral"
            
            return {
                "primary_tone": primary_tone,
                "tone_scores": tone_scores,
                "tone_confidence": max(tone_scores.values()) / sum(tone_scores.values()) if sum(tone_scores.values()) > 0 else 0,
                "is_balanced": len([score for score in tone_scores.values() if score > 0]) >= 2
            }
        except Exception as e:
            logger.error(f"❌ Tone analysis failed: {str(e)}")
            return {"primary_tone": "unknown", "error": str(e)}
    
    def _analyze_citability(self, text: str) -> Dict[str, Any]:
        """Analyze how citable the response is for other AI models."""
        try:
            sentences = text.split('.')
            
            # Count different types of citable content
            definitions = sum(1 for s in sentences if any(
                phrase in s.lower() for phrase in ['se define como', 'es un', 'consiste en', 'significa']
            ))
            
            facts = sum(1 for s in sentences if any(
                phrase in s.lower() for phrase in ['según', 'los datos', 'la investigación', 'estudios']
            ))
            
            statistics = len(re.findall(r'\d+%|\d+\.\d+%|\d+ de cada \d+', text))
            
            clear_statements = sum(1 for s in sentences if (
                20 <= len(s.strip()) <= 100 and
                not s.strip().startswith(('Por ejemplo', 'Como', 'Si', 'Cuando'))
            ))
            
            citability_score = min((definitions * 3 + facts * 2 + statistics * 2 + clear_statements) / 10, 10)
            
            return {
                "citability_score": citability_score,
                "definitions_count": definitions,
                "facts_count": facts,
                "statistics_count": statistics,
                "clear_statements_count": clear_statements,
                "citability_level": "high" if citability_score >= 7 else "medium" if citability_score >= 4 else "low"
            }
        except Exception as e:
            logger.error(f"❌ Citability analysis failed: {str(e)}")
            return {"citability_score": 0, "error": str(e)}
    
    def _identify_llm_patterns(self, text: str) -> Dict[str, Any]:
        """Identify patterns typical of LLM responses."""
        try:
            patterns = {
                "starts_with_definition": text.strip().lower().startswith(('el ', 'la ', 'los ', 'las ', 'un ', 'una ')),
                "uses_transition_phrases": any(phrase in text.lower() for phrase in [
                    'por otro lado', 'además', 'sin embargo', 'por lo tanto', 'en consecuencia'
                ]),
                "includes_examples": 'ejemplo' in text.lower() or 'por ejemplo' in text.lower(),
                "balanced_structure": len(text.split('\n\n')) >= 3,
                "uses_qualifiers": any(qualifier in text.lower() for qualifier in [
                    'generalmente', 'típicamente', 'a menudo', 'puede', 'podría', 'suele'
                ]),
                "includes_caveats": any(caveat in text.lower() for caveat in [
                    'sin embargo', 'no obstante', 'aunque', 'pero', 'es importante notar'
                ]),
                "comprehensive_coverage": len(text.split()) > 200,
                "neutral_tone": not any(opinion in text.lower() for opinion in [
                    'creo que', 'pienso que', 'en mi opinión', 'personalmente'
                ])
            }
            
            # Calculate LLM-likeness score
            llm_score = sum(patterns.values()) / len(patterns)
            
            return {
                "patterns": patterns,
                "llm_likeness_score": llm_score,
                "llm_confidence": "high" if llm_score >= 0.7 else "medium" if llm_score >= 0.5 else "low"
            }
        except Exception as e:
            logger.error(f"❌ LLM pattern identification failed: {str(e)}")
            return {"patterns": {}, "llm_likeness_score": 0, "error": str(e)}
