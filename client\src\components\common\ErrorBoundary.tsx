import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallbackComponent?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to an error reporting service
    console.error('Uncaught error:', error, errorInfo);
    
    // Optional custom error handling
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI or default
      const FallbackUI = this.props.fallbackComponent || (
        <div className="flex flex-col items-center justify-center min-h-screen bg-red-50 p-4">
          <AlertTriangle className="w-16 h-16 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-red-700 mb-2">Something went wrong</h1>
          <p className="text-red-600 mb-4">
            {this.state.error?.message || 'An unexpected error occurred'}
          </p>
          <Button 
            variant="destructive" 
            onClick={this.handleReset}
            className="mb-4"
          >
            Try Again
          </Button>
          <details className="text-xs text-gray-500 max-w-md">
            <summary>Error Details</summary>
            <pre className="bg-red-100 p-2 rounded mt-2 overflow-auto">
              {this.state.error?.stack}
            </pre>
          </details>
        </div>
      );

      return FallbackUI;
    }

    return this.props.children;
  }
}

// Utility hook for programmatic error handling
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((err: Error | string) => {
    const processedError = err instanceof Error 
      ? err 
      : new Error(err);
    
    setError(processedError);
    console.error('Handled error:', processedError);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
};

// Higher-order component for error handling
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>, 
  fallbackComponent?: ReactNode
) => {
  return (props: P) => (
    <ErrorBoundary fallbackComponent={fallbackComponent}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );
};
