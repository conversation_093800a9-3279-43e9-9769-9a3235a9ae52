#!/usr/bin/env python3
"""
Create SEO GPT Optimizer tables directly using SQLAlchemy
This script creates the necessary database tables for the SEO & GPT Optimizer™
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import engine
from app.db.seo_gpt_models import Base
from app.db.models import Base as MainBase

def create_tables():
    """Create all SEO GPT Optimizer tables."""
    try:
        print("🗄️  Creating SEO & GPT Optimizer™ database tables...")
        
        # Create all tables defined in both Base classes
        MainBase.metadata.create_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        
        print("✅ Database tables created successfully!")
        print("\nCreated tables:")
        print("- seo_gpt_projects")
        print("- content_analyses") 
        print("- gpt_rank_history")
        print("- keyword_research")
        print("\nThe SEO & GPT Optimizer™ is now ready to use!")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create database tables: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_tables()
    sys.exit(0 if success else 1)
