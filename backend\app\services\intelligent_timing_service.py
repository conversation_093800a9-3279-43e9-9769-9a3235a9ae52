"""Service for intelligent timing analysis and optimization."""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import calendar

logger = logging.getLogger(__name__)


class IntelligentTimingService:
    """Service for calculating optimal contact timing based on multiple factors."""

    def __init__(self):
        """Initialize the intelligent timing service."""
        self.industry_patterns = self._load_industry_patterns()
        self.role_patterns = self._load_role_patterns()
        self.seasonal_patterns = self._load_seasonal_patterns()

    def calculate_optimal_timing(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any],
        current_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Calculate optimal contact timing based on persona characteristics.
        
        Args:
            persona_data: Complete buyer persona information
            product_info: Product/service details
            current_date: Current date for calculations (defaults to now)
            
        Returns:
            Comprehensive timing analysis and recommendations
        """
        if current_date is None:
            current_date = datetime.now()

        try:
            # Extract persona characteristics
            industry = persona_data.get('job', {}).get('industry', 'Technology')
            job_title = persona_data.get('job', {}).get('title', 'Manager')
            age = persona_data.get('age', 35)
            location = persona_data.get('location', 'España')
            company_size = persona_data.get('job', {}).get('company_size', 'Medium')

            # Calculate different timing aspects
            daily_timing = self._calculate_daily_timing(job_title, industry, age)
            weekly_timing = self._calculate_weekly_timing(job_title, industry)
            monthly_timing = self._calculate_monthly_timing(industry, company_size)
            seasonal_timing = self._calculate_seasonal_timing(industry, location)
            follow_up_strategy = self._calculate_follow_up_strategy(persona_data, product_info)

            return {
                "status": "success",
                "timing_analysis": {
                    "daily_patterns": daily_timing,
                    "weekly_patterns": weekly_timing,
                    "monthly_patterns": monthly_timing,
                    "seasonal_patterns": seasonal_timing,
                    "follow_up_strategy": follow_up_strategy,
                    "next_optimal_contact": self._get_next_optimal_contact(
                        daily_timing, weekly_timing, current_date
                    ),
                    "timing_confidence": self._calculate_timing_confidence(
                        industry, job_title, location
                    )
                },
                "generated_at": current_date.isoformat()
            }

        except Exception as e:
            logger.error(f"Error calculating optimal timing: {e}")
            return {
                "status": "error",
                "error_message": f"Failed to calculate timing: {str(e)}"
            }

    def _calculate_daily_timing(self, job_title: str, industry: str, age: int) -> Dict[str, Any]:
        """Calculate optimal daily contact windows."""
        
        # Base patterns by role
        role_patterns = self.role_patterns.get(self._categorize_role(job_title), {})
        industry_patterns = self.industry_patterns.get(industry.lower(), {})
        
        # Age-based adjustments
        age_factor = self._get_age_factor(age)
        
        # Calculate optimal windows
        morning_window = {
            "start": role_patterns.get("morning_start", "09:00"),
            "end": role_patterns.get("morning_end", "11:00"),
            "effectiveness": min(95, role_patterns.get("morning_effectiveness", 80) + age_factor),
            "reasoning": "Alta concentración y disponibilidad mental"
        }
        
        afternoon_window = {
            "start": role_patterns.get("afternoon_start", "14:00"),
            "end": role_patterns.get("afternoon_end", "16:00"),
            "effectiveness": min(90, role_patterns.get("afternoon_effectiveness", 70) + age_factor),
            "reasoning": "Post-almuerzo, momento de planificación"
        }
        
        return {
            "optimal_windows": [morning_window, afternoon_window],
            "avoid_periods": [
                {"period": "08:00-09:00", "reason": "Llegada y organización"},
                {"period": "12:00-14:00", "reason": "Hora de almuerzo"},
                {"period": "17:00-18:00", "reason": "Cierre de jornada"},
                {"period": "18:00+", "reason": "Horario personal"}
            ],
            "timezone_considerations": industry_patterns.get("timezone_notes", "Horario local de oficina")
        }

    def _calculate_weekly_timing(self, job_title: str, industry: str) -> Dict[str, Any]:
        """Calculate optimal weekly contact patterns."""
        
        # Industry-specific patterns
        industry_data = self.industry_patterns.get(industry.lower(), {})
        
        # Day effectiveness scores
        day_scores = {
            "lunes": 60,  # Planificación semanal
            "martes": 90,  # Alta productividad
            "miércoles": 85,  # Pico de semana
            "jueves": 80,  # Buena disponibilidad
            "viernes": 45   # Cierre de semana
        }
        
        # Adjust based on role
        if "director" in job_title.lower() or "ceo" in job_title.lower():
            day_scores["lunes"] += 15  # Ejecutivos planifican más en lunes
            day_scores["viernes"] -= 10  # Menos disponibles viernes
        
        return {
            "day_effectiveness": day_scores,
            "optimal_days": ["martes", "miércoles", "jueves"],
            "avoid_days": ["lunes temprano", "viernes tarde"],
            "weekly_patterns": {
                "meeting_heavy_days": industry_data.get("meeting_days", ["martes", "miércoles"]),
                "planning_days": ["lunes"],
                "execution_days": ["martes", "miércoles", "jueves"],
                "wrap_up_days": ["viernes"]
            }
        }

    def _calculate_monthly_timing(self, industry: str, company_size: str) -> Dict[str, Any]:
        """Calculate optimal monthly contact patterns."""
        
        # Month-end considerations
        month_patterns = {
            "high_activity": [1, 2, 3, 15, 16, 17],  # Beginning and mid-month
            "medium_activity": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
            "low_activity": [28, 29, 30, 31],  # Month-end closing
            "budget_review": [25, 26, 27],  # Pre-month-end planning
        }
        
        # Company size adjustments
        if company_size.lower() in ["large", "enterprise"]:
            month_patterns["budget_review"].extend([20, 21, 22, 23, 24])
        
        return {
            "monthly_patterns": month_patterns,
            "optimal_weeks": ["semana 1", "semana 2", "semana 3"],
            "avoid_periods": ["últimos 3 días del mes", "primeros 2 días del mes"],
            "budget_cycles": {
                "quarterly_planning": ["enero", "abril", "julio", "octubre"],
                "annual_planning": ["noviembre", "diciembre"],
                "execution_months": ["febrero", "marzo", "mayo", "junio", "agosto", "septiembre"]
            }
        }

    def _calculate_seasonal_timing(self, industry: str, location: str) -> Dict[str, Any]:
        """Calculate seasonal timing patterns."""
        
        # Base seasonal patterns
        seasonal_data = self.seasonal_patterns.get(location.lower(), self.seasonal_patterns["default"])
        
        # Industry-specific adjustments
        industry_seasonal = {
            "retail": {
                "high_season": ["octubre", "noviembre", "enero"],
                "low_season": ["julio", "agosto"]
            },
            "education": {
                "high_season": ["enero", "febrero", "agosto", "septiembre"],
                "low_season": ["junio", "julio", "diciembre"]
            },
            "technology": {
                "high_season": ["enero", "febrero", "septiembre", "octubre"],
                "low_season": ["julio", "agosto", "diciembre"]
            }
        }
        
        industry_pattern = industry_seasonal.get(industry.lower(), industry_seasonal["technology"])
        
        return {
            "seasonal_effectiveness": seasonal_data["effectiveness"],
            "high_activity_periods": industry_pattern["high_season"],
            "low_activity_periods": industry_pattern["low_season"],
            "vacation_periods": seasonal_data["vacation_periods"],
            "budget_seasons": {
                "planning": ["octubre", "noviembre", "diciembre"],
                "approval": ["enero", "febrero"],
                "execution": ["marzo", "abril", "mayo", "junio", "septiembre"]
            },
            "cultural_considerations": seasonal_data.get("cultural_notes", [])
        }

    def _calculate_follow_up_strategy(self, persona_data: Dict[str, Any], product_info: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal follow-up timing strategy."""
        
        # Extract decision-making characteristics
        job_level = self._get_job_level(persona_data.get('job', {}).get('title', ''))
        industry = persona_data.get('job', {}).get('industry', 'Technology')
        
        # Base follow-up intervals
        if job_level == "executive":
            intervals = ["2 días", "1 semana", "2 semanas", "1 mes"]
            max_attempts = 4
        elif job_level == "manager":
            intervals = ["1 día", "3 días", "1 semana", "2 semanas"]
            max_attempts = 5
        else:
            intervals = ["1 día", "2 días", "5 días", "1 semana", "2 semanas"]
            max_attempts = 6
        
        return {
            "follow_up_intervals": intervals,
            "max_attempts": max_attempts,
            "escalation_strategy": {
                "after_attempts": 3,
                "escalation_method": "cambiar canal de comunicación",
                "escalation_timing": "1 semana después del último intento"
            },
            "persistence_level": self._get_persistence_level(job_level, industry),
            "channel_rotation": [
                "email profesional",
                "LinkedIn message",
                "llamada telefónica",
                "email con referencia"
            ]
        }

    def _get_next_optimal_contact(self, daily_timing: Dict, weekly_timing: Dict, current_date: datetime) -> Dict[str, Any]:
        """Calculate the next optimal contact time."""
        
        # Get optimal days and times
        optimal_days = weekly_timing["optimal_days"]
        optimal_windows = daily_timing["optimal_windows"]
        
        # Find next optimal day
        current_weekday = current_date.strftime("%A").lower()
        spanish_days = {
            "monday": "lunes", "tuesday": "martes", "wednesday": "miércoles",
            "thursday": "jueves", "friday": "viernes", "saturday": "sábado", "sunday": "domingo"
        }
        current_day_spanish = spanish_days.get(current_weekday, "lunes")
        
        # Calculate next optimal contact
        days_ahead = 1
        while days_ahead <= 7:
            next_date = current_date + timedelta(days=days_ahead)
            next_day_spanish = spanish_days.get(next_date.strftime("%A").lower(), "lunes")
            
            if next_day_spanish in optimal_days:
                # Found optimal day, now find optimal time
                optimal_time = optimal_windows[0]["start"]  # Use first optimal window
                
                return {
                    "next_contact_date": next_date.strftime("%Y-%m-%d"),
                    "next_contact_day": next_day_spanish,
                    "next_contact_time": optimal_time,
                    "effectiveness_probability": optimal_windows[0]["effectiveness"],
                    "reasoning": f"Próximo {next_day_spanish} a las {optimal_time} - {optimal_windows[0]['reasoning']}"
                }
            
            days_ahead += 1
        
        # Fallback to next Tuesday if no optimal day found
        return {
            "next_contact_date": (current_date + timedelta(days=1)).strftime("%Y-%m-%d"),
            "next_contact_day": "martes",
            "next_contact_time": "10:00",
            "effectiveness_probability": 85,
            "reasoning": "Fallback a martes por la mañana - horario estándar óptimo"
        }

    def _load_industry_patterns(self) -> Dict[str, Any]:
        """Load industry-specific timing patterns."""
        return {
            "technology": {
                "morning_effectiveness": 85,
                "afternoon_effectiveness": 75,
                "meeting_days": ["martes", "miércoles"],
                "timezone_notes": "Flexible con horarios globales"
            },
            "finance": {
                "morning_effectiveness": 90,
                "afternoon_effectiveness": 70,
                "meeting_days": ["martes", "jueves"],
                "timezone_notes": "Estricto horario de mercados"
            },
            "healthcare": {
                "morning_effectiveness": 80,
                "afternoon_effectiveness": 85,
                "meeting_days": ["miércoles", "jueves"],
                "timezone_notes": "Horarios de consulta variables"
            },
            "education": {
                "morning_effectiveness": 85,
                "afternoon_effectiveness": 60,
                "meeting_days": ["martes", "jueves"],
                "timezone_notes": "Horario académico estándar"
            }
        }

    def _load_role_patterns(self) -> Dict[str, Any]:
        """Load role-specific timing patterns."""
        return {
            "executive": {
                "morning_start": "08:30",
                "morning_end": "10:00",
                "afternoon_start": "15:00",
                "afternoon_end": "16:30",
                "morning_effectiveness": 90,
                "afternoon_effectiveness": 75
            },
            "manager": {
                "morning_start": "09:00",
                "morning_end": "11:00",
                "afternoon_start": "14:00",
                "afternoon_end": "16:00",
                "morning_effectiveness": 85,
                "afternoon_effectiveness": 80
            },
            "specialist": {
                "morning_start": "09:30",
                "morning_end": "11:30",
                "afternoon_start": "14:30",
                "afternoon_end": "16:30",
                "morning_effectiveness": 80,
                "afternoon_effectiveness": 85
            }
        }

    def _load_seasonal_patterns(self) -> Dict[str, Any]:
        """Load seasonal timing patterns by location."""
        return {
            "españa": {
                "effectiveness": {
                    "enero": 85, "febrero": 90, "marzo": 85, "abril": 80,
                    "mayo": 85, "junio": 75, "julio": 40, "agosto": 30,
                    "septiembre": 90, "octubre": 85, "noviembre": 80, "diciembre": 50
                },
                "vacation_periods": ["julio", "agosto", "semana santa", "navidad"],
                "cultural_notes": ["siesta consideration", "late dinner culture"]
            },
            "default": {
                "effectiveness": {
                    "enero": 80, "febrero": 85, "marzo": 85, "abril": 80,
                    "mayo": 80, "junio": 75, "julio": 60, "agosto": 65,
                    "septiembre": 85, "octubre": 80, "noviembre": 75, "diciembre": 60
                },
                "vacation_periods": ["summer months", "year-end holidays"],
                "cultural_notes": ["standard business calendar"]
            }
        }

    def _categorize_role(self, job_title: str) -> str:
        """Categorize job title into role type."""
        title_lower = job_title.lower()
        if any(word in title_lower for word in ["ceo", "director", "vp", "president"]):
            return "executive"
        elif any(word in title_lower for word in ["manager", "lead", "head", "supervisor"]):
            return "manager"
        else:
            return "specialist"

    def _get_job_level(self, job_title: str) -> str:
        """Get job level for follow-up strategy."""
        return self._categorize_role(job_title)

    def _get_age_factor(self, age: int) -> int:
        """Get age-based adjustment factor."""
        if age < 30:
            return 5  # Younger professionals more responsive
        elif age > 50:
            return -5  # Senior professionals more selective
        else:
            return 0

    def _get_persistence_level(self, job_level: str, industry: str) -> str:
        """Determine appropriate persistence level."""
        if job_level == "executive":
            return "low"  # Executives prefer less frequent contact
        elif industry.lower() in ["technology", "startups"]:
            return "medium-high"  # Tech industry more open to outreach
        else:
            return "medium"

    def _calculate_timing_confidence(self, industry: str, job_title: str, location: str) -> int:
        """Calculate confidence score for timing predictions."""
        confidence = 70  # Base confidence
        
        # Industry knowledge boost
        if industry.lower() in self.industry_patterns:
            confidence += 15
        
        # Role clarity boost
        if any(word in job_title.lower() for word in ["manager", "director", "ceo"]):
            confidence += 10
        
        # Location data boost
        if location.lower() in self.seasonal_patterns:
            confidence += 5
        
        return min(confidence, 95)


# Create singleton instance
intelligent_timing_service = IntelligentTimingService()
