"""
Completeness Analyzer Module
Analyzes completeness of information coverage
"""

import logging
import asyncio
import re
from typing import Dict, Any, List

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class CompletenessAnalyzer:
    """Analyzes completeness of information coverage."""
    
    def __init__(self):
        self.gemini_model = None
        
        # Initialize Gemini AI
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Completeness Analyzer - Gemini AI initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Gemini AI: {e}")
                self.gemini_model = None
        else:
            logger.warning("⚠️ GEMINI_API_KEY not found. Completeness analysis will use rule-based approach.")
    
    async def calculate_completeness_score(self, content: str, topic: str) -> float:
        """
        Calculate completeness of information coverage.
        
        Args:
            content: Content to analyze
            topic: Main topic for context
            
        Returns:
            Completeness score (0-100)
        """
        try:
            if not self.gemini_model:
                return self._fallback_completeness_score(content, topic)
            
            prompt = f"""
            Evalúa qué tan completo está el siguiente contenido sobre "{topic}" en una escala de 0-100.
            
            {content[:2000]}
            
            Considera:
            - ¿Cubre los aspectos principales del tema?
            - ¿Incluye información suficiente para ser útil?
            - ¿Responde las preguntas básicas (qué, cómo, por qué, cuándo)?
            - ¿Proporciona contexto adecuado?
            - ¿Incluye ejemplos y aplicaciones prácticas?
            
            Responde solo con un número entre 0 y 100.
            """
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, prompt
            )
            
            score_text = response.text.strip()
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', score_text)
            
            if score_match:
                score = float(score_match.group(1))
                return min(max(score, 0), 100)
            else:
                return self._fallback_completeness_score(content, topic)
                
        except Exception as e:
            logger.error(f"❌ Completeness score calculation failed: {str(e)}")
            return self._fallback_completeness_score(content, topic)
    
    def _fallback_completeness_score(self, content: str, topic: str) -> float:
        """Fallback completeness score calculation."""
        try:
            score = 40.0  # Base score
            
            # Check content length (longer content tends to be more complete)
            word_count = len(content.split())
            if word_count >= 500:
                score += 20
            elif word_count >= 300:
                score += 15
            elif word_count >= 150:
                score += 10
            
            # Check for question words (indicates comprehensive coverage)
            question_words = ['qué', 'cómo', 'por qué', 'cuándo', 'dónde', 'quién']
            question_coverage = sum(1 for word in question_words if word in content.lower())
            score += min(question_coverage * 3, 18)
            
            # Check for examples
            if any(phrase in content.lower() for phrase in ['por ejemplo', 'ejemplo', 'como']):
                score += 12
            
            # Check for topic coverage
            if topic and topic.lower() in content.lower():
                topic_mentions = content.lower().count(topic.lower())
                score += min(topic_mentions * 2, 10)
            
            return min(score, 100)
            
        except:
            return 40.0
    
    def analyze_coverage_dimensions(self, content: str, topic: str) -> Dict[str, Any]:
        """Analyze different dimensions of content coverage."""
        try:
            content_lower = content.lower()
            
            coverage_dimensions = {
                "what_coverage": {
                    "indicators": ['qué es', 'se define', 'consiste en', 'significa'],
                    "score": 0
                },
                "how_coverage": {
                    "indicators": ['cómo', 'proceso', 'método', 'pasos', 'procedimiento'],
                    "score": 0
                },
                "why_coverage": {
                    "indicators": ['por qué', 'razón', 'causa', 'motivo', 'beneficio'],
                    "score": 0
                },
                "when_coverage": {
                    "indicators": ['cuándo', 'tiempo', 'momento', 'fecha', 'período'],
                    "score": 0
                },
                "where_coverage": {
                    "indicators": ['dónde', 'lugar', 'ubicación', 'sitio', 'región'],
                    "score": 0
                },
                "who_coverage": {
                    "indicators": ['quién', 'persona', 'experto', 'profesional', 'usuario'],
                    "score": 0
                }
            }
            
            # Score each dimension
            for dimension, data in coverage_dimensions.items():
                for indicator in data["indicators"]:
                    if indicator in content_lower:
                        data["score"] += 20
                data["score"] = min(data["score"], 100)
            
            # Calculate overall coverage
            total_coverage = sum(data["score"] for data in coverage_dimensions.values())
            avg_coverage = total_coverage / len(coverage_dimensions)
            
            # Identify gaps
            coverage_gaps = [
                dimension for dimension, data in coverage_dimensions.items() 
                if data["score"] < 40
            ]
            
            return {
                "coverage_dimensions": coverage_dimensions,
                "average_coverage": avg_coverage,
                "coverage_gaps": coverage_gaps,
                "coverage_completeness": self._classify_coverage_completeness(avg_coverage)
            }
            
        except Exception as e:
            logger.error(f"❌ Coverage dimensions analysis failed: {str(e)}")
            return {
                "average_coverage": 50,
                "coverage_completeness": "unknown",
                "error": str(e)
            }
    
    def _classify_coverage_completeness(self, avg_coverage: float) -> str:
        """Classify coverage completeness level."""
        if avg_coverage >= 80:
            return "comprehensive"
        elif avg_coverage >= 60:
            return "good"
        elif avg_coverage >= 40:
            return "partial"
        else:
            return "incomplete"
