"""
Schemas for Style Transfer functionality using Stability AI API.
Based on the documentation at stability-openapi.json line 13816.
"""

from typing import Optional, Literal
from pydantic import BaseModel, Field


class StyleTransferRequest(BaseModel):
    """Request schema for Stability AI style transfer API."""
    
    prompt: Optional[str] = Field(
        default="",
        max_length=10000,
        description="What you wish to see in the output image. A strong, descriptive prompt that clearly defines elements, colors, and subjects will lead to better results."
    )
    
    negative_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="A blurb of text describing what you do not wish to see in the output image."
    )
    
    style_strength: Optional[float] = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="Controls how much influence the style_image parameter has on the generated image. A value of 0 would yield an image identical to the input. A value of 1 would be as if you passed in no image at all."
    )
    
    composition_fidelity: Optional[float] = Field(
        default=0.9,
        ge=0.0,
        le=1.0,
        description="How closely the output image's style resembles the input image's style."
    )
    
    change_strength: Optional[float] = Field(
        default=0.9,
        ge=0.1,
        le=1.0,
        description="How much the original image should change"
    )
    
    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation (0 = random)"
    )
    
    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="png",
        description="Dictates the content-type of the generated image"
    )


class StyleTransferResponse(BaseModel):
    """Response schema from Stability AI style transfer API."""
    
    image: str = Field(description="Base64 encoded image data")
    seed: Optional[int] = Field(description="The seed used for generation")
    finish_reason: str = Field(description="Reason the generation finished")
