# Backend Vibe Marketing – Setup y Deploy

## 1. Requisitos
- Python 3.10+
- Acceso a variables de entorno/API keys (ver `.env.example`)
- Docker (opcional para producción)

## 2. Instalación y entorno virtual

```bash
python3 -m venv .venv
source .venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

## 3. Variables de entorno
Copia `.env.example` a `.env` y completa tus claves:

```bash
cp .env.example .env
```

## 4. CrewAI como dependencia local
El código de CrewAI está clonado en `backend/crewAI` y se instala como editable (`-e ./crewAI`). Puedes modificarlo/extenderlo según tus agentes y flujos.

## 5. Ejecución local (desarrollo)

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 6. Deploy en producción

- <PERSON><PERSON>/Uvicorn para producción:

```bash
gunicorn -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
```

- O crea un `Dockerfile` para deploy en cualquier cloud (Vercel, Railway, Render, etc.)

## 7. Estructura recomendada

```
backend/
  crewAI/           # Código fuente CrewAI (open source)
  main.py           # FastAPI app
  requirements.txt
  .env.example
  README_BACKEND.md
```

## 8. Notas importantes
- No expongas nunca claves API en frontend.
- El reasoning trace y los agentes se orquestan localmente usando CrewAI.
- Esta app está lista para deploy en cualquier plataforma cloud compatible con Python/FastAPI.

## 👑 Agente Emma: Jefa de Orquestación (CrewAI)

### Principios de Emma (Inspirados en biología y neurociencia)

- Analiza el objetivo del usuario y el contexto del mercado.
- Descompone el objetivo en sub-tareas inteligentes.
- Asigna, motiva y supervisa agentes especialistas (memes, copy, imagen, cine, etc.).
- Toma decisiones estratégicas y adapta el flujo según resultados parciales.
- Documenta todo el reasoning trace y justifica cada decisión.
- Usa memoria de contexto (Gémini, CrewAI memory) para continuidad y aprendizaje.

Desde la versión 2025-04, el backend implementa a **Emma** como agente principal y jefa de orquestación en CrewAI.

- **Emma** recibe el objetivo de campaña del usuario y diseña la estrategia global.
- Orquesta y dirige a los agentes especialistas: meme, imagen fotográfica, cine, copy, etc.
- Emma decide qué agentes activar, cómo deben colaborar y explica su razonamiento en el reasoning trace.
- Emma reemplaza al antiguo "orquestador" y es el entrypoint de la función `build_autonomous_crewai_team` en `crew_agents.py`.
- Emma asegura el cumplimiento de reglas de seguridad y estilo en todos los assets generados.

### Ejemplo de uso en código

```python
from crew_agents import build_autonomous_crewai_team

crew = build_autonomous_crewai_team(prompt="Campaña viral para turismo joven en Instagram")
result = crew.kickoff()
print(result)
```

### Estructura del reasoning trace generado por Emma (versión avanzada)

```json
[
  { "type": "prompt", "content": "Campaña viral para turismo joven en Instagram", "timestamp": "..." },
  { "type": "analysis", "agent": "Emma", "content": "Emma analiza el objetivo y contexto del mercado usando memoria y tendencias previas.", "memory_refs": ["historial_campañas", "tendencias_2025"], "timestamp": "..." },
  { "type": "decision", "agent": "Emma", "content": "Emma decide activar meme_agent, photographic_agent y copy_agent para captar atención visual y verbal.", "timestamp": "..." },
  { "type": "subtask", "agent": "meme_agent", "content": "Genera meme visual con humor actual.", "output": "url_meme", "timestamp": "..." },
  { "type": "subtask", "agent": "photographic_agent", "content": "Genera imagen fotográfica atractiva.", "output": "url_foto", "timestamp": "..." },
  { "type": "subtask", "agent": "copy_agent", "content": "Genera copy viral alineado a la campaña.", "output": "copy_text", "timestamp": "..." },
  { "type": "reflection", "agent": "Emma", "content": "Emma integra los assets, verifica coherencia y ajusta la estrategia si es necesario.", "timestamp": "..." },
  { "type": "asset", "agent": "Emma", "content": { "meme": "url_meme", "foto": "url_foto", "copy": "copy_text" }, "timestamp": "..." }
]
```

- Puedes extender a Emma con nuevas herramientas (ej: generación de video, búsqueda web, etc.) simplemente añadiendo más tools y agentes en `crew_agents.py`.
- El reasoning trace avanzado permite auditar, explicar y mejorar cada decisión tomada por Emma y los agentes.

---

Para dudas técnicas revisa también el README principal del proyecto.

## 🔮 Diseño de Integración con Base de Datos Vectorial (para memoria/contexto de Emma)

> **Nota:** Esta sección documenta el diseño y los modelos de datos recomendados para implementar memoria vectorial y reasoning trace avanzado en el futuro. No está implementado aún, pero la arquitectura está preparada para escalar y conectar con Pinecone, Weaviate, ChromaDB u otro proveedor.

### 1. Arquitectura General

```
[Frontend] <--> [FastAPI Backend (Emma + CrewAI)] <--> [Vector DB/Memoria]
```

### 2. Flujos Clave

- **Guardar reasoning trace y resultados:**
  - Al finalizar una campaña, Emma guarda el reasoning trace, assets, metadatos y embeddings en la base de datos vectorial.
- **Consultar memoria/contexto:**
  - Antes de orquestar una campaña, Emma consulta la vector DB para recuperar campañas/contextos similares usando embeddings del prompt.
- **Referenciar contexto en reasoning trace:**
  - El reasoning trace documenta campañas/contextos recuperados de la memoria vectorial.

### 3. Modelos de Datos Recomendados

#### campaigns (colección/tabla principal)
- `id`: string/uuid
- `user_id`: string
- `prompt`: string
- `reasoning_trace`: JSON (estructura avanzada)
- `assets`: JSON (urls, textos, etc.)
- `timestamp`: datetime
- `embeddings`: vector (array de floats, para búsqueda semántica)
- `tags`: array[string] (temas, canal, etc.)
- `context`: JSON (opcional, info adicional)

#### users (colección/tabla secundaria)
- `id`: string/uuid
- `preferences`: JSON
- `history`: array[campaign_id]

#### Ejemplo de reasoning trace referenciando memoria/contexto
```json
{
  "type": "analysis",
  "agent": "Emma",
  "content": "Emma analiza el objetivo y contexto usando memoria y campañas previas.",
  "memory_refs": [
    {"campaign_id": "abc123", "summary": "Campaña exitosa para turismo joven 2024"},
    {"trend": "tendencias_2025"}
  ],
  "timestamp": "..."
}
```

### 4. Ventajas del diseño
- Agnóstico al proveedor (Pinecone, Weaviate, ChromaDB, etc.).
- Permite búsquedas semánticas, recuperación de contexto y personalización por usuario.
- Facilita la auditoría y mejora continua de Emma y los agentes.

---

> Cuando se decida el proveedor de vector DB, implementar los métodos `save_campaign_trace` y `retrieve_relevant_context` siguiendo estos modelos y flujos.
