"""
API endpoints for inpaint (fill areas) functionality.
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from app.core.auth import verify_api_key
from app.schemas.inpaint import InpaintRequest, FrontendInpaintResponse
from app.services.inpaint_service import inpaint_areas_stability

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/inpaint",
    response_model=FrontendInpaintResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Fill areas in image with AI-generated content",
    description="""
    Fill specific areas in an image with AI-generated content using Stability AI v2beta API.

    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Cost:** 3 credits per successful generation

    **Parameters:**
    - **image**: The image file to process (required)
    - **prompt**: Text describing what to fill in the masked areas (required)
    - **mask**: Optional mask file indicating areas to fill (black = preserve, white = fill)
    - **negative_prompt**: Optional text describing what NOT to include
    - **grow_mask**: Grows the edges of the mask outward (0-20 pixels, default: 5)
    - **seed**: Random seed for reproducible results (0 = random, default: 0)
    - **output_format**: Output format ("jpeg", "png", or "webp", default: "webp")

    **Mask Options:**
    1. **Explicit mask**: Pass a separate black and white image via the `mask` parameter
    2. **Alpha channel**: Use an image with transparency (alpha channel) as the mask

    **How masks work:**
    - **Black pixels**: Areas to preserve (no filling)
    - **White pixels**: Areas to fill with AI-generated content
    - **Gray pixels**: Partial filling based on intensity

    **Prompt Guidelines:**
    - Be specific about what you want to fill the areas with
    - Use descriptive language for better results
    - Consider the context of the surrounding image
    """
)
async def inpaint_areas_endpoint(
    image: UploadFile = File(..., description="Image file to process"),
    prompt: str = Form(..., description="Text describing what to fill in the masked areas"),
    mask: Optional[UploadFile] = File(None, description="Optional mask file (black=preserve, white=fill)"),
    negative_prompt: Optional[str] = Form(None, description="Text describing what NOT to include"),
    grow_mask: Optional[int] = Form(5, description="Grow mask edges (0-100 pixels)"),
    seed: Optional[int] = Form(0, description="Random seed (0 = random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)"),
    style_preset: Optional[str] = Form(None, description="Style preset for the generation")
) -> FrontendInpaintResponse:
    """
    Fill areas in an image with AI-generated content using Stability AI v2beta API.

    This endpoint fills specified areas in images using either:
    1. An explicit mask image (black and white)
    2. The alpha channel of the input image

    The AI will generate content based on the provided prompt to fill the masked areas
    while preserving the rest of the image.
    """
    logger.info(f"Received inpaint request for prompt: '{prompt[:50]}...'")

    try:
        # Validar archivo de imagen
        if not image.filename:
            raise HTTPException(status_code=400, detail="Image filename is required")

        # Validar formato de salida
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="output_format must be 'jpeg', 'png', or 'webp'"
            )

        # Validar prompt
        if not prompt or not prompt.strip():
            raise HTTPException(
                status_code=400,
                detail="Prompt is required and cannot be empty"
            )

        # Crear request object
        inpaint_request = InpaintRequest(
            prompt=prompt.strip(),
            negative_prompt=negative_prompt.strip() if negative_prompt else None,
            grow_mask=grow_mask or 5,
            seed=seed or 0,
            output_format=output_format or "png",
            style_preset=style_preset
        )

        # Llamar al servicio de Stability AI
        service_response = await inpaint_areas_stability(
            image_file=image,
            mask_file=mask,
            request=inpaint_request
        )

        # Crear data URL para el frontend (igual que erase y background removal)
        mime_type = f"image/{output_format or 'png'}"
        image_data_url = f"data:{mime_type};base64,{service_response.image}"

        logger.info(f"Inpaint operation completed successfully. Result size: {len(image_data_url)} chars")

        return FrontendInpaintResponse(
            success=True,
            image_url=image_data_url,
            seed=service_response.seed,
            finish_reason=service_response.finish_reason,
            metadata={
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "grow_mask": grow_mask,
                "seed": seed,
                "output_format": output_format,
                "style_preset": style_preset,
                "original_filename": image.filename,
                "mask_provided": mask is not None
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in inpaint endpoint: {str(e)}", exc_info=True)
        return FrontendInpaintResponse(
            success=False,
            error=f"Failed to process inpaint request: {str(e)}"
        )
