"""
Service for outpaint (expand image) functionality using Stability AI v2beta API.
"""
import httpx
import io
import logging
from fastapi import HTTPEx<PERSON>, UploadFile
from PIL import Image

from app.core.config import settings
from app.schemas.outpaint import OutpaintRequest, OutpaintResponse

logger = logging.getLogger(__name__)


def get_stability_headers_json():
    """Get headers for Stability AI API requests expecting JSON response."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"
    }


async def validate_outpaint_image(image_file: UploadFile) -> bytes:
    """
    Validate an image file for outpaint operation.

    Args:
        image_file: The uploaded image file

    Returns:
        bytes: The validated image content

    Raises:
        HTTPException: If validation fails
    """
    try:
        # Leer el contenido del archivo
        image_content = await image_file.read()

        # Validar tamaño del archivo
        max_size = 10 * 1024 * 1024  # 10MB
        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"Image file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )

        # Validar que sea una imagen válida
        try:
            with Image.open(io.BytesIO(image_content)) as img:
                # Validar dimensiones mínimas (64x64 según documentación)
                if img.width < 64 or img.height < 64:
                    raise HTTPException(
                        status_code=400,
                        detail="Image dimensions too small. Minimum size is 64x64 pixels"
                    )

                # Validar dimensiones máximas (9,437,184 pixels total según documentación)
                total_pixels = img.width * img.height
                max_pixels = 9_437_184
                if total_pixels > max_pixels:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Image too large. Maximum total pixels: {max_pixels:,}"
                    )

                # Validar formato
                if img.format not in ['JPEG', 'PNG', 'WEBP']:
                    raise HTTPException(
                        status_code=400,
                        detail="Unsupported image format. Supported formats: JPEG, PNG, WebP"
                    )

                logger.info(f"Image validated: {img.width}x{img.height}, format: {img.format}")

        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=400, detail="Invalid image file")

        return image_content

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating image: {e}")
        raise HTTPException(status_code=400, detail="Error processing image file")


async def outpaint_image_stability(
    image_file: UploadFile,
    request: OutpaintRequest
) -> OutpaintResponse:
    """
    Expand an image using Stability AI v2beta outpaint API.

    Args:
        image_file: The image file to expand
        request: The outpaint parameters

    Returns:
        OutpaintResponse: The expanded image and metadata

    Raises:
        HTTPException: If outpaint operation fails
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar que al menos una dirección esté especificada
        if not request.has_outpaint_direction():
            raise HTTPException(
                status_code=400,
                detail="At least one outpaint direction (left, right, up, down) must be greater than 0"
            )

        # Validar la imagen principal
        image_content = await validate_outpaint_image(image_file)

        # URL de la API v2beta para outpaint
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/edit/outpaint"

        # Headers para recibir respuesta JSON con base64
        headers = get_stability_headers_json()

        # Preparar archivos para el request usando io.BytesIO
        files = {
            "image": ("image.jpg", io.BytesIO(image_content), image_file.content_type or "image/jpeg")
        }

        # Preparar FormData (igual que erase_service)
        form_data = {
            "left": request.left,
            "right": request.right,
            "up": request.up,
            "down": request.down,
            "creativity": request.creativity,
            "output_format": request.output_format
        }

        # Agregar seed si se especifica (igual que erase_service)
        if request.seed and request.seed > 0:
            form_data["seed"] = request.seed

        # Agregar prompt si se proporciona
        if request.prompt:
            form_data["prompt"] = request.prompt

        # Agregar style_preset si se proporciona
        if request.style_preset:
            form_data["style_preset"] = request.style_preset

        logger.info(f"Outpainting image using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Directions: left={request.left}, right={request.right}, up={request.up}, down={request.down}")
        logger.info(f"Creativity: {request.creativity}")
        logger.info(f"Output format: {request.output_format}")
        logger.info(f"Style preset: {request.style_preset}")
        logger.info(f"Has prompt: {request.prompt is not None}")
        logger.info(f"Form data: {form_data}")
        logger.info(f"Files: {list(files.keys())}")
        logger.info(f"Headers: {headers}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return OutpaintResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in outpaint operation: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to expand image: {str(e)}"
        )
