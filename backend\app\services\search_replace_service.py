"""
Service for search and replace (replace objects) functionality using Stability AI v2beta API.
"""
import httpx
import io
import logging
import base64
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, UploadFile
from PIL import Image

from app.core.config import settings
from app.schemas.search_replace import SearchReplaceRequest, SearchReplaceResponse

logger = logging.getLogger(__name__)


def get_stability_headers_json():
    """Get headers for Stability AI API requests expecting JSON response."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"
    }


def validate_base64_image(image_data: str) -> bool:
    """
    Validate that the base64 string represents a valid image.

    Args:
        image_data: Base64 encoded image string

    Returns:
        bool: True if valid, False otherwise
    """
    try:
        # Intentar decodificar el base64
        image_bytes = base64.b64decode(image_data)

        # Verificar que tenga un tamaño mínimo razonable
        if len(image_bytes) < 100:  # Muy pequeño para ser una imagen válida
            logger.warning(f"Base64 image too small: {len(image_bytes)} bytes")
            return False

        # Intentar abrir como imagen con PIL
        with Image.open(io.BytesIO(image_bytes)) as img:
            # Verificar que tenga dimensiones válidas
            if img.width < 1 or img.height < 1:
                logger.warning(f"Invalid image dimensions: {img.width}x{img.height}")
                return False

            logger.info(f"Base64 image validated: {img.width}x{img.height}, format: {img.format}, size: {len(image_bytes)} bytes")
            return True

    except Exception as e:
        logger.error(f"Base64 image validation failed: {e}")
        return False


async def validate_search_replace_image(image_file: UploadFile) -> bytes:
    """
    Validate and process the search replace image file.

    Args:
        image_file: The uploaded image file

    Returns:
        bytes: The processed image bytes

    Raises:
        HTTPException: If validation fails
    """
    # Verificar que hay un archivo
    if not image_file:
        raise HTTPException(status_code=400, detail="No image file provided")

    # Verificar el tipo de contenido
    allowed_types = ["image/jpeg", "image/png", "image/webp"]
    if image_file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported image format. Allowed: {', '.join(allowed_types)}"
        )

    # Leer el contenido del archivo
    try:
        image_content = await image_file.read()
    except Exception as e:
        logger.error(f"Error reading image file: {str(e)}")
        raise HTTPException(status_code=400, detail="Error reading image file")

    # Verificar el tamaño del archivo (máximo 10MB)
    max_size = 10 * 1024 * 1024  # 10MB
    if len(image_content) > max_size:
        raise HTTPException(
            status_code=413,
            detail="Image file too large. Maximum size is 10MB"
        )

    # Validar que es una imagen válida usando PIL
    try:
        image = Image.open(io.BytesIO(image_content))

        # Verificar dimensiones mínimas (64px por lado según documentación)
        width, height = image.size
        if width < 64 or height < 64:
            raise HTTPException(
                status_code=400,
                detail="Image dimensions too small. Minimum 64x64 pixels"
            )

        # Verificar límite de píxeles totales (9,437,184 según documentación)
        max_pixels = 9437184
        if width * height > max_pixels:
            raise HTTPException(
                status_code=400,
                detail=f"Image too large. Maximum {max_pixels} pixels total"
            )

        # Verificar aspect ratio (entre 1:2.5 y 2.5:1)
        aspect_ratio = width / height
        if aspect_ratio < 0.4 or aspect_ratio > 2.5:
            raise HTTPException(
                status_code=400,
                detail="Invalid aspect ratio. Must be between 1:2.5 and 2.5:1"
            )

        logger.info(f"Image validation successful: {width}x{height}, {len(image_content)} bytes")
        return image_content

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating image: {str(e)}")
        raise HTTPException(status_code=400, detail="Invalid image file")


async def search_replace_objects_stability(
    image_file: UploadFile,
    request: SearchReplaceRequest
) -> SearchReplaceResponse:
    """
    Replace objects in an image using Stability AI v2beta search and replace API.
    
    Args:
        image_file: The image file to process
        request: The search and replace parameters
        
    Returns:
        SearchReplaceResponse: The processed image and metadata
        
    Raises:
        HTTPException: If search and replace operation fails
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar la imagen principal
        image_content = await validate_search_replace_image(image_file)
        
        # URL de la API v2beta para search and replace
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/edit/search-and-replace"
        
        # Headers para recibir respuesta JSON con base64
        headers = get_stability_headers_json()

        # Preparar archivos para el request usando io.BytesIO
        files = {
            "image": ("image.jpg", io.BytesIO(image_content), image_file.content_type or "image/jpeg")
        }

        # Preparar FormData (igual que otras implementaciones exitosas)
        form_data = {
            "prompt": request.prompt,
            "search_prompt": request.search_prompt,
            "grow_mask": request.grow_mask,
            "output_format": request.output_format
        }

        # Agregar seed si se especifica (igual que otras implementaciones)
        if request.seed and request.seed > 0:
            form_data["seed"] = request.seed
        
        # Agregar negative_prompt si se proporciona
        if request.negative_prompt:
            form_data["negative_prompt"] = request.negative_prompt
            
        # Agregar style_preset si se proporciona
        if request.style_preset:
            form_data["style_preset"] = request.style_preset

        logger.info(f"Search and replace using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Prompt: {request.prompt}")
        logger.info(f"Search prompt: {request.search_prompt}")
        logger.info(f"Grow mask: {request.grow_mask}")
        logger.info(f"Output format: {request.output_format}")
        logger.info(f"Style preset: {request.style_preset}")
        logger.info(f"Has negative prompt: {request.negative_prompt is not None}")
        logger.info(f"Form data: {form_data}")
        logger.info(f"Files: {list(files.keys())}")
        logger.info(f"Headers: {headers}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            logger.info(f"Stability AI response keys: {list(result.keys())}")

            image_data = result.get("image")

            if not image_data:
                logger.error(f"No image data in response. Response: {result}")
                raise ValueError("No image data in response")

            # Validar que la imagen base64 sea válida
            if not validate_base64_image(image_data):
                logger.error("Received invalid base64 image data from Stability AI")
                raise ValueError("Invalid image data received from Stability AI")

            logger.info(f"Search and replace completed successfully. Image data length: {len(image_data)} chars")

            return SearchReplaceResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in search and replace operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during search and replace operation: {str(e)}"
        )
