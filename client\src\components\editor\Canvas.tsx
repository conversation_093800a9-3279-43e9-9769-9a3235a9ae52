import React, { useRef, useEffect } from "react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  useSensor,
  useSensors,
  PointerSensor,
} from "@dnd-kit/core";
import { useEditor, ElementStyle } from "./EditorContext";
import CanvasElement from "./CanvasElement";

interface CanvasProps {
  width?: number;
  height?: number;
}

const Canvas: React.FC<CanvasProps> = ({ width = 1080, height = 1080 }) => {
  const {
    elements,
    selectedElementId,
    canvasBackground,
    selectElement,
    updateElementPosition,
  } = useEditor();

  const canvasRef = useRef<HTMLDivElement>(null);

  // Configurar sensores para drag & drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // distancia mínima para activar el arrastre
      },
    }),
  );

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    selectElement(active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, delta } = event;
    const id = active.id as string;

    // Encontrar el elemento actual
    const element = elements.find((el) => el.id === id);
    if (!element) return;

    // Calcular nueva posición
    const newPosition = {
      x: element.position.x + delta.x,
      y: element.position.y + delta.y,
    };

    // Actualizar posición
    updateElementPosition(id, newPosition);
  };

  const handleCanvasClick = () => {
    // Deseleccionar elementos al hacer clic en el canvas vacío
    selectElement(null);
  };

  // Asegurarse de que los elementos no se salgan del canvas
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvasBounds = canvasRef.current.getBoundingClientRect();

    elements.forEach((element) => {
      let needsUpdate = false;
      let newX = element.position.x;
      let newY = element.position.y;

      // Limitar posición X
      if (newX < 0) {
        newX = 0;
        needsUpdate = true;
      } else if (newX > width - 50) {
        // asumir un ancho mínimo de elemento
        newX = width - 50;
        needsUpdate = true;
      }

      // Limitar posición Y
      if (newY < 0) {
        newY = 0;
        needsUpdate = true;
      } else if (newY > height - 50) {
        // asumir una altura mínima de elemento
        newY = height - 50;
        needsUpdate = true;
      }

      // Actualizar si es necesario
      if (needsUpdate) {
        updateElementPosition(element.id, { x: newX, y: newY });
      }
    });
  }, [elements, updateElementPosition, width, height]);

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div
        ref={canvasRef}
        className="canvas-container bg-white shadow-md rounded-md border border-gray-200"
        style={{
          width: "100%",
          height: "100%",
          maxWidth: "700px",
          maxHeight: "700px",
          aspectRatio: `${width} / ${height}`,
          position: "relative",
          overflow: "hidden",
        }}
        onClick={handleCanvasClick}
      >
        <div
          className="canvas absolute inset-0"
          style={{
            backgroundColor: canvasBackground,
            touchAction: "none",
          }}
        >
          {elements.map((element) => (
            <CanvasElement
              key={element.id}
              id={element.id}
              type={element.type}
              content={element.content}
              position={element.position}
              style={element.style}
              selected={selectedElementId === element.id}
              onClick={(id) => selectElement(id)}
            />
          ))}
        </div>
      </div>
    </DndContext>
  );
};

export default Canvas;
