"""
Tests for the AgentOrchestrator class
"""

import unittest
import asyncio
from unittest.mock import MagicMock, patch

from agents import (
    BaseAgent,
    AgentOrchestrator,
    TaskStatus,
    TaskPriority,
    AgentTask,
    AgentContext,
    AgentAction,
    ActionResult,
    ContextType
)

class TestAgentOrchestrator(unittest.TestCase):
    """Test cases for the AgentOrchestrator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a concrete implementation of BaseAgent for testing
        class TestAgent(BaseAgent):
            async def get_next_action(self, context):
                return AgentAction(action_type="COMPLETE", content="Test result")
                
            async def execute_action(self, action):
                return ActionResult(success=True, result="Test result")
        
        self.agent_class = TestAgent
        self.orchestrator = AgentOrchestrator("Test Orchestrator")
        self.agent = self.agent_class("test_agent", "Test Agent")
        self.orchestrator.register_agent(self.agent)
    
    def test_initialization(self):
        """Test orchestrator initialization."""
        orchestrator = AgentOrchestrator("Test Orchestrator")
        self.assertEqual(orchestrator.name, "Test Orchestrator")
        self.assertIsNotNone(orchestrator.id)  # Should generate a UUID
        self.assertEqual(len(orchestrator.agents), 0)
    
    def test_register_agent(self):
        """Test registering an agent."""
        # Create a new orchestrator
        orchestrator = AgentOrchestrator("Test Orchestrator")
        
        # Register an agent
        agent = self.agent_class("test_agent", "Test Agent")
        orchestrator.register_agent(agent)
        
        # Check that the agent was registered
        self.assertEqual(len(orchestrator.agents), 1)
        self.assertIn(agent.id, orchestrator.agents)
        self.assertEqual(orchestrator.agents[agent.id], agent)
    
    def test_create_task(self):
        """Test creating a task."""
        # Create a task
        task = self.orchestrator.create_task(
            description="Test task",
            priority=TaskPriority.HIGH,
            assigned_to=self.agent.id
        )
        
        # Check the task properties
        self.assertEqual(task.description, "Test task")
        self.assertEqual(task.priority, TaskPriority.HIGH.value)
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertEqual(task.assigned_to, self.agent.id)
    
    def test_create_workflow(self):
        """Test creating a workflow."""
        # Create a workflow
        workflow = self.orchestrator.create_workflow(
            name="Test Workflow",
            description="Test workflow description",
            priority=TaskPriority.MEDIUM
        )
        
        # Check the workflow properties
        self.assertEqual(workflow.name, "Test Workflow")
        self.assertEqual(workflow.description, "Test workflow description")
        self.assertEqual(workflow.priority, TaskPriority.MEDIUM.value)
        self.assertEqual(workflow.status, TaskStatus.PENDING)
        self.assertEqual(len(workflow.tasks), 0)
    
    def test_add_task_to_workflow(self):
        """Test adding a task to a workflow."""
        # Create a workflow
        workflow = self.orchestrator.create_workflow(
            name="Test Workflow"
        )
        
        # Create a task
        task = self.orchestrator.create_task(
            description="Test task",
            assigned_to=self.agent.id
        )
        
        # Add the task to the workflow
        self.orchestrator.add_task_to_workflow(workflow.id, task)
        
        # Check that the task was added to the workflow
        self.assertEqual(len(workflow.tasks), 1)
        self.assertEqual(workflow.tasks[0], task)
        self.assertEqual(task.metadata["workflow_id"], workflow.id)
    
    @patch.object(BaseAgent, 'process_task')
    def test_execute_workflow(self, mock_process_task):
        """Test executing a workflow."""
        # Mock the process_task method
        mock_process_task.return_value = asyncio.Future()
        mock_process_task.return_value.set_result({
            "task_id": "test_task",
            "status": "completed",
            "result": "Test result"
        })
        
        # Create a workflow
        workflow = self.orchestrator.create_workflow(
            name="Test Workflow"
        )
        
        # Create a task
        task = self.orchestrator.create_task(
            description="Test task",
            assigned_to=self.agent.id
        )
        
        # Add the task to the workflow
        self.orchestrator.add_task_to_workflow(workflow.id, task)
        
        # Execute the workflow
        result = asyncio.run(self.orchestrator.execute_workflow(workflow.id))
        
        # Check the result
        self.assertEqual(result.workflow_id, workflow.id)
        self.assertEqual(result.status, TaskStatus.COMPLETED)
        self.assertEqual(len(result.task_results), 1)
        self.assertEqual(result.task_results[0].task_id, task.id)
        self.assertEqual(result.task_results[0].status, TaskStatus.COMPLETED)

if __name__ == "__main__":
    unittest.main()
