/**
 * Database Cleanup Utility for Visual Complexity Analyzer
 * This script identifies and fixes inconsistencies between database records and Supabase Storage files
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🧹 Starting Database Cleanup Utility...');

class DatabaseCleanupUtility {
  constructor() {
    this.results = {
      auth: null,
      analyses: [],
      storageFiles: [],
      inconsistencies: [],
      cleanupActions: [],
      summary: {}
    };
    this.supabase = window.supabase;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`[${timestamp}] ${emoji} ${message}`);
  }

  async checkAuth() {
    this.log('🔐 Checking authentication status...');
    
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();
      
      if (error) {
        this.log(`Authentication error: ${error.message}`, 'error');
        return false;
      }
      
      if (!user) {
        this.log('No authenticated user found', 'warn');
        return false;
      }
      
      this.results.auth = {
        userId: user.id,
        email: user.email
      };
      
      this.log(`✅ Authenticated as: ${user.email} (${user.id})`);
      return true;
    } catch (error) {
      this.log(`Failed to check auth: ${error.message}`, 'error');
      return false;
    }
  }

  async getAllAnalyses() {
    this.log('📊 Fetching all analyses...');
    
    try {
      const { data: analyses, error } = await this.supabase
        .schema('api')
        .from('design_analyses')
        .select('*')
        .eq('user_id', this.results.auth.userId)
        .order('created_at', { ascending: false });

      if (error) {
        this.log(`Error fetching analyses: ${error.message}`, 'error');
        return [];
      }

      this.results.analyses = analyses || [];
      this.log(`📈 Found ${analyses.length} total analyses`);
      
      return analyses;
    } catch (error) {
      this.log(`Failed to fetch analyses: ${error.message}`, 'error');
      return [];
    }
  }

  async getAllStorageFiles() {
    this.log('📁 Fetching all storage files...');
    
    try {
      const { data: files, error } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, {
          limit: 1000,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        this.log(`Error fetching storage files: ${error.message}`, 'error');
        return [];
      }

      this.results.storageFiles = files || [];
      this.log(`📁 Found ${files.length} files in storage`);
      
      return files;
    } catch (error) {
      this.log(`Failed to fetch storage files: ${error.message}`, 'error');
      return [];
    }
  }

  async identifyInconsistencies() {
    this.log('🔍 Identifying inconsistencies...');
    
    const analyses = this.results.analyses;
    const storageFiles = this.results.storageFiles;
    const inconsistencies = [];

    // Create a map of storage files for quick lookup
    const storageFileMap = new Map();
    storageFiles.forEach(file => {
      const fullPath = `${this.results.auth.userId}/${file.name}`;
      storageFileMap.set(fullPath, file);
    });

    // Check each analysis
    for (const analysis of analyses) {
      const inconsistency = {
        analysisId: analysis.id,
        filename: analysis.original_filename,
        file_url: analysis.file_url,
        issues: [],
        recommendations: []
      };

      // Issue 1: Analysis has null file_url but should have one
      if (!analysis.file_url) {
        inconsistency.issues.push('NULL_FILE_URL');
        
        // Try to find a matching file in storage
        const potentialMatches = storageFiles.filter(file => 
          file.name.includes(analysis.original_filename.replace(/[^a-zA-Z0-9.-]/g, '_'))
        );
        
        if (potentialMatches.length > 0) {
          inconsistency.recommendations.push({
            action: 'UPDATE_FILE_URL',
            suggestedPath: `${this.results.auth.userId}/${potentialMatches[0].name}`,
            confidence: 'medium'
          });
        } else {
          inconsistency.recommendations.push({
            action: 'MARK_AS_NO_IMAGE',
            reason: 'No matching file found in storage'
          });
        }
      }

      // Issue 2: Analysis has file_url but file doesn't exist in storage
      if (analysis.file_url && !storageFileMap.has(analysis.file_url)) {
        inconsistency.issues.push('MISSING_STORAGE_FILE');
        inconsistency.recommendations.push({
          action: 'CLEAR_FILE_URL',
          reason: 'Referenced file does not exist in storage'
        });
      }

      // Issue 3: Analysis has HTTP URL instead of file path
      if (analysis.file_url && analysis.file_url.startsWith('http')) {
        inconsistency.issues.push('HTTP_URL_FORMAT');
        
        // Try to extract the file path
        try {
          const url = new URL(analysis.file_url);
          const pathSegments = url.pathname.split('/').filter(segment => segment);
          if (pathSegments.length >= 2) {
            const extractedPath = pathSegments.slice(-2).join('/');
            if (storageFileMap.has(extractedPath)) {
              inconsistency.recommendations.push({
                action: 'UPDATE_TO_FILE_PATH',
                suggestedPath: extractedPath,
                confidence: 'high'
              });
            }
          }
        } catch (urlError) {
          inconsistency.recommendations.push({
            action: 'CLEAR_FILE_URL',
            reason: 'Invalid URL format'
          });
        }
      }

      if (inconsistency.issues.length > 0) {
        inconsistencies.push(inconsistency);
      }
    }

    // Check for orphaned files in storage
    const referencedFiles = new Set();
    analyses.forEach(analysis => {
      if (analysis.file_url && !analysis.file_url.startsWith('http')) {
        referencedFiles.add(analysis.file_url);
      }
    });

    storageFiles.forEach(file => {
      const fullPath = `${this.results.auth.userId}/${file.name}`;
      if (!referencedFiles.has(fullPath)) {
        inconsistencies.push({
          analysisId: null,
          filename: file.name,
          file_url: fullPath,
          issues: ['ORPHANED_FILE'],
          recommendations: [{
            action: 'DELETE_ORPHANED_FILE',
            reason: 'File exists in storage but no analysis references it'
          }]
        });
      }
    });

    this.results.inconsistencies = inconsistencies;
    this.log(`🔍 Found ${inconsistencies.length} inconsistencies`);
    
    return inconsistencies;
  }

  async generateCleanupPlan() {
    this.log('📋 Generating cleanup plan...');
    
    const plan = [];
    
    for (const inconsistency of this.results.inconsistencies) {
      for (const recommendation of inconsistency.recommendations) {
        plan.push({
          analysisId: inconsistency.analysisId,
          filename: inconsistency.filename,
          action: recommendation.action,
          details: recommendation,
          priority: this.getPriority(recommendation.action)
        });
      }
    }

    // Sort by priority
    plan.sort((a, b) => a.priority - b.priority);
    
    this.results.cleanupActions = plan;
    this.log(`📋 Generated ${plan.length} cleanup actions`);
    
    return plan;
  }

  getPriority(action) {
    const priorities = {
      'UPDATE_TO_FILE_PATH': 1,  // High priority - fixes existing data
      'UPDATE_FILE_URL': 2,      // Medium priority - restores missing links
      'CLEAR_FILE_URL': 3,       // Low priority - cleanup
      'MARK_AS_NO_IMAGE': 4,     // Low priority - documentation
      'DELETE_ORPHANED_FILE': 5  // Lowest priority - cleanup
    };
    return priorities[action] || 10;
  }

  async executeCleanupPlan(dryRun = true) {
    this.log(`🚀 ${dryRun ? 'Simulating' : 'Executing'} cleanup plan...`);
    
    const results = [];
    
    for (const action of this.results.cleanupActions) {
      try {
        const result = await this.executeAction(action, dryRun);
        results.push(result);
        
        if (!dryRun) {
          // Small delay to avoid overwhelming the database
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        this.log(`❌ Failed to execute action: ${error.message}`, 'error');
        results.push({
          action: action.action,
          analysisId: action.analysisId,
          success: false,
          error: error.message
        });
      }
    }
    
    this.log(`✅ ${dryRun ? 'Simulation' : 'Execution'} complete: ${results.filter(r => r.success).length}/${results.length} successful`);
    return results;
  }

  async executeAction(action, dryRun) {
    const { analysisId, action: actionType, details } = action;
    
    this.log(`${dryRun ? '🧪' : '⚡'} ${actionType}: ${action.filename}`);
    
    if (dryRun) {
      return {
        action: actionType,
        analysisId,
        success: true,
        simulated: true,
        details
      };
    }

    switch (actionType) {
      case 'UPDATE_TO_FILE_PATH':
        const { data, error } = await this.supabase
          .schema('api')
          .from('design_analyses')
          .update({ file_url: details.suggestedPath })
          .eq('id', analysisId);
        
        if (error) throw error;
        return { action: actionType, analysisId, success: true };

      case 'UPDATE_FILE_URL':
        const { data: data2, error: error2 } = await this.supabase
          .schema('api')
          .from('design_analyses')
          .update({ file_url: details.suggestedPath })
          .eq('id', analysisId);
        
        if (error2) throw error2;
        return { action: actionType, analysisId, success: true };

      case 'CLEAR_FILE_URL':
        const { data: data3, error: error3 } = await this.supabase
          .schema('api')
          .from('design_analyses')
          .update({ file_url: null })
          .eq('id', analysisId);
        
        if (error3) throw error3;
        return { action: actionType, analysisId, success: true };

      default:
        return { action: actionType, analysisId, success: false, error: 'Unknown action' };
    }
  }

  async runFullCleanup(dryRun = true) {
    this.log('🚀 Starting full cleanup process...');
    
    // Step 1: Check authentication
    const authOk = await this.checkAuth();
    if (!authOk) {
      this.log('❌ Cannot proceed without authentication', 'error');
      return this.results;
    }

    // Step 2: Get all data
    await this.getAllAnalyses();
    await this.getAllStorageFiles();

    // Step 3: Identify inconsistencies
    await this.identifyInconsistencies();

    // Step 4: Generate cleanup plan
    await this.generateCleanupPlan();

    // Step 5: Execute cleanup (dry run by default)
    const executionResults = await this.executeCleanupPlan(dryRun);

    // Step 6: Generate summary
    this.generateSummary(executionResults, dryRun);
    
    this.log('✅ Full cleanup process complete!');
    return this.results;
  }

  generateSummary(executionResults, dryRun) {
    const summary = {
      totalAnalyses: this.results.analyses.length,
      totalStorageFiles: this.results.storageFiles.length,
      inconsistenciesFound: this.results.inconsistencies.length,
      cleanupActionsPlanned: this.results.cleanupActions.length,
      executionResults: executionResults ? {
        total: executionResults.length,
        successful: executionResults.filter(r => r.success).length,
        failed: executionResults.filter(r => !r.success).length
      } : null,
      dryRun
    };

    this.results.summary = summary;
    
    this.log('📊 CLEANUP SUMMARY:');
    this.log(`  Total analyses: ${summary.totalAnalyses}`);
    this.log(`  Total storage files: ${summary.totalStorageFiles}`);
    this.log(`  Inconsistencies found: ${summary.inconsistenciesFound}`);
    this.log(`  Cleanup actions planned: ${summary.cleanupActionsPlanned}`);
    
    if (summary.executionResults) {
      this.log(`  Execution results (${dryRun ? 'DRY RUN' : 'LIVE'}):`);
      this.log(`    - Successful: ${summary.executionResults.successful}`);
      this.log(`    - Failed: ${summary.executionResults.failed}`);
    }
  }
}

// Create and export the utility
const cleanupUtility = new DatabaseCleanupUtility();
window.cleanupUtility = cleanupUtility;

// Auto-run the cleanup analysis (dry run)
cleanupUtility.runFullCleanup(true).then(results => {
  console.log('🎯 Cleanup analysis complete:', results);
  console.log('💡 Use window.cleanupUtility.runFullCleanup(false) to execute the cleanup plan');
}).catch(error => {
  console.error('❌ Cleanup analysis failed:', error);
});
