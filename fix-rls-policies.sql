-- Fix RLS Policies for Supabase Storage Upload Issues
-- This script fixes the UUID/TEXT comparison issue in storage.objects policies

-- Problem: auth.uid() returns UUID type, but storage.foldername() returns TEXT
-- Solution: Cast auth.uid() to text for proper comparison

-- 1. Drop existing policies
DROP POLICY IF EXISTS "Users can upload their own design analysis images" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own design analysis images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own design analysis images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own design analysis images" ON storage.objects;

-- 2. Create fixed INSERT policy (for uploads)
CREATE POLICY "Users can upload their own design analysis images"
ON storage.objects FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = auth.uid()::text
);

-- 3. Create fixed SELECT policy (for downloads/viewing)
CREATE POLICY "Users can view their own design analysis images"
ON storage.objects FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = auth.uid()::text
);

-- 4. <PERSON>reate fixed UPDATE policy
CREATE POLICY "Users can update their own design analysis images"
ON storage.objects FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = auth.uid()::text
)
WITH CHECK (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = auth.uid()::text
);

-- 5. Create fixed DELETE policy
CREATE POLICY "Users can delete their own design analysis images"
ON storage.objects FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = auth.uid()::text
);

-- 6. Verify policies are created correctly
SELECT 
  policyname, 
  cmd, 
  qual, 
  with_check 
FROM pg_policies 
WHERE tablename = 'objects' 
  AND schemaname = 'storage' 
  AND policyname LIKE '%design analysis%'
ORDER BY cmd;

-- Expected results should show:
-- - INSERT policy with WITH CHECK containing auth.uid()::text
-- - SELECT policy with USING containing auth.uid()::text  
-- - UPDATE policy with both USING and WITH CHECK containing auth.uid()::text
-- - DELETE policy with USING containing auth.uid()::text

-- Alternative approach if the above doesn't work:
-- Sometimes Supabase requires explicit subquery for auth functions

/*
-- Alternative INSERT policy (uncomment if needed)
CREATE POLICY "Users can upload their own design analysis images"
ON storage.objects FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);

-- Alternative SELECT policy (uncomment if needed)
CREATE POLICY "Users can view their own design analysis images"
ON storage.objects FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'design-analysis-images' AND 
  (storage.foldername(name))[1] = (SELECT auth.uid()::text)
);
*/
