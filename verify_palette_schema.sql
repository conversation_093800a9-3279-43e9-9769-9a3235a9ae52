-- Verification script for user_palettes table and RLS policies
-- Run this in your Supabase SQL editor to verify the schema was applied correctly

-- 1. Check if user_palettes table exists and has correct structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_palettes' 
ORDER BY ordinal_position;

-- 2. Check if RLS is enabled on user_palettes table
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE tablename = 'user_palettes';

-- 3. Check RLS policies on user_palettes table
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'user_palettes';

-- 4. Check indexes on user_palettes table
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'user_palettes';

-- 5. Check if the update trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'user_palettes';

-- 6. Check table constraints
SELECT 
    constraint_name,
    constraint_type,
    check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'user_palettes';

-- 7. Test RLS policies with sample data (optional - only run if you want to test)
-- WARNING: This will create test data. Only run in development environment.

/*
-- Insert test data as different users (you'll need to replace with actual user IDs)
-- This should work for the authenticated user
INSERT INTO user_palettes (user_id, name, colors, description) 
VALUES (auth.uid()::text, 'Test Palette', '["#FF5733", "#33FF57", "#3357FF"]', 'Test description');

-- This should fail if RLS is working (trying to insert with different user_id)
INSERT INTO user_palettes (user_id, name, colors, description) 
VALUES ('different-user-id', 'Unauthorized Palette', '["#FF0000"]', 'This should fail');

-- Query to see only your own palettes (should only show palettes for current user)
SELECT id, name, colors, created_at, user_id 
FROM user_palettes;

-- Clean up test data
DELETE FROM user_palettes WHERE name = 'Test Palette';
*/

-- 8. Verify the update_updated_at_column function exists
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name = 'update_updated_at_column';

-- Expected Results Summary:
-- 1. Table should have columns: id, created_at, updated_at, user_id, name, colors, description, tags, is_favorite
-- 2. RLS should be enabled (rowsecurity = true)
-- 3. Should have 4 RLS policies: SELECT, INSERT, UPDATE, DELETE
-- 4. Should have indexes on: user_id, created_at, name, is_favorite
-- 5. Should have trigger: update_user_palettes_updated_at
-- 6. Should have constraints: valid_palette_name, valid_colors_array
-- 7. update_updated_at_column function should exist
