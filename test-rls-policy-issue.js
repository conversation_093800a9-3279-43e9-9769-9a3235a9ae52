/**
 * RLS Policy Issue Test for Visual Complexity Analyzer
 * This script specifically tests for Row Level Security policy violations
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🔒 Testing RLS Policy Issues...');

class RLSPolicyTester {
  constructor() {
    this.results = {
      auth: null,
      policies: null,
      uploadTests: [],
      errors: []
    };
    this.supabase = window.supabase;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`[${timestamp}] ${emoji} ${message}`);
  }

  async checkAuth() {
    this.log('🔐 Checking authentication...');
    
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();
      
      if (error || !user) {
        this.log(`Authentication failed: ${error?.message || 'No user'}`, 'error');
        return false;
      }
      
      this.results.auth = {
        userId: user.id,
        email: user.email,
        role: user.role || 'authenticated'
      };
      
      this.log(`✅ Authenticated as: ${user.email} (${user.id})`);
      this.log(`👤 User role: ${user.role || 'authenticated'}`);
      
      return true;
    } catch (error) {
      this.log(`Auth check failed: ${error.message}`, 'error');
      return false;
    }
  }

  async testDirectStorageAccess() {
    this.log('🪣 Testing direct storage access...');
    
    try {
      // Test 1: List files in user's folder
      const { data: files, error: listError } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, { limit: 1 });

      if (listError) {
        this.log(`❌ List files failed: ${listError.message}`, 'error');
        this.results.errors.push(`List error: ${listError.message}`);
        return false;
      }

      this.log(`✅ List files successful: ${files.length} files found`);

      // Test 2: Try to upload a minimal file
      const testBlob = new Blob(['test'], { type: 'text/plain' });
      const testFile = new File([testBlob], 'rls-test.txt', { type: 'text/plain' });
      const testPath = `${this.results.auth.userId}/rls-test-${Date.now()}.txt`;

      this.log(`📤 Testing upload to path: ${testPath}`);

      const { data: uploadData, error: uploadError } = await this.supabase.storage
        .from('design-analysis-images')
        .upload(testPath, testFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        this.log(`❌ Direct upload failed: ${uploadError.message}`, 'error');
        this.log(`🔍 Error details:`, 'error');
        this.log(`  - Code: ${uploadError.code || 'N/A'}`, 'error');
        this.log(`  - Status: ${uploadError.status || 'N/A'}`, 'error');
        this.log(`  - Details: ${uploadError.details || 'N/A'}`, 'error');
        
        if (uploadError.message.includes('row-level security policy')) {
          this.log('🚨 RLS POLICY VIOLATION CONFIRMED', 'error');
          this.results.errors.push('RLS Policy Violation: INSERT policy failed');
        }
        
        return false;
      }

      this.log(`✅ Direct upload successful: ${uploadData.path}`);

      // Clean up test file
      await this.supabase.storage
        .from('design-analysis-images')
        .remove([testPath]);

      return true;
    } catch (error) {
      this.log(`❌ Storage access test failed: ${error.message}`, 'error');
      this.results.errors.push(`Storage access error: ${error.message}`);
      return false;
    }
  }

  async testImageUpload() {
    this.log('🖼️ Testing image upload specifically...');
    
    try {
      // Create a proper image file
      const canvas = document.createElement('canvas');
      canvas.width = 10;
      canvas.height = 10;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 10, 10);

      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
      const imageFile = new File([blob], 'rls-test-image.png', { type: 'image/png' });

      this.log(`📄 Created test image: ${imageFile.name} (${imageFile.size} bytes)`);

      // Test with the exact same path format as the service
      const timestamp = Date.now();
      const sanitizedName = imageFile.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const fileName = `${this.results.auth.userId}/${timestamp}_${sanitizedName}`;

      this.log(`📝 Using filename format: ${fileName}`);

      const { data, error } = await this.supabase.storage
        .from('design-analysis-images')
        .upload(fileName, imageFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        this.log(`❌ Image upload failed: ${error.message}`, 'error');
        this.log(`🔍 Full error object:`, 'error');
        console.error(error);
        
        // Analyze the error
        if (error.message.includes('row-level security policy')) {
          this.log('🚨 RLS POLICY VIOLATION ON IMAGE UPLOAD', 'error');
          this.results.errors.push('RLS Policy Violation: Image upload INSERT policy failed');
        } else if (error.message.includes('Duplicate')) {
          this.log('🔄 Duplicate file error - trying with different name', 'warn');
          // Try again with different timestamp
          const newFileName = `${this.results.auth.userId}/${Date.now() + 1000}_${sanitizedName}`;
          const { data: retryData, error: retryError } = await this.supabase.storage
            .from('design-analysis-images')
            .upload(newFileName, imageFile, {
              cacheControl: '3600',
              upsert: false
            });
          
          if (retryError) {
            this.log(`❌ Retry upload also failed: ${retryError.message}`, 'error');
            return false;
          } else {
            this.log(`✅ Retry upload successful: ${retryData.path}`);
            // Clean up
            await this.supabase.storage
              .from('design-analysis-images')
              .remove([newFileName]);
            return true;
          }
        }
        
        return false;
      }

      this.log(`✅ Image upload successful: ${data.path}`);

      // Clean up
      await this.supabase.storage
        .from('design-analysis-images')
        .remove([fileName]);

      return true;
    } catch (error) {
      this.log(`❌ Image upload test failed: ${error.message}`, 'error');
      this.results.errors.push(`Image upload error: ${error.message}`);
      return false;
    }
  }

  async testServiceMethod() {
    this.log('🔧 Testing designAnalysisService.uploadImage...');
    
    try {
      if (!window.designAnalysisService) {
        this.log('❌ designAnalysisService not available', 'error');
        return false;
      }

      // Create test image
      const canvas = document.createElement('canvas');
      canvas.width = 5;
      canvas.height = 5;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#00FF00';
      ctx.fillRect(0, 0, 5, 5);

      const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
      const imageFile = new File([blob], 'service-test.png', { type: 'image/png' });

      const result = await window.designAnalysisService.uploadImage(imageFile, this.results.auth.userId);
      
      this.log(`✅ Service upload successful: ${result}`);
      
      // Clean up
      await this.supabase.storage
        .from('design-analysis-images')
        .remove([result]);

      return true;
    } catch (error) {
      this.log(`❌ Service upload failed: ${error.message}`, 'error');
      this.results.errors.push(`Service upload error: ${error.message}`);
      return false;
    }
  }

  async runFullTest() {
    this.log('🚀 Starting RLS Policy Test...');
    
    // Step 1: Check authentication
    const authOk = await this.checkAuth();
    if (!authOk) {
      this.log('❌ Cannot proceed without authentication', 'error');
      return this.results;
    }

    // Step 2: Test direct storage access
    const storageOk = await this.testDirectStorageAccess();
    
    // Step 3: Test image upload specifically
    const imageOk = await this.testImageUpload();
    
    // Step 4: Test service method
    const serviceOk = await this.testServiceMethod();

    // Step 5: Generate summary
    this.generateSummary(storageOk, imageOk, serviceOk);
    
    this.log('✅ RLS Policy test complete!');
    return this.results;
  }

  generateSummary(storageOk, imageOk, serviceOk) {
    this.log('📊 RLS POLICY TEST SUMMARY:');
    this.log(`  Authentication: ${this.results.auth ? '✅ Success' : '❌ Failed'}`);
    this.log(`  Direct Storage Access: ${storageOk ? '✅ Success' : '❌ Failed'}`);
    this.log(`  Image Upload Test: ${imageOk ? '✅ Success' : '❌ Failed'}`);
    this.log(`  Service Method Test: ${serviceOk ? '✅ Success' : '❌ Failed'}`);
    
    if (this.results.errors.length > 0) {
      this.log('  🚨 ERRORS FOUND:');
      this.results.errors.forEach(error => {
        this.log(`    - ${error}`);
      });
    }

    // Diagnosis
    if (!storageOk && !imageOk && !serviceOk) {
      this.log('🎯 DIAGNOSIS: RLS policies are blocking ALL uploads', 'error');
      this.log('💡 SOLUTION: Check storage.objects INSERT policy for authenticated users', 'warn');
    } else if (storageOk && !imageOk) {
      this.log('🎯 DIAGNOSIS: Text files work but images fail - check MIME type restrictions', 'error');
    } else if (storageOk && imageOk && !serviceOk) {
      this.log('🎯 DIAGNOSIS: Direct uploads work but service method fails - check service implementation', 'error');
    } else if (storageOk && imageOk && serviceOk) {
      this.log('🎯 DIAGNOSIS: All tests pass - issue may be intermittent or context-specific', 'success');
    }
  }
}

// Create and run the tester
const rlsTester = new RLSPolicyTester();

// Export to global scope
window.rlsTester = rlsTester;

// Auto-run the test
rlsTester.runFullTest().then(results => {
  console.log('🎯 RLS Policy test results:', results);
  console.log('💡 Use window.rlsTester.runFullTest() to run again');
}).catch(error => {
  console.error('❌ RLS Policy test failed:', error);
});
