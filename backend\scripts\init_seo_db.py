#!/usr/bin/env python3
"""
Initialize SEO Analysis Database
Creates the necessary tables for persistent SEO analysis tracking
"""

import sys
import os
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy import create_engine, text
from app.db.models import Base, SEOAnalysis
from app.db.session import get_database_url

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_tables():
    """Create all tables in the database."""
    try:
        # Get database URL
        database_url = get_database_url()
        logger.info(f"Connecting to database...")
        
        # Create engine
        engine = create_engine(database_url)
        
        # Create all tables
        logger.info("Creating tables...")
        Base.metadata.create_all(bind=engine)
        
        # Verify tables were created (SQLite compatible)
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='seo_analyses'
            """))

            if result.fetchone():
                logger.info("✅ SEO analyses table created successfully")
            else:
                logger.error("❌ Failed to create SEO analyses table")
                return False
        
        logger.info("✅ Database initialization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {str(e)}")
        return False


def test_database_connection():
    """Test the database connection."""
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                logger.info("✅ Database connection test successful")
                return True
        
        return False
        
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {str(e)}")
        return False


def main():
    """Main function."""
    logger.info("🚀 Starting SEO Analysis Database Initialization")
    
    # Test database connection first
    if not test_database_connection():
        logger.error("❌ Cannot connect to database. Please check your configuration.")
        sys.exit(1)
    
    # Create tables
    if not create_tables():
        logger.error("❌ Failed to initialize database")
        sys.exit(1)
    
    logger.info("🎉 SEO Analysis Database initialization completed successfully!")
    logger.info("You can now use the persistent SEO analysis features.")


if __name__ == "__main__":
    main()
