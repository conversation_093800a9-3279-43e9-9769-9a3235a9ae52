"""
API endpoints for search and recolor (change colors) functionality.
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from app.core.auth import verify_api_key
from app.schemas.search_recolor import SearchRecolorRequest, FrontendSearchRecolorResponse
from app.services.search_recolor_service import search_recolor_objects_stability

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/search-recolor",
    response_model=FrontendSearchRecolorResponse,
    dependencies=[Depends(verify_api_key)],
    summary="Change colors of objects in image using AI",
    description="""
    Change the colors of specific objects in an image using Stability AI v2beta search and recolor API.

    **Supported Image Formats:** JPEG, PNG, WebP
    **Maximum File Size:** 10MB
    **Image Dimensions:** Minimum 64x64, maximum 9,437,184 pixels total
    **Aspect Ratio:** Between 1:2.5 and 2.5:1
    **Cost:** 5 credits per successful generation

    This endpoint automatically identifies and recolors objects in images without requiring a mask.
    Simply specify what object to find (select_prompt) and what colors to apply (prompt).

    **Examples:**
    - Select: "car" → Recolor: "bright red car"
    - Select: "shirt" → Recolor: "blue striped shirt"
    - Select: "flowers" → Recolor: "purple and pink flowers"

    **Tips:**
    - Use specific color descriptions for better results
    - The select_prompt should be simple and clear (e.g., "car", "shirt", "flowers")
    - The color prompt should be detailed and descriptive
    - Use negative prompts to avoid unwanted color changes
    - Adjust grow_mask to control the recoloring area size
    """
)
async def search_recolor_objects_endpoint(
    image: UploadFile = File(..., description="Image file to process"),
    prompt: str = Form(..., description="Detailed description of the desired colors and appearance"),
    select_prompt: str = Form(..., description="Simple description of what object to find and recolor (e.g., 'car', 'shirt', 'flowers')"),
    negative_prompt: Optional[str] = Form(None, description="Text describing what colors or effects NOT to include"),
    grow_mask: Optional[int] = Form(3, description="Grow mask edges (0-20 pixels)"),
    seed: Optional[int] = Form(0, description="Random seed (0 = random)"),
    output_format: Optional[str] = Form("png", description="Output format (jpeg, png, webp)"),
    style_preset: Optional[str] = Form(None, description="Style preset for the generation")
) -> FrontendSearchRecolorResponse:
    """
    Change colors of objects in an image using Stability AI v2beta search and recolor API.
    
    This endpoint automatically identifies objects based on the select_prompt and recolors them
    with colors described in the prompt, without requiring manual mask creation.
    """
    try:
        logger.info(f"Received search and recolor request for select: '{select_prompt}' → recolor with: '{prompt[:50]}...'")
        logger.info(f"Grow mask: {grow_mask}, Output format: {output_format}")
        
        # Validar archivo de imagen
        if not image.filename:
            raise HTTPException(status_code=400, detail="Image filename is required")

        # Validar prompts requeridos
        if not prompt or not prompt.strip():
            raise HTTPException(
                status_code=400,
                detail="Prompt is required and cannot be empty"
            )
            
        if not select_prompt or not select_prompt.strip():
            raise HTTPException(
                status_code=400,
                detail="Select prompt is required and cannot be empty"
            )

        # Validar grow_mask
        if grow_mask is not None and (grow_mask < 0 or grow_mask > 20):
            raise HTTPException(
                status_code=400,
                detail="grow_mask must be between 0 and 20"
            )

        # Validar seed
        if seed is not None and (seed < 0 or seed > 4294967294):
            raise HTTPException(
                status_code=400,
                detail="seed must be between 0 and 4294967294"
            )

        # Validar output_format
        if output_format not in ["jpeg", "png", "webp"]:
            raise HTTPException(
                status_code=400,
                detail="output_format must be 'jpeg', 'png', or 'webp'"
            )

        # Crear request object
        search_recolor_request = SearchRecolorRequest(
            prompt=prompt.strip(),
            select_prompt=select_prompt.strip(),
            negative_prompt=negative_prompt.strip() if negative_prompt and negative_prompt.strip() else None,
            grow_mask=grow_mask or 3,
            seed=seed or 0,
            output_format=output_format or "png",
            style_preset=style_preset
        )

        # Llamar al servicio de Stability AI
        service_response = await search_recolor_objects_stability(
            image_file=image,
            request=search_recolor_request
        )

        # Crear data URL para el frontend (igual que otras implementaciones)
        mime_type = f"image/{output_format or 'png'}"
        image_data_url = f"data:{mime_type};base64,{service_response.image}"

        logger.info(f"Search and recolor operation completed successfully. Result size: {len(image_data_url)} chars")

        return FrontendSearchRecolorResponse(
            success=True,
            image_url=image_data_url,
            seed=service_response.seed,
            finish_reason=service_response.finish_reason,
            metadata={
                "prompt": prompt,
                "select_prompt": select_prompt,
                "negative_prompt": negative_prompt,
                "grow_mask": grow_mask,
                "seed": seed,
                "output_format": output_format,
                "style_preset": style_preset,
                "original_filename": image.filename
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in search and recolor operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during search and recolor operation: {str(e)}"
        )
