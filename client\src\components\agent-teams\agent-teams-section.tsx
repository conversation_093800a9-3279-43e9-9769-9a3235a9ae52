import React from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { ArrowR<PERSON>, Users, Sparkles } from "lucide-react";

import { AgentTeam, agentTeams } from "@/data/agent-teams-data";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

// Variantes para animaciones
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
      delay: i * 0.1,
    },
  }),
};

interface AgentTeamsSectionProps {
  className?: string;
}

export default function AgentTeamsSection({
  className = "",
}: AgentTeamsSectionProps) {
  const [_, setLocation] = useLocation();

  // Función para navegar al equipo de agentes seleccionado
  const handleTeamSelect = (team: AgentTeam) => {
    // En una implementación real, esto debería llevar a una página de conversación
    // con el equipo de agentes. Por ahora, simulemos redirigiendo a una página.
    setLocation(`/agentes/${team.id}`);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Título de la sección con efectos visuales */}
      <div className="flex items-center mb-6 relative">
        <h2 className="text-2xl font-extrabold inline-block py-2 px-4 bg-purple-500 text-white border-4 border-black rounded-lg shadow-[5px_5px_0px_0px_rgba(0,0,0,1)] transform -rotate-1">
          Equipos de Agentes Expertos
        </h2>
        <Badge className="ml-3 bg-gradient-to-r from-purple-500 to-pink-500 py-1 px-3 rounded-full text-white border-none">
          <Sparkles className="h-3.5 w-3.5 mr-1" />
          NUEVO
        </Badge>
      </div>

      {/* Elementos decorativos de fondo */}
      <div className="absolute -inset-10 -z-10">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={`bg-agent-element-${i}`}
            className="absolute rounded-full bg-purple-500/5"
            style={{
              width: Math.random() * 80 + 20,
              height: Math.random() * 80 + 20,
              top: Math.random() * 100 + "%",
              left: Math.random() * 100 + "%",
              zIndex: -1,
            }}
            animate={{
              y: [0, -40, 0],
              x: [0, Math.random() * 30 - 15, 0],
              opacity: [0.3, 0.7, 0.3],
              scale: [1, 1.4, 1],
            }}
            transition={{
              duration: 7 + Math.random() * 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Grid de equipos de agentes */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {agentTeams.map((team, index) => (
          <motion.div
            key={team.id}
            className="card-3d"
            custom={index}
            variants={itemVariants}
            whileHover={{
              scale: 1.03,
              rotate: index % 2 === 0 ? 1 : -1,
              transition: { duration: 0.3 },
            }}
          >
            <div
              className={`relative rounded-2xl overflow-hidden p-0.5 ${team.gradientClass}`}
            >
              <div className="absolute inset-0 animate-pulse-slow opacity-75"></div>
              <Card className="card-agent-team h-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-none">
                <motion.div
                  className="p-6 flex flex-col h-full"
                  whileHover={{ z: 30 }}
                >
                  {/* Cabecera del equipo */}
                  <div className="flex items-start justify-between mb-4">
                    <div
                      className={`icon-container w-16 h-16 rounded-xl flex items-center justify-center`}
                    >
                      <team.icon className="h-8 w-8 text-white" />
                    </div>
                    <Badge className="bg-black/10 text-black dark:bg-white/10 dark:text-white flex items-center py-1">
                      <Users className="h-3 w-3 mr-1" />
                      {team.agents.length} agentes
                    </Badge>
                  </div>

                  {/* Información del equipo */}
                  <h3 className="font-bold text-xl mb-2">{team.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    {team.description}
                  </p>

                  {/* Avatares de los agentes */}
                  <div className="mb-4 flex -space-x-3">
                    {team.agents.map((agent, idx) => (
                      <div
                        key={agent.id}
                        className="relative w-10 h-10 rounded-full border-2 border-white dark:border-gray-900 overflow-hidden bg-gray-100"
                        style={{ zIndex: 10 - idx }}
                      >
                        <img
                          src={
                            agent.avatar || `/placeholder-agent-${idx + 1}.png`
                          }
                          alt={agent.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = `/placeholder-agent-${idx + 1}.png`;
                          }}
                        />
                      </div>
                    ))}
                  </div>

                  {/* Especialidades */}
                  <div className="mb-4">
                    <p className="text-xs text-muted-foreground mb-2">
                      Especialidades:
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {team.specialties.map((specialty) => (
                        <Badge
                          key={specialty}
                          variant="outline"
                          className="text-xs"
                        >
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Botón de acción */}
                  <div className="mt-auto pt-4 border-t border-gray-100 dark:border-gray-800">
                    <Button
                      className={`w-full rounded-xl py-6 text-white font-medium ${team.gradientClass}`}
                      onClick={() => handleTeamSelect(team)}
                    >
                      <span className="mr-2">Trabajar con este equipo</span>
                      <ArrowRight className="h-4 w-4 animate-bounce-slow" />
                    </Button>
                  </div>
                </motion.div>
              </Card>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}
