#!/usr/bin/env python3
"""
Test script para probar CloudBrowser con Browserless.io
"""

import sys
import os
import asyncio

# Agregar el path de agenticseek
sys.path.append(os.path.join(os.path.dirname(__file__), 'app', 'agenticseek'))

from app.agenticseek.sources.browser import CloudBrowser

async def test_cloud_browser():
    """Test básico del CloudBrowser"""
    
    # API key de Browserless.io
    api_key = "2SP6LRG5ebh7ohfc3b60651a5f2f574bc95ee4e3c8caf2a58"
    
    print("🚀 Iniciando test de CloudBrowser...")
    
    try:
        # Crear instancia del CloudBrowser
        print("📱 Creando CloudBrowser...")
        browser = CloudBrowser(api_key)
        
        # Test 1: Navegar a Google
        print("🌐 Test 1: Navegando a Google...")
        success = browser.go_to("https://www.google.com")
        if success:
            print("✅ Navegación exitosa!")
            
            # Obtener título de la página
            title = browser.get_page_title()
            print(f"📄 Título de la página: {title}")
            
            # Obtener texto de la página
            text = browser.get_text()
            if text:
                print(f"📝 Texto extraído (primeros 200 chars): {text[:200]}...")
            
            # Verificar screenshot
            screenshot_path = browser.get_screenshot()
            if os.path.exists(screenshot_path):
                print(f"📸 Screenshot guardado en: {screenshot_path}")
            else:
                print("❌ No se encontró el screenshot")
                
        else:
            print("❌ Error navegando a Google")
            
        # Test 2: Navegar a una página de noticias
        print("\n🌐 Test 2: Navegando a BBC News...")
        success = browser.go_to("https://www.bbc.com/news")
        if success:
            print("✅ Navegación a BBC exitosa!")
            
            # Obtener links navegables
            links = browser.get_navigable()
            print(f"🔗 Links encontrados: {len(links)}")
            if links:
                print(f"🔗 Primeros 3 links: {links[:3]}")
                
        else:
            print("❌ Error navegando a BBC")
            
        print("\n🎉 Test completado!")
        
    except Exception as e:
        print(f"❌ Error durante el test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cloud_browser())
