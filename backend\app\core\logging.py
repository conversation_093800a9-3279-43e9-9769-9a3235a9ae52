
import logging
import sys
import os
from typing import Any
import json
from datetime import datetime

class J<PERSON>NFormatter(logging.Formatter):
    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
        }
        if hasattr(record, "request_id"):
            log_data["request_id"] = record.request_id
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        return json.dumps(log_data)

def setup_logging() -> logging.Logger:
    """
    Set up logging with environment-specific configuration.

    In development: More verbose logging with DEBUG level
    In production: More concise logging with INFO level

    Returns:
        Configured logger instance
    """
    # Get environment and log level from settings or environment variables
    environment = os.getenv("ENVIRONMENT", "development")
    log_level_str = os.getenv("LOG_LEVEL", "INFO" if environment == "production" else "DEBUG")

    # Convert string log level to logging constant
    log_level = getattr(logging, log_level_str.upper(), logging.INFO)

    # Create and configure logger
    logger = logging.getLogger("emma_studio")
    logger.setLevel(log_level)

    # Remove existing handlers if any
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create and configure handler
    handler = logging.StreamHandler(sys.stdout)

    # Use different formatters based on environment
    if environment == "production":
        handler.setFormatter(JSONFormatter())
    else:
        # More readable format for development
        formatter = logging.Formatter(
            "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s"
        )
        handler.setFormatter(formatter)

    logger.addHandler(handler)

    # Log startup information
    logger.info(f"Logging configured: environment={environment}, level={log_level_str}")

    return logger
