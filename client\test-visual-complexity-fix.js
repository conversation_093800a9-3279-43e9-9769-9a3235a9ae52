/**
 * Test script to verify the Visual Complexity Analyzer critical fixes
 * Run this in the browser console to test all fixed functionality
 */

console.log('🧪 Testing Visual Complexity Analyzer critical fixes...');

// Test 1: Check if the component is rendered without errors
function testComponentRendering() {
  console.log('\n📋 Test 1: Component Rendering');
  
  try {
    // Look for the Visual Complexity Analyzer component
    const analyzerComponent = document.querySelector('[data-testid="visual-complexity-analyzer"], .design-complexity-analyzer, [class*="complexity"]');
    
    if (analyzerComponent) {
      console.log('✅ Visual Complexity Analyzer component found');
      return true;
    } else {
      console.log('❌ Visual Complexity Analyzer component not found');
      return false;
    }
  } catch (error) {
    console.log('❌ Error checking component:', error.message);
    return false;
  }
}

// Test 2: Check for React errors in console
function testForReactErrors() {
  console.log('\n🔍 Test 2: React Error Check');
  
  // Check if there are any React error boundaries triggered
  const errorElements = document.querySelectorAll('[data-react-error], .react-error, [class*="error"]');
  
  if (errorElements.length === 0) {
    console.log('✅ No React error boundaries detected');
    return true;
  } else {
    console.log('❌ React error boundaries found:', errorElements.length);
    return false;
  }
}

// Test 3: Check if tabs are functional
function testTabFunctionality() {
  console.log('\n📑 Test 3: Tab Functionality');
  
  try {
    // Look for tab elements
    const tabs = document.querySelectorAll('[role="tablist"] button, .tab, [data-value]');
    
    if (tabs.length > 0) {
      console.log(`✅ Found ${tabs.length} tabs`);
      
      // Check for History and Favorites tabs specifically
      const historyTab = Array.from(tabs).find(tab => 
        tab.textContent && tab.textContent.includes('Historial')
      );
      const favoritesTab = Array.from(tabs).find(tab => 
        tab.textContent && tab.textContent.includes('Favoritos')
      );
      
      if (historyTab && favoritesTab) {
        console.log('✅ History and Favorites tabs found');
        
        // Check if tab counters are displayed
        const historyCount = historyTab.textContent.match(/\((\d+)\)/);
        const favoritesCount = favoritesTab.textContent.match(/\((\d+)\)/);
        
        if (historyCount && favoritesCount) {
          console.log(`✅ Tab counters working - History: ${historyCount[1]}, Favorites: ${favoritesCount[1]}`);
          return true;
        } else {
          console.log('⚠️ Tab counters not found or not working properly');
          return false;
        }
      } else {
        console.log('❌ History or Favorites tabs not found');
        return false;
      }
    } else {
      console.log('❌ No tabs found');
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing tabs:', error.message);
    return false;
  }
}

// Test 4: Check authentication state
function testAuthenticationState() {
  console.log('\n🔐 Test 4: Authentication State Check');

  try {
    // Look for authentication debug logs in console
    console.log('📝 Check console for "🔐 Visual Complexity Analyzer - Auth State" logs');

    // Check if tabs are enabled/disabled correctly
    const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button')).find(tab =>
      tab.textContent && tab.textContent.includes('Historial')
    );
    const favoritesTab = Array.from(document.querySelectorAll('[role="tablist"] button')).find(tab =>
      tab.textContent && tab.textContent.includes('Favoritos')
    );

    if (historyTab && favoritesTab) {
      const historyDisabled = historyTab.hasAttribute('disabled') || historyTab.getAttribute('aria-disabled') === 'true';
      const favoritesDisabled = favoritesTab.hasAttribute('disabled') || favoritesTab.getAttribute('aria-disabled') === 'true';

      console.log(`📊 Tab states - History disabled: ${historyDisabled}, Favorites disabled: ${favoritesDisabled}`);

      if (!historyDisabled && !favoritesDisabled) {
        console.log('✅ Authentication working - tabs are enabled');
        return true;
      } else {
        console.log('⚠️ Authentication issue - tabs are disabled');
        return false;
      }
    } else {
      console.log('❌ Could not find History/Favorites tabs');
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing authentication:', error.message);
    return false;
  }
}

// Test 5: Check favorite button functionality
function testFavoriteButtons() {
  console.log('\n❤️ Test 5: Favorite Button Functionality');

  try {
    // Navigate to history tab first
    const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button')).find(tab =>
      tab.textContent && tab.textContent.includes('Historial')
    );

    if (historyTab) {
      historyTab.click();

      setTimeout(() => {
        // Look for favorite buttons (heart icons)
        const favoriteButtons = document.querySelectorAll('button svg[class*="heart"], button svg[class*="Heart"]');

        if (favoriteButtons.length > 0) {
          console.log(`✅ Found ${favoriteButtons.length} favorite buttons`);

          // Check if buttons are clickable
          const firstButton = favoriteButtons[0].closest('button');
          if (firstButton && !firstButton.disabled) {
            console.log('✅ Favorite buttons are clickable');
            console.log('💡 Click a heart icon to test favorite toggle functionality');
            return true;
          } else {
            console.log('❌ Favorite buttons are disabled');
            return false;
          }
        } else {
          console.log('❌ No favorite buttons found');
          return false;
        }
      }, 1000);
    } else {
      console.log('❌ Cannot access history tab');
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing favorite buttons:', error.message);
    return false;
  }

  return true;
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running Visual Complexity Analyzer Fix Tests...\n');

  const results = {
    componentRendering: testComponentRendering(),
    reactErrors: testForReactErrors(),
    tabFunctionality: testTabFunctionality(),
    authenticationState: testAuthenticationState(),
    favoriteButtons: testFavoriteButtons()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} - ${test}`);
  });
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! The initialization fix is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the specific issues above.');
  }
  
  return results;
}

// Auto-run tests when script loads
setTimeout(() => {
  runAllTests();
}, 2000); // Wait 2 seconds for component to load

// Export for manual testing
window.testVisualComplexityFix = runAllTests;

console.log('📋 Test script loaded. Tests will run automatically in 2 seconds.');
console.log('💡 You can also run tests manually with: testVisualComplexityFix()');
