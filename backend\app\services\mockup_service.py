"""
Service for creating product mockups using <PERSON>'s exclusive AI technology.
Takes a product image and places it in realistic contexts (hands, environments, etc.).
"""

import logging
import base64
from typing import Dict, Any, List
from fastapi import UploadFile

logger = logging.getLogger(__name__)


class MockupService:
    """Service for creating product mockups using <PERSON>'s exclusive AI technology."""
    
    def __init__(self):
        # Import ideogram service
        from app.services.ideogram_service import ideogram_service
        self.ideogram_service = ideogram_service
        
        # Predefined mockup contexts
        self.mockup_contexts = {
            "hands": {
                "name": "En Manos de Usuario",
                "description": "Producto siendo usado por una persona",
                "prompt_template": "Professional product photography showing {product_description} being held and used by hands in a natural, realistic way. Clean background, good lighting, lifestyle photography style, commercial quality."
            },
            "desk": {
                "name": "En Escritorio",
                "description": "Producto en un ambiente de trabajo profesional",
                "prompt_template": "Professional product photography of {product_description} placed on a clean, modern desk workspace. Minimalist office environment, natural lighting, professional business setting, high-end commercial photography."
            },
            "lifestyle": {
                "name": "Estilo de Vida",
                "description": "Producto en contexto de uso cotidiano",
                "prompt_template": "Lifestyle product photography showing {product_description} in a natural daily life setting. Realistic home environment, warm lighting, authentic usage context, aspirational lifestyle photography."
            },
            "outdoor": {
                "name": "Ambiente Exterior",
                "description": "Producto en contexto al aire libre",
                "prompt_template": "Outdoor product photography of {product_description} in a natural outdoor setting. Beautiful landscape background, natural lighting, adventure or outdoor lifestyle context, professional commercial photography."
            },
            "studio": {
                "name": "Estudio Profesional",
                "description": "Producto en estudio con iluminación profesional",
                "prompt_template": "High-end studio product photography of {product_description} with professional lighting setup. Clean white or gradient background, dramatic lighting, commercial advertising style, premium product presentation."
            },
            "social": {
                "name": "Contexto Social",
                "description": "Producto siendo compartido o usado en grupo",
                "prompt_template": "Social product photography showing {product_description} being shared or used in a group setting. People interacting with the product, social context, warm and friendly atmosphere, lifestyle commercial photography."
            }
        }
        
    async def generate_mockup(
        self,
        product_image: UploadFile,
        context: str,
        product_description: str = "",
        size: str = "1024x1024",
        variations: int = 4
    ) -> Dict[str, Any]:
        """
        Generate multiple product mockup variations using Emma's exclusive AI technology.

        Args:
            product_image: The product image to place in context
            context: The mockup context (hands, desk, lifestyle, etc.)
            product_description: Description of the product
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            variations: Number of variations to generate (minimum 4)

        Returns:
            Dict with success status, image data, and metadata for all variations
        """
        if context not in self.mockup_contexts:
            logger.error(f"Invalid mockup context: {context}")
            return {"success": False, "error": f"Invalid context: {context}"}
            
        # Ensure minimum 4 variations
        variations = max(4, variations)

        try:
            # Get context configuration
            context_config = self.mockup_contexts[context]

            # Generate multiple variations with different prompt variations
            variation_results = []

            for i in range(variations):
                # Create variation-specific prompts for diversity
                base_prompt = context_config["prompt_template"].format(
                    product_description=product_description or "the product"
                )

                # Add variation-specific elements for diversity
                variation_elements = [
                    "with dramatic lighting and professional shadows",
                    "with soft natural lighting and warm atmosphere",
                    "with modern minimalist composition and clean aesthetics",
                    "with dynamic angles and creative perspective",
                    "with premium luxury feel and high-end presentation",
                    "with lifestyle authenticity and natural usage context"
                ]

                variation_element = variation_elements[i % len(variation_elements)]
                enhanced_prompt = f"{base_prompt} {variation_element}"

                # Add consistent quality requirements
                enhanced_prompt += f"""

PRODUCT INTEGRATION REQUIREMENTS:
- Seamlessly integrate the product from the reference image into the scene
- Maintain the product's original design, colors, and branding
- Ensure the product looks natural and realistic in the new context
- Keep proper scale and proportions relative to the environment
- Use professional commercial photography lighting and composition
- Make the product the focal point while maintaining context authenticity

QUALITY STANDARDS:
- High-resolution commercial photography quality
- Professional lighting and shadows
- Realistic textures and materials
- Clean, polished final result suitable for marketing use

VARIATION {i+1}: {variation_element}
"""

                logger.info(f"🎨 Generating {context} mockup variation {i+1}/{variations} with Emma's AI technology...")

                # Reset file pointer for each generation
                await product_image.seek(0)

                # Use Ideogram with reference image
                result = await self.ideogram_service.generate_with_reference(
                    prompt=enhanced_prompt,
                    reference_image=product_image,
                    size=size
                )

                if result.get("success"):
                    # Add mockup-specific metadata
                    if "metadata" not in result:
                        result["metadata"] = {}

                    result["metadata"].update({
                        "mockup_context": context,
                        "context_name": context_config["name"],
                        "context_description": context_config["description"],
                        "product_description": product_description,
                        "service_type": "product_mockup",
                        "model": "emma-ai-exclusive",
                        "variation_number": i + 1,
                        "total_variations": variations,
                        "variation_style": variation_element
                    })

                    variation_results.append(result)
                else:
                    logger.warning(f"Failed to generate variation {i+1}: {result.get('error', 'Unknown error')}")

            # Return results
            if variation_results:
                return {
                    "success": True,
                    "variations": variation_results,
                    "total_generated": len(variation_results),
                    "context": context,
                    "context_name": context_config["name"],
                    "product_description": product_description,
                    "metadata": {
                        "service_type": "product_mockup_variations",
                        "model": "emma-ai-exclusive",
                        "requested_variations": variations,
                        "successful_variations": len(variation_results)
                    }
                }
            else:
                return {"success": False, "error": "Failed to generate any variations"}
            
        except Exception as e:
            logger.error(f"Error generating mockup: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}
    
    def get_available_contexts(self) -> Dict[str, Dict[str, str]]:
        """
        Get all available mockup contexts.
        
        Returns:
            Dict with context information
        """
        return {
            context_id: {
                "name": config["name"],
                "description": config["description"]
            }
            for context_id, config in self.mockup_contexts.items()
        }
    
    async def generate_multiple_mockups(
        self,
        product_image: UploadFile,
        contexts: List[str],
        product_description: str = "",
        size: str = "1024x1024"
    ) -> Dict[str, Any]:
        """
        Generate multiple mockups with different contexts.
        
        Args:
            product_image: The product image to place in context
            contexts: List of mockup contexts to generate
            product_description: Description of the product
            size: Image size
            
        Returns:
            Dict with results for each context
        """
        results = {}
        
        for context in contexts:
            if context in self.mockup_contexts:
                # Read image content for each generation
                await product_image.seek(0)  # Reset file pointer
                result = await self.generate_mockup(
                    product_image=product_image,
                    context=context,
                    product_description=product_description,
                    size=size
                )
                results[context] = result
            else:
                results[context] = {
                    "success": False, 
                    "error": f"Invalid context: {context}"
                }
                
        return {
            "success": True,
            "results": results,
            "total_generated": len([r for r in results.values() if r.get("success")]),
            "contexts_processed": list(contexts)
        }


# Global service instance
mockup_service = MockupService()
