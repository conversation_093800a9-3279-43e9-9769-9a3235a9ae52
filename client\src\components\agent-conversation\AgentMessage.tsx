import React from "react";
import {
  AgentMessage as AgentMessageType,
  Agent,
  MessageType,
} from "@shared/agent-types";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface AgentMessageProps {
  message: AgentMessageType;
  agent: Agent;
}

export function AgentMessage({ message, agent }: AgentMessageProps) {
  // Formatear la hora del mensaje
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Determinar el estilo basado en el tipo de mensaje
  const getTypeStyles = () => {
    switch (message.type) {
      case MessageType.THINKING:
        return "border-dashed border-gray-300 bg-gray-50 text-gray-500 italic";
      case MessageType.SYSTEM:
        return "border-blue-200 bg-blue-50 font-medium";
      case MessageType.ERROR:
        return "border-red-200 bg-red-50 text-red-700";
      case MessageType.TIMEOUT:
        return "border-orange-200 bg-orange-50 text-orange-700";
      case MessageType.USER:
        return "border-purple-200 bg-purple-50";
      default:
        return "";
    }
  };

  // Color de fondo para el avatar basado en el color del agente o un color por defecto
  const avatarBgColor = agent.color || "#6b7280";

  return (
    <div className="flex items-start gap-3 mb-4">
      <Avatar className="mt-1" style={{ backgroundColor: avatarBgColor }}>
        {agent.avatar ? (
          <AvatarImage src={agent.avatar} alt={agent.name} />
        ) : (
          <AvatarFallback>
            {agent.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        )}
      </Avatar>

      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium">{agent.name}</span>
          <span className="text-xs text-gray-500">({agent.role})</span>
          <span className="text-xs text-gray-400">
            {formatTime(message.timestamp)}
          </span>

          {message.type !== MessageType.MESSAGE && (
            <Badge
              variant={
                message.type === MessageType.ERROR
                  ? "destructive"
                  : message.type === MessageType.TIMEOUT
                    ? "outline"
                    : "outline"
              }
              className={cn(
                "text-xs",
                message.type === MessageType.TIMEOUT &&
                  "border-orange-200 text-orange-700 bg-orange-50",
              )}
            >
              {message.type.charAt(0).toUpperCase() +
                message.type.slice(1).toLowerCase()}
            </Badge>
          )}
        </div>

        <Card
          className={cn("p-3 text-sm whitespace-pre-wrap", getTypeStyles())}
        >
          {message.content}
        </Card>
      </div>
    </div>
  );
}
