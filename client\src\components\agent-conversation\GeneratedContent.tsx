import React, { useState } from "react";
import { ContentType } from "@shared/agent-types";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Copy, Download, Check, Loader2 } from "lucide-react";

interface GeneratedContentProps {
  content: string;
  contentType: ContentType | null;
  isLoading: boolean;
}

export function GeneratedContent({
  content,
  contentType,
  isLoading,
}: GeneratedContentProps) {
  const [copied, setCopied] = useState(false);
  const [edited, setEdited] = useState(false);
  const [editedContent, setEditedContent] = useState(content);

  // Actualizar el contenido editable cuando cambia el contenido original
  React.useEffect(() => {
    setEditedContent(content);
  }, [content]);

  // Manejar copia al portapapeles
  const handleCopy = () => {
    navigator.clipboard.writeText(editedContent);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Manejar descarga como archivo de texto
  const handleDownload = () => {
    // Crear nombre de archivo basado en el tipo de contenido
    const fileType = contentType
      ? String(contentType).toLowerCase()
      : "content";
    const filename = `${fileType}_${new Date().toISOString().split("T")[0]}.txt`;

    // Crear un blob con el contenido
    const blob = new Blob([editedContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);

    // Crear un enlace y hacer clic en él para iniciar la descarga
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Limpiar
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Formatear el título del tipo de contenido
  const formatContentTypeTitle = (type: ContentType | null): string => {
    if (!type) return "Contenido";

    const formatMap: Record<ContentType, string> = {
      [ContentType.INSTAGRAM_POST]: "Post para Instagram",
      [ContentType.FACEBOOK_POST]: "Post para Facebook",
      [ContentType.TWITTER_POST]: "Tweet / Publicación para Twitter",
      [ContentType.LANDING_PAGE]: "Página de aterrizaje (Landing Page)",
      [ContentType.EMAIL_CAMPAIGN]: "Newsletter por email",
      [ContentType.BLOG_POST]: "Artículo para blog",
      [ContentType.PRODUCT_DESCRIPTION]: "Descripción de producto",
      [ContentType.AD_COPY]: "Copy publicitario",
      [ContentType.PRESS_RELEASE]: "Comunicado de prensa",
      [ContentType.SOCIAL_MEDIA_BIO]: "Biografía para redes sociales",
      [ContentType.VIDEO_SCRIPT]: "Guión para video",
    };

    return formatMap[type] || type.replace(/_/g, " ").toLowerCase();
  };

  return (
    <Card className="p-4 h-full flex flex-col border-2 border-black shadow-neo">
      <div className="text-center text-2xl font-bold mb-4 border-b-2 border-black pb-2">
        {formatContentTypeTitle(contentType)}
      </div>

      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-gray-500" />
            <p className="text-gray-600">Generando contenido...</p>
            <p className="text-sm text-gray-500 mt-2">
              Esto puede tomar un momento mientras nuestros agentes trabajan en
              tu contenido.
            </p>
          </div>
        </div>
      ) : content ? (
        <>
          <div className="flex-1 mb-4">
            <Textarea
              className="w-full h-full min-h-[300px] p-3 text-base font-medium"
              value={editedContent}
              onChange={(e) => {
                setEditedContent(e.target.value);
                setEdited(true);
              }}
            />
          </div>

          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {edited && <span className="text-blue-500 mr-2">Editado</span>}
              {content.length} caracteres • {content.split(/\s+/).length}{" "}
              palabras
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="flex items-center gap-1"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
                {copied ? "Copiado" : "Copiar"}
              </Button>

              <Button
                variant="default"
                size="sm"
                onClick={handleDownload}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                Descargar
              </Button>
            </div>
          </div>
        </>
      ) : (
        <div className="flex-1 flex items-center justify-center text-center text-gray-500 p-8">
          El contenido generado aparecerá aquí cuando esté listo.
        </div>
      )}
    </Card>
  );
}
