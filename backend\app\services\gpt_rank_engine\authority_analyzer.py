"""
Authority Analyzer Module
Analyzes authority and expertise signals in content
"""

import logging
import re
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class AuthorityAnalyzer:
    """Analyzes authority and expertise signals in content."""
    
    def __init__(self):
        logger.info("✅ Authority Analyzer initialized successfully")
    
    def calculate_authority_signals(self, content: str, topic: str) -> float:
        """
        Calculate authority and expertise signals in content.
        
        Args:
            content: Content to analyze
            topic: Main topic for context
            
        Returns:
            Authority signals score (0-100)
        """
        try:
            score = 50.0  # Base score
            content_lower = content.lower()
            
            # Check for expertise indicators
            expertise_indicators = [
                'experiencia', 'experto', 'especialista', 'profesional', 'certificado',
                'años de experiencia', 'investigación', 'estudio', 'análisis',
                'doctor', 'profesor', 'investigador', 'científico', 'académico'
            ]
            expertise_count = sum(1 for indicator in expertise_indicators if indicator in content_lower)
            score += min(expertise_count * 3, 20)
            
            # Check for data and statistics
            statistics_patterns = [
                r'\d+%',  # Percentages
                r'\d+\.\d+%',  # Decimal percentages
                r'\d+ de cada \d+',  # Ratios
                r'\d+,\d+',  # Numbers with commas
                r'estadística',  # Statistics mentions
                r'datos'  # Data mentions
            ]
            
            stats_count = 0
            for pattern in statistics_patterns:
                stats_count += len(re.findall(pattern, content))
            
            score += min(stats_count * 2, 15)
            
            # Check for specific numbers and facts
            number_count = len(re.findall(r'\b\d+\b', content))
            score += min(number_count * 0.5, 10)
            
            # Check for authoritative language
            auth_language = [
                'demostrado', 'comprobado', 'evidencia', 'investigación muestra',
                'estudios confirman', 'datos revelan', 'análisis indica',
                'se ha establecido', 'está documentado', 'científicamente probado'
            ]
            auth_count = sum(1 for phrase in auth_language if phrase in content_lower)
            score += min(auth_count * 4, 20)
            
            # Check for balanced perspective
            balance_indicators = [
                'sin embargo', 'no obstante', 'por otro lado', 'aunque',
                'es importante notar', 'cabe mencionar', 'hay que considerar'
            ]
            balance_count = sum(1 for indicator in balance_indicators if indicator in content_lower)
            score += min(balance_count * 2, 10)
            
            # Check for source attribution
            source_indicators = [
                'según', 'de acuerdo con', 'basado en', 'fuente:',
                'referencia:', 'citado por', 'reportado por'
            ]
            source_count = sum(1 for indicator in source_indicators if indicator in content_lower)
            score += min(source_count * 3, 15)
            
            # Penalty for promotional language
            promotional_words = [
                'increíble', 'amazing', 'fantástico', 'revolucionario',
                'único', 'exclusivo', 'garantizado', 'gratis', 'oferta'
            ]
            promotional_count = sum(1 for word in promotional_words if word in content_lower)
            score -= promotional_count * 2
            
            return min(max(score, 0), 100)
            
        except Exception as e:
            logger.error(f"❌ Authority signals calculation failed: {str(e)}")
            return 50.0
    
    def analyze_expertise_indicators(self, content: str) -> Dict[str, Any]:
        """Analyze specific expertise indicators in content."""
        try:
            content_lower = content.lower()
            
            expertise_categories = {
                "academic_credentials": {
                    "indicators": ['doctor', 'phd', 'profesor', 'investigador', 'académico', 'universidad'],
                    "count": 0
                },
                "professional_experience": {
                    "indicators": ['años de experiencia', 'especialista', 'experto', 'profesional', 'certificado'],
                    "count": 0
                },
                "research_background": {
                    "indicators": ['investigación', 'estudio', 'análisis', 'publicación', 'paper'],
                    "count": 0
                },
                "industry_recognition": {
                    "indicators": ['premio', 'reconocimiento', 'líder', 'autoridad', 'referente'],
                    "count": 0
                }
            }
            
            # Count indicators in each category
            for category, data in expertise_categories.items():
                for indicator in data["indicators"]:
                    if indicator in content_lower:
                        data["count"] += 1
            
            # Calculate expertise score
            total_indicators = sum(data["count"] for data in expertise_categories.values())
            expertise_score = min(total_indicators * 5, 100)
            
            # Identify strongest expertise area
            strongest_area = max(
                expertise_categories.keys(),
                key=lambda k: expertise_categories[k]["count"]
            )
            
            return {
                "expertise_categories": expertise_categories,
                "total_expertise_indicators": total_indicators,
                "expertise_score": expertise_score,
                "strongest_expertise_area": strongest_area,
                "expertise_level": self._classify_expertise_level(expertise_score)
            }
            
        except Exception as e:
            logger.error(f"❌ Expertise indicators analysis failed: {str(e)}")
            return {
                "expertise_score": 50,
                "expertise_level": "unknown",
                "error": str(e)
            }
    
    def analyze_evidence_quality(self, content: str) -> Dict[str, Any]:
        """Analyze quality of evidence presented in content."""
        try:
            content_lower = content.lower()
            
            evidence_types = {
                "statistical_evidence": {
                    "patterns": [r'\d+%', r'\d+\.\d+%', r'\d+ de cada \d+'],
                    "count": 0,
                    "weight": 3
                },
                "research_citations": {
                    "indicators": ['estudio', 'investigación', 'análisis', 'según'],
                    "count": 0,
                    "weight": 4
                },
                "expert_opinions": {
                    "indicators": ['experto dice', 'especialista afirma', 'según el doctor'],
                    "count": 0,
                    "weight": 3
                },
                "factual_data": {
                    "patterns": [r'\b\d{4}\b', r'\b\d+\b'],  # Years, numbers
                    "count": 0,
                    "weight": 2
                },
                "comparative_data": {
                    "indicators": ['comparado con', 'en contraste', 'versus', 'frente a'],
                    "count": 0,
                    "weight": 2
                }
            }
            
            # Count evidence types
            for evidence_type, data in evidence_types.items():
                if "patterns" in data:
                    for pattern in data["patterns"]:
                        data["count"] += len(re.findall(pattern, content))
                if "indicators" in data:
                    for indicator in data["indicators"]:
                        if indicator in content_lower:
                            data["count"] += 1
            
            # Calculate weighted evidence score
            weighted_score = 0
            total_weight = 0
            
            for evidence_type, data in evidence_types.items():
                weighted_score += data["count"] * data["weight"]
                total_weight += data["weight"]
            
            evidence_score = min((weighted_score / total_weight) * 20, 100) if total_weight > 0 else 0
            
            return {
                "evidence_types": evidence_types,
                "evidence_score": evidence_score,
                "evidence_quality": self._classify_evidence_quality(evidence_score),
                "strongest_evidence_type": max(
                    evidence_types.keys(),
                    key=lambda k: evidence_types[k]["count"]
                ) if any(data["count"] > 0 for data in evidence_types.values()) else "none"
            }
            
        except Exception as e:
            logger.error(f"❌ Evidence quality analysis failed: {str(e)}")
            return {
                "evidence_score": 50,
                "evidence_quality": "unknown",
                "error": str(e)
            }
    
    def analyze_objectivity(self, content: str) -> Dict[str, Any]:
        """Analyze objectivity and neutrality of content."""
        try:
            content_lower = content.lower()
            
            objectivity_indicators = {
                "neutral_language": {
                    "indicators": ['según', 'de acuerdo con', 'los datos muestran', 'se observa'],
                    "count": 0,
                    "positive": True
                },
                "subjective_language": {
                    "indicators": ['creo que', 'pienso que', 'en mi opinión', 'personalmente'],
                    "count": 0,
                    "positive": False
                },
                "emotional_language": {
                    "indicators": ['increíble', 'fantástico', 'terrible', 'horrible', 'amazing'],
                    "count": 0,
                    "positive": False
                },
                "balanced_perspective": {
                    "indicators": ['sin embargo', 'por otro lado', 'aunque', 'no obstante'],
                    "count": 0,
                    "positive": True
                },
                "factual_statements": {
                    "indicators": ['es un', 'se define como', 'consiste en', 'se caracteriza por'],
                    "count": 0,
                    "positive": True
                }
            }
            
            # Count objectivity indicators
            for category, data in objectivity_indicators.items():
                for indicator in data["indicators"]:
                    if indicator in content_lower:
                        data["count"] += 1
            
            # Calculate objectivity score
            positive_score = sum(
                data["count"] for data in objectivity_indicators.values() 
                if data["positive"]
            )
            negative_score = sum(
                data["count"] for data in objectivity_indicators.values() 
                if not data["positive"]
            )
            
            objectivity_score = max(0, min(100, 50 + (positive_score * 5) - (negative_score * 3)))
            
            return {
                "objectivity_indicators": objectivity_indicators,
                "positive_indicators": positive_score,
                "negative_indicators": negative_score,
                "objectivity_score": objectivity_score,
                "objectivity_level": self._classify_objectivity_level(objectivity_score)
            }
            
        except Exception as e:
            logger.error(f"❌ Objectivity analysis failed: {str(e)}")
            return {
                "objectivity_score": 50,
                "objectivity_level": "unknown",
                "error": str(e)
            }
    
    def _classify_expertise_level(self, expertise_score: float) -> str:
        """Classify expertise level based on score."""
        if expertise_score >= 80:
            return "expert"
        elif expertise_score >= 60:
            return "knowledgeable"
        elif expertise_score >= 40:
            return "informed"
        else:
            return "basic"
    
    def _classify_evidence_quality(self, evidence_score: float) -> str:
        """Classify evidence quality based on score."""
        if evidence_score >= 80:
            return "strong"
        elif evidence_score >= 60:
            return "good"
        elif evidence_score >= 40:
            return "moderate"
        else:
            return "weak"
    
    def _classify_objectivity_level(self, objectivity_score: float) -> str:
        """Classify objectivity level based on score."""
        if objectivity_score >= 80:
            return "highly_objective"
        elif objectivity_score >= 60:
            return "objective"
        elif objectivity_score >= 40:
            return "somewhat_objective"
        else:
            return "subjective"
    
    def get_authority_improvement_suggestions(self, analysis_results: Dict[str, Any]) -> List[str]:
        """Generate suggestions to improve authority signals."""
        suggestions = []
        
        # Check expertise indicators
        expertise_score = analysis_results.get("expertise_score", 50)
        if expertise_score < 60:
            suggestions.append("Incluir más indicadores de experiencia y credenciales")
        
        # Check evidence quality
        evidence_score = analysis_results.get("evidence_score", 50)
        if evidence_score < 60:
            suggestions.append("Agregar más datos, estadísticas y referencias a estudios")
        
        # Check objectivity
        objectivity_score = analysis_results.get("objectivity_score", 50)
        if objectivity_score < 60:
            suggestions.append("Usar lenguaje más neutral y objetivo")
        
        # Check for balanced perspective
        objectivity_indicators = analysis_results.get("objectivity_indicators", {})
        balanced_count = objectivity_indicators.get("balanced_perspective", {}).get("count", 0)
        if balanced_count == 0:
            suggestions.append("Incluir perspectivas balanceadas y consideraciones alternativas")
        
        return suggestions
