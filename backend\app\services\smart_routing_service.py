"""
Smart Routing Service for Emma Studio Agent System

This service implements intelligent routing logic to determine whether requests
should go directly to specific agents or through <PERSON>'s orchestration system.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from app.schemas.crew import AgentChatRequest

logger = logging.getLogger(__name__)


class RoutingDecision(Enum):
    """Possible routing decisions for agent requests."""
    DIRECT_TO_AGENT = "direct_to_agent"
    ORCHESTRATE_WITH_EMMA = "orchestrate_with_emma"
    MULTI_AGENT_COORDINATION = "multi_agent_coordination"


@dataclass
class RoutingResult:
    """Result of routing analysis."""
    decision: RoutingDecision
    target_agent_id: Optional[str]
    target_agents: List[str]
    confidence: float
    reasoning: str
    metadata: Dict[str, Any]


class SmartRoutingService:
    """
    Intelligent routing service that determines the best way to handle agent requests.

    This service analyzes incoming requests and decides whether to:
    1. Route directly to a specific agent (bypassing <PERSON>)
    2. Route through <PERSON> for orchestration
    3. Coordinate multiple agents for complex tasks
    """

    def __init__(self, agent_configs: Dict[str, Dict[str, Any]]):
        """
        Initialize the smart routing service.

        Args:
            agent_configs: Dictionary of agent configurations from YAML
        """
        self.agent_configs = agent_configs
        self.logger = logging.getLogger(__name__)

        # Build routing maps from agent configurations
        self._build_routing_maps()

    def _build_routing_maps(self):
        """Build internal routing maps from agent configurations."""
        self.direct_access_keywords = {}
        self.agent_specialties = {}
        self.orchestrator_agents = []
        self.autonomous_agents = []

        for agent_name, config in self.agent_configs.items():
            agent_type = config.get('agent_type', 'autonomous')
            routing_mode = config.get('routing_mode', 'direct_access')

            if agent_type == 'orchestrator':
                self.orchestrator_agents.append(agent_name.lower())
            else:
                self.autonomous_agents.append(agent_name.lower())

            # Map keywords to agents for direct access
            keywords = config.get('direct_access_keywords', [])
            for keyword in keywords:
                if keyword not in self.direct_access_keywords:
                    self.direct_access_keywords[keyword] = []
                self.direct_access_keywords[keyword].append(agent_name.lower())

            # Map specialties
            specialties = config.get('specialties', [])
            self.agent_specialties[agent_name.lower()] = specialties

        self.logger.info(f"Built routing maps: {len(self.autonomous_agents)} autonomous agents, "
                        f"{len(self.orchestrator_agents)} orchestrators")

    def analyze_request(self, request: AgentChatRequest) -> RoutingResult:
        """
        Analyze an incoming request and determine the best routing strategy.

        Args:
            request: The agent chat request to analyze

        Returns:
            RoutingResult with routing decision and metadata
        """
        message = request.message.lower()
        requested_agent = request.agent_id.lower() if request.agent_id else None

        # 1. Check for explicit agent request
        if self._is_explicit_agent_request(requested_agent, message):
            return self._route_to_specific_agent(requested_agent, message)

        # 2. Check for multi-agent requirements
        multi_agent_result = self._check_multi_agent_requirements(message)
        if multi_agent_result:
            return multi_agent_result

        # 3. Check for direct access keywords
        direct_access_result = self._check_direct_access_keywords(message)
        if direct_access_result:
            return direct_access_result

        # 4. Default to Emma orchestration for complex or unclear requests
        return self._route_to_orchestration(message, "Complex or unclear request requiring orchestration")

    def _is_explicit_agent_request(self, requested_agent: Optional[str], message: str) -> bool:
        """Check if user explicitly requested a specific agent."""
        if not requested_agent:
            return False

        # Check if the requested agent is autonomous and can handle direct requests
        if requested_agent in self.autonomous_agents:
            # Additional checks for explicit intent
            explicit_patterns = [
                f"talk to {requested_agent}",
                f"use {requested_agent}",
                f"ask {requested_agent}",
                f"get {requested_agent}",
                "directly",
                "specifically",
                "only"
            ]

            for pattern in explicit_patterns:
                if pattern in message:
                    return True

            # If user selected a specific agent in UI, treat as explicit
            return True

        return False

    def _route_to_specific_agent(self, agent_id: str, message: str) -> RoutingResult:
        """Route directly to a specific autonomous agent."""
        return RoutingResult(
            decision=RoutingDecision.DIRECT_TO_AGENT,
            target_agent_id=agent_id,
            target_agents=[agent_id],
            confidence=0.9,
            reasoning=f"User explicitly requested {agent_id} agent for direct communication",
            metadata={
                "routing_type": "explicit_agent_request",
                "bypass_orchestration": True
            }
        )

    def _check_multi_agent_requirements(self, message: str) -> Optional[RoutingResult]:
        """Check if the request requires multiple agents."""
        # Keywords that suggest multi-agent coordination
        multi_agent_patterns = [
            "strategy", "campaign", "comprehensive", "complete solution",
            "both", "all aspects", "full service", "end-to-end",
            "estrategia", "campaña", "completo", "integral"
        ]

        # Check for cross-domain requirements
        content_keywords = ["content", "blog", "article", "copy", "writing"]
        seo_keywords = ["seo", "optimization", "keywords", "ranking"]

        has_content = any(keyword in message for keyword in content_keywords)
        has_seo = any(keyword in message for keyword in seo_keywords)

        if has_content and has_seo:
            return RoutingResult(
                decision=RoutingDecision.MULTI_AGENT_COORDINATION,
                target_agent_id="emma",
                target_agents=["emma", "content", "seo"],
                confidence=0.85,
                reasoning="Request requires both content creation and SEO expertise",
                metadata={
                    "routing_type": "multi_agent_coordination",
                    "required_domains": ["content", "seo"]
                }
            )

        # Check for explicit multi-agent patterns
        for pattern in multi_agent_patterns:
            if pattern in message:
                return RoutingResult(
                    decision=RoutingDecision.ORCHESTRATE_WITH_EMMA,
                    target_agent_id="emma",
                    target_agents=["emma"],
                    confidence=0.8,
                    reasoning=f"Request contains multi-agent pattern: '{pattern}'",
                    metadata={
                        "routing_type": "complex_strategy_request",
                        "pattern_matched": pattern
                    }
                )

        return None

    def _check_direct_access_keywords(self, message: str) -> Optional[RoutingResult]:
        """Check if message contains keywords for direct agent access."""
        matched_agents = []
        matched_keywords = []

        for keyword, agents in self.direct_access_keywords.items():
            if keyword in message:
                matched_agents.extend(agents)
                matched_keywords.append(keyword)

        if matched_agents:
            # If only one agent matches, route directly
            unique_agents = list(set(matched_agents))
            if len(unique_agents) == 1:
                agent_id = unique_agents[0]
                return RoutingResult(
                    decision=RoutingDecision.DIRECT_TO_AGENT,
                    target_agent_id=agent_id,
                    target_agents=[agent_id],
                    confidence=0.75,
                    reasoning=f"Message contains keywords for {agent_id}: {matched_keywords}",
                    metadata={
                        "routing_type": "keyword_match",
                        "matched_keywords": matched_keywords
                    }
                )

        return None

    def _route_to_orchestration(self, message: str, reason: str) -> RoutingResult:
        """Route to Emma for orchestration."""
        return RoutingResult(
            decision=RoutingDecision.ORCHESTRATE_WITH_EMMA,
            target_agent_id="emma",
            target_agents=["emma"],
            confidence=0.6,
            reasoning=reason,
            metadata={
                "routing_type": "orchestration_fallback",
                "requires_analysis": True
            }
        )

    def should_bypass_orchestration(self, request: AgentChatRequest) -> bool:
        """
        Quick check to determine if orchestration should be bypassed.

        Args:
            request: The agent chat request

        Returns:
            True if orchestration should be bypassed, False otherwise
        """
        result = self.analyze_request(request)
        return result.decision == RoutingDecision.DIRECT_TO_AGENT

    def get_routing_explanation(self, request: AgentChatRequest) -> str:
        """
        Get a human-readable explanation of the routing decision.

        Args:
            request: The agent chat request

        Returns:
            String explanation of the routing decision
        """
        result = self.analyze_request(request)
        return f"Routing Decision: {result.decision.value} - {result.reasoning} (Confidence: {result.confidence:.2f})"
