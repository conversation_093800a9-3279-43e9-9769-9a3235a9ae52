import React, { useState } from "react";
import { useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { ElementStyle } from "./EditorContext";

export interface CanvasElementProps {
  id: string;
  type: "text" | "image" | "shape";
  content: string;
  position?: { x: number; y: number };
  style?: ElementStyle;
  selected?: boolean;
  onClick?: (id: string) => void;
  onDoubleClick?: (id: string) => void;
}

const CanvasElement: React.FC<CanvasElementProps> = ({
  id,
  type,
  content,
  position = { x: 0, y: 0 },
  style = {},
  selected = false,
  onClick,
  onDoubleClick,
}) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id,
    data: {
      type,
      content,
      position,
    },
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(content);

  const handleDoubleClick = () => {
    if (type === "text") {
      setIsEditing(true);
      if (onDoubleClick) onDoubleClick(id);
    }
  };

  const handleBlur = () => {
    setIsEditing(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditContent(e.target.value);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) onClick(id);
  };

  // Convertir ElementStyle a un objeto simple sin tipado para evitar errores
  const elementStyle = {
    position: "absolute" as const,
    transform: CSS.Translate.toString(transform),
    top: position.y,
    left: position.x,
    cursor: "move",
    padding: "8px",
    borderRadius: "4px",
    border: selected ? "2px dashed #3b82f6" : "2px solid transparent",
    background: type === "shape" ? "#8b5cf6" : "transparent",
    minWidth: "50px",
    minHeight: "50px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: selected ? 10 : 1,
  };

  // Aplicar estilos personalizados si existen
  if (style) {
    Object.keys(style).forEach((key) => {
      (elementStyle as any)[key] = (style as any)[key];
    });
  }

  const renderContent = () => {
    switch (type) {
      case "text":
        return isEditing ? (
          <textarea
            value={editContent}
            onChange={handleChange}
            onBlur={handleBlur}
            autoFocus
            className="w-full h-full bg-transparent outline-none resize-none"
            style={{ minHeight: "100px" }}
          />
        ) : (
          <div className="whitespace-pre-wrap">{editContent}</div>
        );
      case "image":
        return (
          <img
            src={content}
            alt="Canvas element"
            className="max-w-full h-auto"
          />
        );
      case "shape":
        if (content === "circle") {
          return <div className="w-16 h-16 rounded-full bg-current"></div>;
        } else if (content === "square") {
          return <div className="w-16 h-16 bg-current"></div>;
        } else if (content === "triangle") {
          return (
            <div
              className="w-0 h-0 border-solid"
              style={{
                borderWidth: "0 30px 50px 30px",
                borderColor: "transparent transparent currentColor transparent",
              }}
            ></div>
          );
        }
        return <div>{content}</div>;
      default:
        return <div>{content}</div>;
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={elementStyle as React.CSSProperties}
      {...attributes}
      {...listeners}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      className="canvas-element transition-all"
    >
      {renderContent()}
    </div>
  );
};

export default CanvasElement;
