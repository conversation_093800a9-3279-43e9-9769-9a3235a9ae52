import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Upload,
  Image as ImageIcon,
  Trash2,
  Edit3,
  Eye,
  Download,
  Plus,
  Filter,
  Search,
  Grid3X3,
  List,
  Star,
  Tag,
  Folder,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { useToast } from "@/hooks/use-toast";
import { useTemplates, type AdTemplate } from "@/services/template-service";

interface TemplateManagerProps {
  onSelectTemplate: (template: AdTemplate) => void;
  onEditTemplate: (template: AdTemplate) => void;
}

const categories = [
  { id: "all", name: "Todas", icon: <Grid3X3 className="w-4 h-4" /> },
  { id: "facebook", name: "Facebook", icon: <ImageIcon className="w-4 h-4" /> },
  { id: "instagram", name: "Instagram", icon: <ImageIcon className="w-4 h-4" /> },
  { id: "google", name: "Google Ads", icon: <ImageIcon className="w-4 h-4" /> },
  { id: "linkedin", name: "LinkedIn", icon: <ImageIcon className="w-4 h-4" /> },
  { id: "youtube", name: "YouTube", icon: <ImageIcon className="w-4 h-4" /> },
  { id: "banner", name: "Banners", icon: <ImageIcon className="w-4 h-4" /> },
];

const platforms = [
  { id: "facebook", name: "Facebook", sizes: ["1200x630", "1080x1080"] },
  { id: "instagram", name: "Instagram", sizes: ["1080x1080", "1080x1920", "1080x566"] },
  { id: "google", name: "Google Ads", sizes: ["728x90", "300x250", "320x50"] },
  { id: "linkedin", name: "LinkedIn", sizes: ["1200x627", "1080x1080"] },
  { id: "youtube", name: "YouTube", sizes: ["1280x720", "1920x1080"] },
];

export default function TemplateManager({ onSelectTemplate, onEditTemplate }: TemplateManagerProps) {
  const templateService = useTemplates();
  const [templates, setTemplates] = useState<AdTemplate[]>(templateService.getAllTemplates());
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Auto-seleccionar "Enfoque de Producto" al cargar
  useEffect(() => {
    setSelectedCategory("enfoque-producto");
  }, []);

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Procesar múltiples archivos
    for (const file of Array.from(files)) {
      // Validar tipo de archivo
      const allowedTypes = ['image/png', 'image/jpeg', 'image/svg+xml', 'application/json'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: `Archivo ${file.name} no válido`,
          description: "Solo se permiten archivos PNG, JPG, SVG o JSON",
          variant: "destructive",
        });
        continue;
      }

      try {
        // Auto-detectar categoría y plataforma del nombre del archivo
        const fileName = file.name.toLowerCase();
        let category = "facebook"; // default
        let platform = "facebook"; // default
        let tags: string[] = [];

        if (fileName.includes('instagram')) {
          category = platform = "instagram";
          tags.push("instagram");
        } else if (fileName.includes('facebook')) {
          category = platform = "facebook";
          tags.push("facebook");
        } else if (fileName.includes('google')) {
          category = platform = "google";
          tags.push("google", "ads");
        } else if (fileName.includes('linkedin')) {
          category = platform = "linkedin";
          tags.push("linkedin");
        } else if (fileName.includes('youtube')) {
          category = platform = "youtube";
          tags.push("youtube");
        }

        // Auto-detectar tipo de contenido
        if (fileName.includes('story')) tags.push("story");
        if (fileName.includes('post')) tags.push("post");
        if (fileName.includes('banner')) tags.push("banner");
        if (fileName.includes('ad')) tags.push("anuncio");

        const newTemplate = await templateService.addTemplate({
          name: file.name.replace(/\.[^/.]+$/, ""), // Quitar extensión
          description: `Plantilla para ${platform}`,
          category,
          platform,
          tags,
          file,
        });

        toast({
          title: "Plantilla subida",
          description: `${newTemplate.name} agregada exitosamente`,
        });
      } catch (error) {
        toast({
          title: `Error con ${file.name}`,
          description: "No se pudo procesar el archivo",
          variant: "destructive",
        });
      }
    }

    // Actualizar lista de plantillas
    setTemplates(templateService.getAllTemplates());

    // Limpiar input
    if (event.target) {
      event.target.value = '';
    }
  };

  const handleDeleteTemplate = (templateId: string) => {
    if (templateService.deleteTemplate(templateId)) {
      setTemplates(templateService.getAllTemplates());
      toast({
        title: "Plantilla eliminada",
        description: "La plantilla ha sido eliminada de tu biblioteca",
      });
    }
  };

  const toggleFavorite = (templateId: string) => {
    templateService.toggleFavorite(templateId);
    setTemplates(templateService.getAllTemplates());
  };

  return (
    <div className="space-y-6">
      {/* Header con controles */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Biblioteca de Plantillas</h2>
          <p className="text-gray-600">Gestiona y utiliza tus plantillas de Figma</p>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center gap-2"
          >
            <Upload className="w-4 h-4" />
            Subir Plantillas
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={handleFileUpload}
            accept=".png,.jpg,.jpeg,.svg,.json"
            className="hidden"
          />
        </div>
      </div>

      {/* Filtros y búsqueda */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Buscar plantillas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Categorías */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center gap-2"
          >
            {category.icon}
            {category.name}
          </Button>
        ))}
      </div>

      {/* Grid de plantillas */}
      <div className={`grid gap-4 ${viewMode === "grid" ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" : "grid-cols-1"}`}>
        {filteredTemplates.map((template, index) => (
          <motion.div
            key={template.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
              <div className="relative">
                <img
                  src={template.thumbnail}
                  alt={template.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-2 right-2 flex gap-1">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="h-8 w-8 p-0 bg-white/80 hover:bg-white"
                    onClick={() => toggleFavorite(template.id)}
                  >
                    <Star className={`w-4 h-4 ${template.isFavorite ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                  </Button>
                </div>
                <div className="absolute bottom-2 left-2">
                  <Badge variant="secondary" className="text-xs">
                    {template.width}x{template.height}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-4">
                <div className="space-y-2">
                  <h3 className="font-semibold text-sm line-clamp-1">{template.name}</h3>
                  <p className="text-xs text-gray-600 line-clamp-2">{template.description}</p>
                  
                  <div className="flex flex-wrap gap-1">
                    {template.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex gap-1 pt-2">
                    <Button
                      size="sm"
                      onClick={() => onSelectTemplate(template)}
                      className="flex-1 text-xs"
                    >
                      <Edit3 className="w-3 h-3 mr-1" />
                      Editar
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteTemplate(template.id)}
                      className="text-xs"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No se encontraron plantillas</h3>
          <p className="text-gray-500 mb-4">Intenta cambiar los filtros o sube una nueva plantilla</p>
          <Button onClick={() => setIsUploadDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Subir Primera Plantilla
          </Button>
        </div>
      )}
    </div>
  );
}
