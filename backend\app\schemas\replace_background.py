"""
Schemas for replace background and relight functionality using Stability AI v2beta API.
"""

from typing import Optional, Literal
from pydantic import BaseModel, Field


class ReplaceBackgroundRequest(BaseModel):
    """Request schema for Stability AI replace background and relight API."""

    background_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="What you wish to see in the background of the output image"
    )

    foreground_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="Description of the subject to prevent background bleeding"
    )

    negative_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="A blurb of text describing what you do NOT wish to see in the output image"
    )

    preserve_original_subject: Optional[float] = Field(
        default=0.6,
        ge=0.0,
        le=1.0,
        description="How much to overlay the original subject to exactly match the original image"
    )

    original_background_depth: Optional[float] = Field(
        default=0.5,
        ge=0.0,
        le=1.0,
        description="Controls the generated background to have the same depth as the original subject image"
    )

    keep_original_background: Optional[bool] = Field(
        default=False,
        description="Whether to keep the background of the original image"
    )

    light_source_direction: Optional[Literal["above", "below", "left", "right"]] = Field(
        default=None,
        description="Direction of the light source"
    )

    light_source_strength: Optional[float] = Field(
        default=0.3,
        ge=0.0,
        le=1.0,
        description="Controls the strength of the light source"
    )

    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation"
    )

    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="png",
        description="Dictates the content-type of the generated image"
    )


class ReplaceBackgroundInitialResponse(BaseModel):
    """Initial response from Stability AI replace background API (async start)."""

    id: str = Field(description="Generation ID for polling")


class ReplaceBackgroundFinalResponse(BaseModel):
    """Final response from Stability AI replace background API (polling result)."""

    image: str = Field(description="Base64 encoded image data")
    seed: Optional[int] = Field(description="The seed used for generation")
    finish_reason: str = Field(description="Reason the generation finished")


class FrontendReplaceBackgroundInitialResponse(BaseModel):
    """Response schema for frontend replace background initial requests."""

    success: bool = Field(description="Whether the operation was successful")
    id: Optional[str] = Field(default=None, description="Generation ID for polling")
    message: Optional[str] = Field(default=None, description="Status message")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")


class FrontendReplaceBackgroundStatusResponse(BaseModel):
    """Response schema for frontend replace background status requests."""

    success: bool = Field(description="Whether the operation was successful")
    status: Optional[str] = Field(default=None, description="Current status (IN_PROGRESS, COMPLETED, FAILED)")
    image_url: Optional[str] = Field(default=None, description="Data URL of the generated image")
    seed: Optional[int] = Field(default=None, description="The seed used for generation")
    finish_reason: Optional[str] = Field(default=None, description="Reason the generation finished")
    metadata: Optional[dict] = Field(default=None, description="Additional metadata")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")
