"""
OpenAI Images API endpoints for image generation using gpt-image-1 model.
Supports initial generation, references, and mask-based editing.
"""

from fastapi import APIRouter, Form, File, UploadFile, HTTPException
from typing import List
import logging

from app.services.openai_image_service import openai_image_service
from app.models.openai_image_models import FrontendOpenAIImageResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["openai-images"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


@router.post("/generate")
async def generate_openai_image(
    prompt: str = Form(..., description="Description of the image to create"),
    size: str = Form(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)")
) -> FrontendOpenAIImageResponse:
    """Generate an image using OpenAI's gpt-image-1 model."""
    
    try:
        logger.info(f"🎨 Generating OpenAI image: {prompt[:100]}...")
        
        # Call the service
        service_response = await openai_image_service.generate_image(
            prompt=prompt,
            size=size
        )
        
        # Convert to frontend response
        return FrontendOpenAIImageResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in generate_openai_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image generation: {e}"
        )


@router.post("/multi-turn-edit")
async def multi_turn_edit_openai_image(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendOpenAIImageResponse:
    """Edit an existing image using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing OpenAI image: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await openai_image_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendOpenAIImageResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit_openai_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn editing: {e}"
        )


@router.post("/edit-with-references")
async def edit_openai_image_with_references(
    prompt: str = Form(..., description="Description of the image to create"),
    reference_images: List[UploadFile] = File(..., description="Reference images (max 4)"),
    size: str = Form(default="auto", description="Image size (1024x1024, 1536x1024, 1024x1536, auto)")
) -> FrontendOpenAIImageResponse:
    """Generate image using reference images."""
    
    try:
        logger.info(f"🖼️ Generating OpenAI image using {len(reference_images)} references: {prompt[:100]}...")
        
        # Validate reference images count
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        # Call the service
        service_response = await openai_image_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            size=size
        )
        
        # Convert to frontend response
        return FrontendOpenAIImageResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_openai_image_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference-based generation: {e}"
        )


@router.post("/edit-with-mask")
async def edit_openai_image_with_mask(
    prompt: str = Form(..., description="Description of the changes to make"),
    image: UploadFile = File(..., description="Original image file"),
    mask: UploadFile = File(..., description="Mask image file (white areas will be edited)")
) -> FrontendOpenAIImageResponse:
    """Edit image using mask-based editing."""
    
    try:
        logger.info(f"🎭 Editing OpenAI image using mask: {prompt[:100]}...")
        
        # Call the service
        service_response = await openai_image_service.edit_with_mask(
            prompt=prompt,
            image=image,
            mask=mask
        )
        
        # Convert to frontend response
        return FrontendOpenAIImageResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in edit_openai_image_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask-based editing: {e}"
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for OpenAI images service."""
    return {
        "status": "healthy",
        "service": "openai-images",
        "message": "OpenAI Images service is running"
    }
