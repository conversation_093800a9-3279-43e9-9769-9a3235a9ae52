# Moodboard Supabase Integration Testing Guide

This document provides comprehensive testing instructions for the interactive moodboard tool's Supabase integration.

## Overview

The moodboard tool has been successfully integrated with Supabase following the exact same patterns used in the visual complexity analyzer implementation. This includes:

- ✅ Database schema with proper RLS policies
- ✅ Backend API endpoints with authentication
- ✅ Frontend service layer and custom hooks
- ✅ Auto-save functionality with Tldraw integration
- ✅ User authentication and data privacy
- ✅ Error handling and loading states

## Database Schema

### Tables Created

1. **moodboards** - Main table for storing moodboard data
   - `id` (UUID, Primary Key)
   - `user_id` (TEXT, Foreign Key to auth.users)
   - `title`, `description` (TEXT)
   - `tldraw_data` (JSONB) - Canvas state
   - `canvas_snapshot` (TEXT) - Preview image URL
   - `tags` (TEXT[]) - Categorization
   - `is_public`, `is_favorite` (BOOLEAN)
   - `collaboration_enabled` (BOOLEAN)
   - `shared_with` (TEXT[]) - User IDs with access
   - `view_count` (INTEGER)
   - `status` (TEXT) - active/archived/deleted
   - Timestamps: `created_at`, `updated_at`, `last_viewed_at`

2. **moodboard_history** - Version control and change tracking
   - `id` (UUID, Primary Key)
   - `moodboard_id` (UUID, Foreign Key)
   - `user_id` (TEXT)
   - `change_type` (TEXT) - create/update/snapshot
   - `tldraw_data_snapshot` (JSONB)
   - `version_number` (INTEGER)
   - `is_auto_save` (BOOLEAN)

### RLS Policies

- Users can only view/edit/delete their own moodboards
- Public moodboards can be viewed by anyone
- History entries are restricted to the owner
- All policies follow the same pattern as design_analyses

## API Endpoints

All endpoints are available at `/api/moodboard/`:

- `POST /create` - Create new moodboard
- `GET /list` - List user's moodboards (paginated)
- `GET /{id}` - Get specific moodboard
- `PUT /{id}` - Update moodboard
- `DELETE /{id}` - Soft delete moodboard
- `POST /{id}/history` - Create history entry
- `GET /stats/summary` - Get user statistics

## Testing Instructions

### 1. Automated Integration Test

Visit: `http://localhost:3000/test/moodboard-integration`

This page provides:
- Authentication status check
- Automated CRUD operation tests
- Auto-save functionality verification
- Error handling validation
- Test data cleanup

### 2. Manual Testing Steps

#### Prerequisites
1. Start the frontend: `cd client && npm run dev`
2. Start the backend: `cd backend && python -m uvicorn app.main:app --reload`
3. Ensure you're logged in with a valid user account

#### Test Scenarios

**A. Basic CRUD Operations**
1. Navigate to `/dashboard/herramientas/mood-board`
2. Click "Crear Nuevo" to create a new moodboard
3. Verify the editor loads with authentication
4. Edit the title and save manually
5. Draw something on the canvas
6. Verify auto-save indicator appears
7. Navigate back to the list
8. Verify the moodboard appears in the list
9. Delete the moodboard and verify it's removed

**B. Auto-save Functionality**
1. Create a new moodboard
2. Draw on the canvas
3. Wait 30 seconds (auto-save interval)
4. Verify "Guardado hace un momento" appears
5. Refresh the page
6. Verify the drawing is preserved

**C. Authentication & Privacy**
1. Create a moodboard while logged in
2. Log out
3. Try to access the moodboard URL directly
4. Verify access is denied
5. Log back in
6. Verify access is restored

**D. Cross-browser Testing**
Test the following browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

Verify:
- Tldraw canvas works correctly
- Auto-save functions properly
- Authentication persists
- UI renders correctly

### 3. Performance Testing

**Database Performance**
- Test with 100+ moodboards
- Verify pagination works correctly
- Check query performance with indexes

**Auto-save Performance**
- Test rapid canvas changes
- Verify debouncing works correctly
- Check for memory leaks during long sessions

### 4. Error Handling Testing

**Network Errors**
1. Disconnect internet while using the editor
2. Verify graceful error handling
3. Reconnect and verify sync works

**Authentication Errors**
1. Let session expire during use
2. Verify proper error messages
3. Test token refresh functionality

**Database Errors**
1. Test with invalid data
2. Verify validation works
3. Check error message clarity

## Expected Behavior

### Loading States
- ✅ Authentication loading spinner
- ✅ Moodboard list loading state
- ✅ Individual moodboard loading
- ✅ Save operation indicators

### Error States
- ✅ Authentication required messages
- ✅ Network error handling
- ✅ Validation error display
- ✅ Graceful fallbacks

### Auto-save Features
- ✅ 30-second auto-save interval
- ✅ Visual indicators for unsaved changes
- ✅ Manual save override
- ✅ History tracking

### Data Privacy
- ✅ RLS policies enforce user isolation
- ✅ Public/private moodboard settings
- ✅ Secure API endpoints
- ✅ Proper authentication checks

## Troubleshooting

### Common Issues

1. **"No authentication token available"**
   - Ensure user is logged in
   - Check Supabase session validity
   - Verify API_URL environment variable

2. **"Table doesn't exist" errors**
   - Run the database schema creation scripts
   - Check Supabase project connection
   - Verify RLS policies are enabled

3. **Auto-save not working**
   - Check browser console for errors
   - Verify Tldraw onChange events
   - Check network tab for API calls

4. **Canvas not loading**
   - Clear browser cache
   - Check Tldraw version compatibility
   - Verify persistenceKey is unique

### Debug Tools

- Browser DevTools Network tab
- Supabase Dashboard SQL Editor
- Backend logs (uvicorn output)
- Frontend console logs

## Success Criteria

The integration is considered successful when:

- ✅ All automated tests pass
- ✅ Manual testing scenarios work correctly
- ✅ Cross-browser compatibility confirmed
- ✅ Performance meets expectations
- ✅ Error handling is robust
- ✅ Data privacy is enforced
- ✅ Auto-save functions reliably

## Next Steps

After successful testing:

1. Deploy to staging environment
2. Conduct user acceptance testing
3. Monitor performance metrics
4. Gather user feedback
5. Plan additional features (collaboration, templates, etc.)

## Support

For issues or questions:
- Check the browser console for errors
- Review Supabase dashboard for database issues
- Verify authentication status
- Test with the integration test page first
