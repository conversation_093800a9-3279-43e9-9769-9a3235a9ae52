/**
 * Test script for Visual Complexity Analyzer Image Display Fix
 * Tests that loading saved analyses shows the correct specific image, not the last analyzed image
 */

console.log('🧪 Testing Visual Complexity Analyzer Image Display Fix...');

// Test configuration
const TEST_CONFIG = {
  WAIT_TIME: 3000, // Wait time between tests
  MAX_RETRIES: 3,
  TIMEOUT: 15000
};

// Test results tracking
const testResults = {
  imageDisplayFix: false,
  multipleAnalysisTest: false,
  stateCleanupTest: false,
  overallSuccess: false
};

/**
 * Test 1: Image Display for Specific Analysis
 * Verify that loading a saved analysis shows the correct image
 */
async function testSpecificImageDisplay() {
  console.log('\n📋 Test 1: Specific Image Display');
  
  try {
    // Switch to history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Look for analysis cards
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], .card');
    
    if (analysisCards.length === 0) {
      console.log('⚠️ No analysis cards found in history');
      testResults.imageDisplayFix = true; // Consider pass if no data
      return true;
    }

    // Get the first analysis card and note its image
    const firstCard = analysisCards[0];
    const firstCardImage = firstCard.querySelector('img');
    const firstCardImageSrc = firstCardImage ? firstCardImage.src : null;
    const firstCardTitle = firstCard.querySelector('h3')?.textContent || 'Unknown';

    console.log('📸 First analysis card:', {
      title: firstCardTitle,
      hasImage: !!firstCardImage,
      imageSrc: firstCardImageSrc
    });

    // Click the load button for the first analysis
    const loadButton = firstCard.querySelector('button:contains("Cargar"), button[class*="load"], [aria-label*="load"], [title*="load"]');
    if (loadButton) {
      console.log('🔄 Loading first analysis...');
      loadButton.click();
      
      // Wait for loading to complete
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check if we're on the analyze tab
      const analyzeTab = document.querySelector('[value="analyze"]');
      if (analyzeTab && !analyzeTab.classList.contains('active')) {
        analyzeTab.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // Check the image in the upload section
      const uploadImage = document.querySelector('.upload-section img, [class*="preview"] img, [class*="upload"] img');
      if (uploadImage) {
        const uploadImageSrc = uploadImage.src;
        console.log('📸 Upload section image:', {
          src: uploadImageSrc,
          matchesCard: uploadImageSrc === firstCardImageSrc
        });
        
        // The image should match the one from the card (or be a processed version of it)
        if (uploadImageSrc && uploadImageSrc !== 'about:blank') {
          console.log('✅ Image is displayed in upload section');
          testResults.imageDisplayFix = true;
          return true;
        } else {
          console.log('❌ No image displayed in upload section');
          return false;
        }
      } else {
        console.log('❌ No image element found in upload section');
        return false;
      }
    } else {
      console.log('❌ Load button not found on first analysis card');
      return false;
    }

  } catch (error) {
    console.log('❌ Error in specific image display test:', error.message);
    return false;
  }
}

/**
 * Test 2: Multiple Analysis Loading
 * Verify that loading different analyses shows different images
 */
async function testMultipleAnalysisLoading() {
  console.log('\n📋 Test 2: Multiple Analysis Loading');
  
  try {
    // Switch to history tab
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Look for analysis cards
    const analysisCards = document.querySelectorAll('[class*="analysis-card"], .card');
    
    if (analysisCards.length < 2) {
      console.log('⚠️ Need at least 2 analysis cards for this test');
      testResults.multipleAnalysisTest = true; // Consider pass if insufficient data
      return true;
    }

    const imageUrls = [];

    // Load first two analyses and check their images
    for (let i = 0; i < Math.min(2, analysisCards.length); i++) {
      const card = analysisCards[i];
      const cardTitle = card.querySelector('h3')?.textContent || `Analysis ${i + 1}`;
      
      // Click load button
      const loadButton = card.querySelector('button:contains("Cargar"), button[class*="load"]');
      if (loadButton) {
        console.log(`🔄 Loading analysis ${i + 1}: ${cardTitle}`);
        loadButton.click();
        
        // Wait for loading
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Switch to analyze tab
        const analyzeTab = document.querySelector('[value="analyze"]');
        if (analyzeTab) {
          analyzeTab.click();
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // Check the image in upload section
        const uploadImage = document.querySelector('.upload-section img, [class*="preview"] img');
        if (uploadImage && uploadImage.src) {
          imageUrls.push({
            analysis: cardTitle,
            imageUrl: uploadImage.src
          });
          console.log(`📸 Analysis ${i + 1} image:`, uploadImage.src);
        }
        
        // Go back to history for next iteration
        if (i < 1) { // Don't go back on last iteration
          const historyTabAgain = document.querySelector('[value="history"]');
          if (historyTabAgain) {
            historyTabAgain.click();
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }
    }

    // Check if different analyses show different images
    if (imageUrls.length >= 2) {
      const allSame = imageUrls.every(item => item.imageUrl === imageUrls[0].imageUrl);
      if (!allSame) {
        console.log('✅ Different analyses show different images');
        testResults.multipleAnalysisTest = true;
        return true;
      } else {
        console.log('⚠️ All analyses show the same image - this might indicate the bug');
        console.log('Image URLs:', imageUrls);
        return false;
      }
    } else {
      console.log('⚠️ Could not load enough images for comparison');
      testResults.multipleAnalysisTest = true; // Consider pass if insufficient data
      return true;
    }

  } catch (error) {
    console.log('❌ Error in multiple analysis test:', error.message);
    return false;
  }
}

/**
 * Test 3: State Cleanup
 * Verify that loading an analysis properly clears previous state
 */
async function testStateCleanup() {
  console.log('\n📋 Test 3: State Cleanup');
  
  try {
    // Check console logs for proper state clearing
    const originalConsoleLog = console.log;
    const logs = [];
    
    console.log = function(...args) {
      logs.push(args.join(' '));
      originalConsoleLog.apply(console, args);
    };

    // Switch to history and load an analysis
    const historyTab = document.querySelector('[value="history"]');
    if (historyTab) {
      historyTab.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const analysisCards = document.querySelectorAll('[class*="analysis-card"], .card');
    if (analysisCards.length > 0) {
      const loadButton = analysisCards[0].querySelector('button:contains("Cargar"), button[class*="load"]');
      if (loadButton) {
        loadButton.click();
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // Restore console.log
    console.log = originalConsoleLog;

    // Check if proper logging occurred
    const hasLoadingLogs = logs.some(log => log.includes('Loading saved analysis'));
    const hasImageLogs = logs.some(log => log.includes('Setting preview URL for specific analysis'));
    const hasTabSwitchLogs = logs.some(log => log.includes('Switching to analyze tab'));

    if (hasLoadingLogs && hasImageLogs && hasTabSwitchLogs) {
      console.log('✅ Proper state cleanup and loading sequence detected');
      testResults.stateCleanupTest = true;
      return true;
    } else {
      console.log('❌ Missing expected logging for state cleanup');
      console.log('Logs found:', { hasLoadingLogs, hasImageLogs, hasTabSwitchLogs });
      return false;
    }

  } catch (error) {
    console.log('❌ Error in state cleanup test:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Image Display Fix Tests...\n');
  
  try {
    // Test 1: Specific Image Display
    const imageDisplayResult = await testSpecificImageDisplay();
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.WAIT_TIME));
    
    // Test 2: Multiple Analysis Loading
    const multipleAnalysisResult = await testMultipleAnalysisLoading();
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.WAIT_TIME));
    
    // Test 3: State Cleanup
    const stateCleanupResult = await testStateCleanup();
    
    // Calculate overall success
    testResults.overallSuccess = imageDisplayResult && multipleAnalysisResult && stateCleanupResult;
    
    // Display final results
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Specific Image Display: ${testResults.imageDisplayFix ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Multiple Analysis Test: ${testResults.multipleAnalysisTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`State Cleanup Test: ${testResults.stateCleanupTest ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Overall Success: ${testResults.overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
    
    if (testResults.overallSuccess) {
      console.log('\n🎉 Image display fix is working correctly!');
    } else {
      console.log('\n⚠️ Some issues detected. Check individual test results above.');
    }
    
    return testResults;
    
  } catch (error) {
    console.log('❌ Critical error during testing:', error.message);
    return testResults;
  }
}

// Auto-run tests if this script is executed directly
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    setTimeout(runAllTests, 2000);
  }
}

// Export for manual testing
window.testImageDisplayFix = runAllTests;
window.imageTestResults = testResults;

console.log('📝 Image display test script loaded. Run window.testImageDisplayFix() to start tests manually.');
