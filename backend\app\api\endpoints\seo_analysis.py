"""SEO Analysis API endpoints."""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, HttpUrl
import time
import uuid
import asyncio

from app.services.seo_analyzer import seo_analyzer
from app.services.persistent_seo_service import persistent_seo_service

logger = logging.getLogger(__name__)
router = APIRouter()

# Simple in-memory storage for progress tracking
# In production, this should be Redis or a database
progress_storage: Dict[str, Dict[str, Any]] = {}

class SEOAnalysisRequest(BaseModel):
    """Request model for SEO analysis"""
    url: HttpUrl
    mode: Optional[str] = "page"  # "page" or "website"
    enable_progress: Optional[bool] = False  # Enable real-time progress tracking

class SEORecommendation(BaseModel):
    """SEO recommendation model"""
    category: str
    issue: str
    importance: str
    recommendation: str

class Achievement(BaseModel):
    """Achievement model for positive SEO aspects"""
    category: str
    achievement: str
    description: str
    icon: str
    impact: str

class BasicInfo(BaseModel):
    """Basic SEO information model"""
    title: str
    title_length: int
    meta_description: Optional[str]
    meta_description_length: int
    h1_tags: List[str]
    h1_count: int
    canonical_url: Optional[str]
    language: Optional[str]
    has_viewport: bool
    viewport_content: Optional[str]
    meta_robots: Optional[str]

class TechnicalAudit(BaseModel):
    """Technical audit model"""
    is_https: bool
    status_code: int
    response_time: float

class ImagesInfo(BaseModel):
    """Images information model"""
    total: int
    without_alt: int
    without_alt_percentage: float

class LinksInfo(BaseModel):
    """Links information model"""
    total: int
    internal_count: int
    external_count: int
    internal_links: List[str]
    external_links: List[str]

class TopKeyword(BaseModel):
    """Top keyword model"""
    word: str
    count: int

class RankingKeyword(BaseModel):
    """Ranking keyword model"""
    keyword: str
    position: int
    url: str
    title: str
    snippet: str
    exact_match: bool
    domain_match: bool

class KeywordAnalysis(BaseModel):
    """Keyword analysis model"""
    top_positions: List[RankingKeyword]
    good_positions: List[RankingKeyword]
    opportunities: List[RankingKeyword]
    average_position: float
    total_rankings: int
    best_position: Optional[int] = None
    worst_position: Optional[int] = None

class RankingKeywordsData(BaseModel):
    """Ranking keywords data model"""
    total_keywords_found: int
    ranking_keywords: List[RankingKeyword]
    keyword_analysis: KeywordAnalysis
    search_queries_tested: int
    domain: str
    error: Optional[str] = None

class ContentAnalysis(BaseModel):
    """Content analysis model"""
    word_count: int
    reading_time_minutes: int
    headings_structure: Dict[str, List[str]]
    images: ImagesInfo
    links: LinksInfo
    top_keywords: List[TopKeyword]
    ranking_keywords: Optional[RankingKeywordsData] = None

class PreviewData(BaseModel):
    """Preview data model"""
    title: str
    description: str
    url: str
    og_title: str
    og_description: str
    og_image: str
    twitter_title: str
    twitter_description: str
    twitter_image: str

class MetricValue(BaseModel):
    """Core Web Vitals metric value model"""
    value: Optional[float]
    display_value: Optional[str]
    score: float
    rating: str

class CoreWebVitals(BaseModel):
    """Core Web Vitals model"""
    largest_contentful_paint: MetricValue
    first_input_delay: MetricValue
    cumulative_layout_shift: MetricValue
    first_contentful_paint: MetricValue
    speed_index: MetricValue
    time_to_interactive: MetricValue

class LighthouseScores(BaseModel):
    """Lighthouse scores model"""
    performance: float
    accessibility: float
    best_practices: float
    seo: float

class PerformanceMetrics(BaseModel):
    """Performance metrics model"""
    core_web_vitals: CoreWebVitals
    lighthouse_scores: LighthouseScores
    overall_performance_score: float

class SEOAnalysisResponse(BaseModel):
    """Response model for SEO analysis"""
    status: str
    url: str
    basic_info: BasicInfo
    open_graph: Dict[str, Optional[str]]
    twitter_card: Dict[str, Optional[str]]
    technical_audit: TechnicalAudit
    content_analysis: ContentAnalysis
    performance_metrics: PerformanceMetrics
    seo_checks: Dict[str, bool]
    preview_data: PreviewData
    recommendations: List[SEORecommendation]
    achievements: List[Achievement]
    ai_enhanced: bool
    processing_time: float
    error_message: Optional[str] = None

class SEOAnalysisErrorResponse(BaseModel):
    """Error response model for SEO analysis"""
    status: str
    error_message: str
    url: str
    ai_enhanced: bool

@router.post("/analyze")
async def analyze_seo(request: SEOAnalysisRequest, http_request: Request):
    """
    Perform comprehensive SEO analysis of a website.
    
    This endpoint provides detailed SEO analysis including:
    - Technical SEO audit (meta tags, headers, HTTPS, etc.)
    - Content analysis (word count, headings structure, keywords)
    - Images and links analysis
    - AI-powered recommendations using Gemini
    - Social media preview generation
    - Overall SEO score calculation
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"seo_req_{int(time.time()*1000)}")
    
    logger.info(f"SEO analysis request received. RequestID: {request_id}, URL: {request.url}")
    
    try:
        # Perform SEO analysis
        result = await seo_analyzer.analyze_website(str(request.url))
        
        if result["status"] == "error":
            logger.error(f"SEO analysis failed. RequestID: {request_id}, Error: {result.get('error_message')}")

            # Return a structured error response with 200 status but error content
            return {
                "status": "error",
                "error_message": result.get("error_message", "SEO analysis failed"),
                "url": str(request.url),
                "ai_enhanced": result.get("ai_enhanced", False)
            }
        
        processing_time = time.time() - start_time
        
        logger.info(f"SEO analysis completed successfully. RequestID: {request_id}, ProcessingTime: {processing_time:.2f}s")
        
        # Convert result to response model
        return SEOAnalysisResponse(
            status=result["status"],
            url=result["url"],
            basic_info=BasicInfo(**result["basic_info"]),
            open_graph=result["open_graph"],
            twitter_card=result["twitter_card"],
            technical_audit=TechnicalAudit(**result["technical_audit"]),
            content_analysis=ContentAnalysis(
                word_count=result["content_analysis"]["word_count"],
                reading_time_minutes=result["content_analysis"]["reading_time_minutes"],
                headings_structure=result["content_analysis"]["headings_structure"],
                images=ImagesInfo(**result["content_analysis"]["images"]),
                links=LinksInfo(**result["content_analysis"]["links"]),
                top_keywords=[TopKeyword(**kw) for kw in result["content_analysis"]["top_keywords"]],
                ranking_keywords=RankingKeywordsData(
                    total_keywords_found=result["content_analysis"]["ranking_keywords"]["total_keywords_found"],
                    ranking_keywords=[RankingKeyword(**kw) for kw in result["content_analysis"]["ranking_keywords"]["ranking_keywords"]],
                    keyword_analysis=KeywordAnalysis(**result["content_analysis"]["ranking_keywords"]["keyword_analysis"]),
                    search_queries_tested=result["content_analysis"]["ranking_keywords"]["search_queries_tested"],
                    domain=result["content_analysis"]["ranking_keywords"]["domain"],
                    error=result["content_analysis"]["ranking_keywords"].get("error")
                ) if "ranking_keywords" in result["content_analysis"] else None
            ),
            performance_metrics=PerformanceMetrics(
                core_web_vitals=CoreWebVitals(
                    largest_contentful_paint=MetricValue(**result["performance_metrics"]["core_web_vitals"]["largest_contentful_paint"]),
                    first_input_delay=MetricValue(**result["performance_metrics"]["core_web_vitals"]["first_input_delay"]),
                    cumulative_layout_shift=MetricValue(**result["performance_metrics"]["core_web_vitals"]["cumulative_layout_shift"]),
                    first_contentful_paint=MetricValue(**result["performance_metrics"]["core_web_vitals"]["first_contentful_paint"]),
                    speed_index=MetricValue(**result["performance_metrics"]["core_web_vitals"]["speed_index"]),
                    time_to_interactive=MetricValue(**result["performance_metrics"]["core_web_vitals"]["time_to_interactive"])
                ),
                lighthouse_scores=LighthouseScores(**result["performance_metrics"]["lighthouse_scores"]),
                overall_performance_score=result["performance_metrics"]["overall_performance_score"]
            ),
            seo_checks=result["seo_checks"],
            preview_data=PreviewData(**result["preview_data"]),
            recommendations=[SEORecommendation(**rec) for rec in result["recommendations"]],
            achievements=[Achievement(**ach) for ach in result.get("achievements", [])],
            ai_enhanced=result["ai_enhanced"],
            processing_time=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Unexpected error in SEO analysis. RequestID: {request_id}, Error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during SEO analysis: {str(e)}"
        )

@router.post("/analyze-website")
async def analyze_website_complete(request: SEOAnalysisRequest, http_request: Request):
    """
    Perform comprehensive SEO analysis of an entire website.

    This endpoint provides detailed SEO analysis including:
    - Multi-page crawling and analysis
    - Site-wide technical SEO audit
    - Content analysis across multiple pages
    - Site architecture and internal linking
    - AI-powered recommendations for the entire site
    - Overall site SEO score calculation
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"seo_website_req_{int(time.time()*1000)}")

    logger.info(f"Website SEO analysis request received. RequestID: {request_id}, URL: {request.url}, Progress: {request.enable_progress}")

    try:
        logger.info(f"Starting persistent SEO analysis for URL: {request.url}, Mode: {request.mode}")

        # Use persistent service for long-running analysis
        result = await persistent_seo_service.start_analysis(
            url=str(request.url),
            mode=request.mode or "website",
            user_id=None,  # TODO: Get from auth context
            enable_progress=request.enable_progress or True
        )

        logger.info(f"Persistent SEO analysis started successfully: {result.get('analysis_id')}")
        return result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Unexpected error starting website SEO analysis. RequestID: {request_id}, Error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error al iniciar análisis SEO: {str(e)}"
        )

@router.get("/test")
async def test_seo_service():
    """Test endpoint to verify SEO analysis service connectivity"""
    try:
        # Test with a simple, reliable website
        test_url = "https://example.com"
        result = await seo_analyzer.analyze_website(test_url)

        return {
            "status": "success",
            "message": "SEO analysis service test successful",
            "test_url": test_url,
            "analysis_completed": result["status"] == "success",
            "ai_enhanced": result.get("ai_enhanced", False)
        }

    except Exception as e:
        logger.error(f"SEO service test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"SEO analysis service test failed: {str(e)}"
        }


@router.get("/debug/persistent")
async def debug_persistent_system():
    """Debug endpoint to test persistent SEO system"""
    try:
        from app.db.session import get_db
        from app.db.models import SEOAnalysis

        # Test database connection
        db = next(get_db())
        try:
            count = db.query(SEOAnalysis).count()
            db_status = f"Connected - {count} analyses in database"
        except Exception as e:
            db_status = f"Error: {str(e)}"
        finally:
            db.close()

        # Test persistent service
        try:
            analyses = persistent_seo_service.get_user_analyses(limit=5)
            service_status = f"Working - Retrieved {len(analyses)} analyses"
        except Exception as e:
            service_status = f"Error: {str(e)}"

        return {
            "status": "success",
            "database": db_status,
            "persistent_service": service_status,
            "active_analyses": len(persistent_seo_service.active_analyses),
            "message": "Persistent SEO system debug complete"
        }

    except Exception as e:
        logger.error(f"Debug test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Debug test failed: {str(e)}"
        }

# Legacy function removed - now using persistent_seo_service

@router.get("/progress/{analysis_id}")
async def get_analysis_progress(analysis_id: str):
    """
    Get the progress of a website analysis from persistent storage
    """
    progress_data = persistent_seo_service.get_analysis_progress(analysis_id)

    if not progress_data:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis {analysis_id} not found"
        )

    return progress_data


@router.get("/analyses")
async def get_user_analyses(
    status: Optional[str] = None,
    limit: int = 50,
    user_id: Optional[str] = None  # TODO: Get from auth context
):
    """
    Get list of user's SEO analyses
    """
    analyses = persistent_seo_service.get_user_analyses(
        user_id=user_id,
        limit=limit,
        status_filter=status
    )

    return {
        "analyses": analyses,
        "total": len(analyses)
    }


@router.delete("/analyses/{analysis_id}")
async def cancel_analysis(analysis_id: str):
    """
    Cancel a running analysis
    """
    success = await persistent_seo_service.cancel_analysis(analysis_id)

    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis {analysis_id} not found or cannot be cancelled"
        )

    return {
        "status": "cancelled",
        "analysis_id": analysis_id,
        "message": "Análisis cancelado exitosamente"
    }
