import json
import os
from datetime import datetime
from typing import Optional

LOG_FILE = os.getenv("PROMPT_OPTIMIZATION_LOG", "prompt_optimization_log.jsonl")

# Estructura de log: cada línea es un JSON
# Campos: timestamp, user_id, original_prompt, improved_prompt, tone, platform, goal, model_name, latency_ms, error


def log_prompt_optimization(
    original_prompt: str,
    improved_prompt: str,
    user_id: Optional[str] = None,
    tone: str = "",
    platform: str = "",
    goal: str = "",
    model_name: str = "",
    latency_ms: Optional[int] = None,
    error: str = "",
):
    entry = {
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "user_id": user_id,
        "original_prompt": original_prompt,
        "improved_prompt": improved_prompt,
        "tone": tone,
        "platform": platform,
        "goal": goal,
        "model_name": model_name,
        "latency_ms": latency_ms,
        "error": error,
    }
    with open(LOG_FILE, "a", encoding="utf-8") as f:
        f.write(json.dumps(entry, ensure_ascii=False) + "\n")


def get_prompt_optimization_history(user_id: Optional[str] = None, limit: int = 100):
    if not os.path.exists(LOG_FILE):
        return []
    results = []
    with open(LOG_FILE, "r", encoding="utf-8") as f:
        for line in reversed(list(f)):
            try:
                entry = json.loads(line)
                if user_id and entry.get("user_id") != user_id:
                    continue
                results.append(entry)
                if len(results) >= limit:
                    break
            except Exception:
                continue
    return results
