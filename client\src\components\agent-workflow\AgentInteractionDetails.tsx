import React from 'react';
import { Interaction } from '../../types/unified-agent-types';

interface AgentInteractionDetailsProps {
  interaction: Interaction;
  onClose: () => void;
}

export const AgentInteractionDetails: React.FC<AgentInteractionDetailsProps> = ({
  interaction,
  onClose
}) => {
  return (
    <div className="agent-interaction-details bg-white shadow-lg rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">{interaction.type} Interaction</h3>
        <button 
          onClick={onClose} 
          className="text-gray-500 hover:text-gray-700"
        >
          Close
        </button>
      </div>
      <div className="interaction-content space-y-3">
        <div>
          <span className="font-semibold">Timestamp:</span>
          <span className="ml-2">
            {new Date(interaction.timestamp).toLocaleString()}
          </span>
        </div>
        <div>
          <span className="font-semibold">Agent:</span>
          <span className="ml-2">{interaction.agentName}</span>
        </div>
        <div className="whitespace-pre-wrap">
          {interaction.content}
        </div>
      </div>
    </div>
  );
};
