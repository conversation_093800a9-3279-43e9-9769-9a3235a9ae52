import { motion } from "framer-motion";
import { AITool, filterAITools } from "@/data/ai-tools-data";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, ChevronRight, Info } from "lucide-react";

interface ToolListingViewProps {
  categoryId: string | null;
  onToolSelect: (tool: AITool) => void;
  searchQuery: string;
}

const ToolCard = ({
  tool,
  onSelect,
}: {
  tool: AITool;
  onSelect: (tool: AITool) => void;
}) => {
  const levelColors = {
    Básico: "bg-green-100 text-green-800",
    Intermedio: "bg-blue-100 text-blue-800",
    Avanzado: "bg-purple-100 text-purple-800",
  };

  return (
    <motion.div
      className={`relative bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden h-full flex flex-col ${tool.comingSoon ? "opacity-70" : ""}`}
      whileHover={
        !tool.comingSoon
          ? { y: -8, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }
          : {}
      }
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      {tool.comingSoon && (
        <div className="absolute top-3 right-3 z-10">
          <Badge className="bg-yellow-500 text-white font-bold">
            Próximamente
          </Badge>
        </div>
      )}

      <div className="p-6 flex-grow">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center">
            <span className="text-3xl mr-3">{tool.icon}</span>
            <div>
              <h3 className="text-xl font-black">{tool.name}</h3>
              <Badge
                variant="outline"
                className={`mt-1 ${levelColors[tool.level]}`}
              >
                {tool.level}
              </Badge>
            </div>
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-4">{tool.description}</p>

        <div className="mb-4">
          <h4 className="font-bold text-sm mb-2 text-gray-700">
            Características:
          </h4>
          <ul className="space-y-1">
            {tool.features.slice(0, 3).map((feature, idx) => (
              <li key={idx} className="text-sm flex items-start">
                <ChevronRight
                  size={14}
                  className="min-w-[14px] mt-1 mr-1 text-blue-500"
                />
                <span>{feature}</span>
              </li>
            ))}
            {tool.features.length > 3 && (
              <li className="text-sm text-blue-600 font-medium">
                + {tool.features.length - 3} más
              </li>
            )}
          </ul>
        </div>
      </div>

      <div className="p-4 border-t border-gray-200">
        <Button
          className="w-full bg-white hover:bg-gray-50 text-black border-2 border-black rounded-lg shadow-[4px_4px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all"
          onClick={() => !tool.comingSoon && onSelect(tool)}
          disabled={tool.comingSoon}
        >
          {tool.comingSoon ? "Próximamente" : "Usar Herramienta"}{" "}
          <ArrowRight size={16} className="ml-1" />
        </Button>
      </div>
    </motion.div>
  );
};

export default function ToolListingView({
  categoryId,
  onToolSelect,
  searchQuery,
}: ToolListingViewProps) {
  const filteredTools = filterAITools(categoryId, searchQuery);

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-black mb-3">
          {categoryId
            ? `Herramientas de ${filteredTools[0]?.category || ""}`
            : "Todas las Herramientas de IA"}
        </h2>
        <p className="text-gray-600">
          {categoryId
            ? `Explora nuestras herramientas de IA para ${filteredTools[0]?.category.toLowerCase() || "marketing"}`
            : "Explora todas nuestras herramientas potenciadas por IA para mejorar tu estrategia de marketing"}
        </p>
      </div>

      {filteredTools.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTools.map((tool, index) => (
            <ToolCard key={tool.id} tool={tool} onSelect={onToolSelect} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Info size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-bold mb-2">
            No se encontraron herramientas
          </h3>
          <p className="text-gray-600">
            No hay herramientas que coincidan con tu búsqueda. Intenta con otros
            términos.
          </p>
        </div>
      )}
    </div>
  );
}
