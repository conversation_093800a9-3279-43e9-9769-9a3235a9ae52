import { Workflow } from '../domain/entities/Workflow';
import { UIWorkflowState } from '../types/unified-agent-types';

export class WorkflowAdapter {
  static toUIWorkflowState(workflow: Workflow): UIWorkflowState {
    return {
      id: workflow.id,
      name: workflow.name,
      description: workflow.description,
      status: workflow.status,
      steps: workflow.steps.map(step => ({
        id: step.id,
        description: step.description,
        status: step.status,
        agent: step.agent || ''
      }))
    };
  }
}
