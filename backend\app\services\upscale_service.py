"""
Service for upscale (image quality enhancement) functionality using Stability AI v2beta API.
"""
import httpx
import io
import logging
import hashlib
import base64
from typing import Optional, Dict, Any
from fastapi import HTTPException, UploadFile
from PIL import Image

from app.core.config import settings
from app.schemas.upscale import (
    UpscaleRequest,
    UpscaleResponse,
    UpscaleInitialResponse,
    UpscaleStatusResponse,
    UpscaleMode
)

logger = logging.getLogger(__name__)

# Cache para almacenar resultados de generaciones asíncronas
_generation_cache: Dict[str, Dict[str, Any]] = {}


def get_stability_headers_json() -> Dict[str, str]:
    """Get headers for Stability AI API requests expecting JSON response"""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"
    }


def get_stability_headers_binary() -> Dict[str, str]:
    """Get headers for Stability AI API requests expecting binary response"""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "image/*"
    }


async def validate_upscale_image(image_file: UploadFile) -> bytes:
    """
    Validate and read image file for upscale operations.

    Args:
        image_file: The uploaded image file

    Returns:
        bytes: The image content

    Raises:
        HTTPException: If validation fails
    """
    try:
        # Leer contenido del archivo
        image_content = await image_file.read()

        if not image_content:
            raise HTTPException(status_code=400, detail="Empty image file")

        # Validar que es una imagen válida
        try:
            with Image.open(io.BytesIO(image_content)) as img:
                # Verificar formato soportado
                if img.format.lower() not in ['jpeg', 'jpg', 'png', 'webp']:
                    raise HTTPException(
                        status_code=400,
                        detail="Unsupported image format. Use JPEG, PNG, or WebP"
                    )

                # Verificar dimensiones según el modo
                width, height = img.size
                total_pixels = width * height

                logger.info(f"Image validated: {width}x{height}, format: {img.format}")

        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=400, detail="Invalid image file")

        return image_content

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating image: {e}")
        raise HTTPException(status_code=400, detail="Error processing image file")


async def upscale_image_stability(
    image_file: UploadFile,
    request: UpscaleRequest
) -> UpscaleResponse:
    """
    Upscale an image using Stability AI v2beta API.

    Args:
        image_file: The image file to upscale
        request: The upscale parameters

    Returns:
        UpscaleResponse: The upscaled image and metadata

    Raises:
        HTTPException: If upscale operation fails
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar la imagen
        image_content = await validate_upscale_image(image_file)

        # Validar parámetros según el modo
        if request.mode in [UpscaleMode.CONSERVATIVE, UpscaleMode.CREATIVE]:
            if not request.prompt:
                raise HTTPException(
                    status_code=400,
                    detail=f"Prompt is required for {request.mode} mode"
                )

        # Determinar URL según el modo
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/{request.mode}"

        # Headers para recibir respuesta JSON con base64
        headers = get_stability_headers_json()

        # Preparar archivos para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_content), image_file.content_type or "image/jpeg")
        }

        # Preparar FormData según el modo
        form_data = {
            "output_format": request.output_format
        }

        # Agregar parámetros específicos según el modo
        if request.mode == UpscaleMode.FAST:
            # Fast mode solo necesita imagen y formato
            pass
        elif request.mode == UpscaleMode.CONSERVATIVE:
            form_data["prompt"] = request.prompt
            if request.negative_prompt:
                form_data["negative_prompt"] = request.negative_prompt
            if request.seed and request.seed > 0:
                form_data["seed"] = request.seed
            if request.creativity:
                form_data["creativity"] = request.creativity
        elif request.mode == UpscaleMode.CREATIVE:
            form_data["prompt"] = request.prompt
            if request.negative_prompt:
                form_data["negative_prompt"] = request.negative_prompt
            if request.seed and request.seed > 0:
                form_data["seed"] = request.seed
            if request.creativity:
                form_data["creativity"] = request.creativity
            if request.style_preset:
                form_data["style_preset"] = request.style_preset

        logger.info(f"Upscaling image using Stability AI v2beta - Mode: {request.mode}")
        logger.info(f"URL: {url}")
        logger.info(f"Output format: {request.output_format}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")

            if not image_data:
                raise ValueError("No image data in response")

            return UpscaleResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS"),
                mode=request.mode
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upscale operation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process upscale request: {str(e)}"
        )


async def start_upscale_creative_stability(
    image_file: UploadFile,
    request: UpscaleRequest
) -> UpscaleInitialResponse:
    """
    Start creative upscale operation using Stability AI v2beta API (async).

    Args:
        image_file: The image file to upscale
        request: The upscale parameters (must be creative mode)

    Returns:
        UpscaleInitialResponse: The generation ID for polling

    Raises:
        HTTPException: If operation fails to start
    """
    try:
        # Verificar que es modo creativo
        if request.mode != UpscaleMode.CREATIVE:
            raise HTTPException(
                status_code=400,
                detail="This endpoint is only for creative mode"
            )

        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar la imagen
        image_content = await validate_upscale_image(image_file)

        # Validar prompt requerido
        if not request.prompt:
            raise HTTPException(
                status_code=400,
                detail="Prompt is required for creative mode"
            )

        # URL para creative upscale
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/creative"

        # Headers para la petición
        headers = {
            "Authorization": f"Bearer {settings.STABILITY_API_KEY}"
        }

        # Preparar archivos para upload
        files = {
            "image": ("image.jpg", io.BytesIO(image_content), image_file.content_type or "image/jpeg")
        }

        # Preparar FormData
        form_data = {
            "prompt": request.prompt,
            "output_format": request.output_format
        }

        # Agregar parámetros opcionales
        if request.negative_prompt:
            form_data["negative_prompt"] = request.negative_prompt
        if request.seed and request.seed > 0:
            form_data["seed"] = request.seed
        if request.creativity:
            form_data["creativity"] = request.creativity
        if request.style_preset:
            form_data["style_preset"] = request.style_preset

        logger.info(f"Starting creative upscale using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Prompt: {request.prompt[:50]}...")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            generation_id = result.get("id")

            if not generation_id:
                raise ValueError("No generation ID in response")

            # Almacenar en cache para tracking
            _generation_cache[generation_id] = {
                "status": "IN_PROGRESS",
                "mode": "creative",
                "created_at": logger.info("Generation started")
            }

            return UpscaleInitialResponse(
                generation_id=generation_id,
                message="Creative upscale started successfully"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error starting creative upscale: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start creative upscale: {str(e)}"
        )


async def get_upscale_status_stability(generation_id: str) -> UpscaleStatusResponse:
    """
    Get the status of a creative upscale operation.

    Args:
        generation_id: The generation ID from start_upscale_creative_stability

    Returns:
        UpscaleStatusResponse: Current status and result if completed

    Raises:
        HTTPException: If status check fails
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL para consultar resultado
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/upscale/creative/result/{generation_id}"

        # Headers para recibir respuesta JSON con base64
        headers = get_stability_headers_json()

        logger.info(f"Checking creative upscale status for ID: {generation_id}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)

            logger.info(f"Status check response: {response.status_code}")

            if response.status_code == 202:
                # Aún en progreso
                return UpscaleStatusResponse(
                    success=True,
                    status="IN_PROGRESS"
                )
            elif response.status_code == 200:
                # Completado
                result = response.json()
                image_data = result.get("image")

                if not image_data:
                    raise ValueError("No image data in completed response")

                # Convertir a data URL
                image_url = f"data:image/webp;base64,{image_data}"

                # Actualizar cache
                if generation_id in _generation_cache:
                    _generation_cache[generation_id]["status"] = "COMPLETED"

                return UpscaleStatusResponse(
                    success=True,
                    status="COMPLETED",
                    image_url=image_url,
                    seed=result.get("seed"),
                    finish_reason=result.get("finish_reason", "SUCCESS")
                )
            else:
                # Error
                error_text = response.text
                logger.error(f"Status check error {response.status_code}: {error_text}")

                # Actualizar cache
                if generation_id in _generation_cache:
                    _generation_cache[generation_id]["status"] = "FAILED"

                return UpscaleStatusResponse(
                    success=False,
                    status="FAILED",
                    error=f"Generation failed: {error_text}"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error checking upscale status: {str(e)}", exc_info=True)
        return UpscaleStatusResponse(
            success=False,
            status="FAILED",
            error=f"Failed to check status: {str(e)}"
        )
