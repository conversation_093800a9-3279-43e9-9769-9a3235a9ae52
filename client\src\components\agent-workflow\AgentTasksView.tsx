import React from 'react';
import { Task } from '../../types/unified-agent-types';
import { 
  CheckCircleIcon, 
  ClockIcon, 
  XCircleIcon 
} from 'lucide-react';

interface AgentTasksViewProps {
  tasks: Task[];
  onTaskSelect: (taskId: string) => void;
}

export const AgentTasksView: React.FC<AgentTasksViewProps> = ({
  tasks,
  onTaskSelect
}) => {
  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="text-green-500" />;
      case 'in-progress':
        return <ClockIcon className="text-blue-500" />;
      case 'failed':
        return <XCircleIcon className="text-red-500" />;
      default:
        return <ClockIcon className="text-gray-500" />;
    }
  };

  return (
    <div className="agent-tasks-view space-y-3">
      {tasks.map((task) => (
        <div 
          key={task.id} 
          className="task-card flex items-center bg-gray-50 p-3 rounded-lg cursor-pointer hover:bg-gray-100"
          onClick={() => onTaskSelect(task.id)}
        >
          <div className="mr-3">
            {getStatusIcon(task.status)}
          </div>
          <div className="flex-grow">
            <div className="font-semibold text-sm">{task.title}</div>
            <div className="text-xs text-gray-500">{task.description}</div>
          </div>
          <div className="text-xs text-gray-500">
            {new Date(task.createdAt).toLocaleDateString()}
          </div>
        </div>
      ))}
    </div>
  );
};
