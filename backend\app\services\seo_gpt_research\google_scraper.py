"""
Google Scraper Module
Handles Google search results scraping and content extraction
"""

import logging
import aiohttp
from typing import Dict, Any, List
from urllib.parse import quote_plus

from app.core.config import settings

logger = logging.getLogger(__name__)

class GoogleScraper:
    """Handles Google search results scraping using Serper API."""
    
    def __init__(self):
        self.jina_base_url = "https://r.jina.ai/"
        self.serper_api_key = settings.SERPER_API_KEY
        
        # Validate Serper API
        if not self.serper_api_key:
            logger.warning("⚠️ SERPER_API_KEY not found. Google search functionality will be limited.")
    
    async def get_google_top_results(self, topic: str, num_results: int = 10, target_country: str = "ES", target_language: str = "es") -> Dict[str, Any]:
        """
        Get Google Top results using Serper API with geographic targeting.

        Args:
            topic: Search topic/query
            num_results: Number of results to retrieve
            target_country: Target country code (ES, MX, US, etc.)
            target_language: Target language code (es, en, fr, etc.)

        Returns:
            Structured Google search results
        """
        try:
            if not self.serper_api_key:
                return self._get_fallback_google_results(topic)
            
            url = "https://google.serper.dev/search"
            headers = {
                "X-API-KEY": self.serper_api_key,
                "Content-Type": "application/json"
            }
            
            # Country code mapping for better targeting
            country_mapping = {
                "ES": "es", "MX": "mx", "AR": "ar", "CO": "co", "CL": "cl",
                "PE": "pe", "VE": "ve", "US": "us", "GB": "uk", "FR": "fr",
                "DE": "de", "IT": "it", "BR": "br", "GLOBAL": "us"
            }

            # Language code mapping
            language_mapping = {
                "es": "es", "en": "en", "fr": "fr", "de": "de", "it": "it", "pt": "pt"
            }

            # Get country and language codes
            country_code = country_mapping.get(target_country, "es")
            lang_code = language_mapping.get(target_language, "es")

            payload = {
                "q": topic,
                "num": num_results,
                "gl": country_code,  # Geographic location
                "hl": lang_code      # Interface language
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Extract and enrich results
                        results = await self._process_search_results(data.get('organic', []), num_results)
                        
                        # Extract additional SERP features
                        serp_features = self._extract_serp_features(data)
                        
                        logger.info(f"✅ Retrieved {len(results)} Google results for '{topic}'")
                        return {
                            "status": "success",
                            "query": topic,
                            "total_results": len(results),
                            "results": results,
                            "serp_features": serp_features,
                            "search_metadata": {
                                "search_time": data.get('searchParameters', {}).get('time', 0),
                                "total_results_count": data.get('searchInformation', {}).get('totalResults', 0)
                            }
                        }
                    else:
                        logger.error(f"❌ Serper API returned status {response.status}")
                        return self._get_fallback_google_results(topic)
                        
        except Exception as e:
            logger.error(f"❌ Google search failed: {str(e)}")
            return self._get_fallback_google_results(topic)
    
    async def _process_search_results(self, organic_results: List[Dict], num_results: int) -> List[Dict[str, Any]]:
        """Process and enrich organic search results."""
        results = []

        # Process results without content preview to avoid timeouts
        for i, result in enumerate(organic_results[:num_results]):
            enriched_result = {
                "position": i + 1,
                "title": result.get('title', ''),
                "link": result.get('link', ''),
                "snippet": result.get('snippet', ''),
                "displayed_link": result.get('displayedLink', ''),
                "date": result.get('date', ''),
                "content_preview": result.get('snippet', ''),  # Use snippet as preview for speed
                "domain": self._extract_domain(result.get('link', '')),
                "title_length": len(result.get('title', '')),
                "snippet_length": len(result.get('snippet', ''))
            }
            results.append(enriched_result)

        return results
    
    def _extract_serp_features(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract SERP features from search results."""
        return {
            "people_also_ask": data.get('peopleAlsoAsk', []),
            "related_searches": data.get('relatedSearches', []),
            "knowledge_graph": data.get('knowledgeGraph', {}),
            "featured_snippet": data.get('answerBox', {}),
            "local_results": data.get('places', []),
            "news_results": data.get('news', []),
            "shopping_results": data.get('shopping', []),
            "image_results": data.get('images', [])
        }
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc.replace('www.', '')
        except:
            return "unknown"
    
    async def _extract_content_preview(self, url: str, max_length: int = 500) -> str:
        """Extract content preview from URL using Jina AI."""
        try:
            if not url:
                return ""
            
            jina_url = f"{self.jina_base_url}{url}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(jina_url, timeout=15) as response:
                    if response.status == 200:
                        content = await response.text()
                        # Clean and truncate content for preview
                        preview = self._clean_content_preview(content, max_length)
                        return preview
                    else:
                        return ""
                        
        except Exception as e:
            logger.debug(f"Content preview extraction failed for {url}: {str(e)}")
            return ""
    
    def _clean_content_preview(self, content: str, max_length: int) -> str:
        """Clean and format content preview."""
        try:
            # Remove excessive whitespace and newlines
            cleaned = ' '.join(content.split())
            
            # Truncate content for preview
            if len(cleaned) > max_length:
                cleaned = cleaned[:max_length]
                # Try to end at a sentence boundary
                last_period = cleaned.rfind('.')
                if last_period > max_length * 0.8:  # If period is reasonably close to end
                    cleaned = cleaned[:last_period + 1]
                else:
                    cleaned += "..."
            
            return cleaned
        except:
            return content[:max_length] + "..." if len(content) > max_length else content
    
    def _get_fallback_google_results(self, topic: str) -> Dict[str, Any]:
        """Provide fallback Google results when API is not available."""
        return {
            "status": "fallback",
            "query": topic,
            "total_results": 0,
            "results": [],
            "serp_features": {
                "people_also_ask": [],
                "related_searches": [],
                "knowledge_graph": {},
                "featured_snippet": {},
                "local_results": [],
                "news_results": [],
                "shopping_results": [],
                "image_results": []
            },
            "search_metadata": {
                "search_time": 0,
                "total_results_count": 0
            },
            "message": "Google search API not available"
        }
    
    def analyze_serp_competition(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze competition level from SERP results."""
        try:
            search_results = results.get('results', [])
            
            if not search_results:
                return {"competition_level": "unknown", "analysis": "No results to analyze"}
            
            # Analyze domain authority indicators
            high_authority_domains = [
                'wikipedia.org', 'youtube.com', 'amazon.com', 'facebook.com',
                'linkedin.com', 'twitter.com', 'instagram.com', 'pinterest.com'
            ]
            
            authority_count = 0
            avg_title_length = 0
            avg_snippet_length = 0
            
            for result in search_results:
                domain = result.get('domain', '')
                if any(auth_domain in domain for auth_domain in high_authority_domains):
                    authority_count += 1
                
                avg_title_length += result.get('title_length', 0)
                avg_snippet_length += result.get('snippet_length', 0)
            
            if search_results:
                avg_title_length /= len(search_results)
                avg_snippet_length /= len(search_results)
            
            # Determine competition level
            authority_ratio = authority_count / len(search_results)
            
            if authority_ratio > 0.6:
                competition_level = "high"
            elif authority_ratio > 0.3:
                competition_level = "medium"
            else:
                competition_level = "low"
            
            return {
                "competition_level": competition_level,
                "authority_domains_count": authority_count,
                "authority_ratio": authority_ratio,
                "avg_title_length": avg_title_length,
                "avg_snippet_length": avg_snippet_length,
                "total_results_analyzed": len(search_results),
                "analysis": f"Competition level: {competition_level} based on {authority_count}/{len(search_results)} high-authority domains"
            }
            
        except Exception as e:
            logger.error(f"❌ SERP competition analysis failed: {str(e)}")
            return {"competition_level": "unknown", "analysis": f"Analysis failed: {str(e)}"}
