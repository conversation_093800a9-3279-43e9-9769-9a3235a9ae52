"""
SaaS Security Middleware for SEO Intelligence API
Implements authentication, rate limiting, and usage tracking for production
"""

import time
import hashlib
import logging
from typing import Dict, Optional
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from collections import defaultdict
from datetime import datetime, timedelta

from ..models.seo_intelligence_models import APIUsageStats
from ..db.session import get_db
from ..core.config import get_settings

logger = logging.getLogger(__name__)
security = HTTPBearer(auto_error=False)

# In-memory rate limiting (for production, use Redis)
rate_limit_storage = defaultdict(list)
api_usage_cache = defaultdict(int)

class SEOIntelligenceSecurityMiddleware:
    """Security middleware for SEO Intelligence API endpoints"""
    
    def __init__(self):
        self.settings = get_settings()
        self.rate_limits = {
            'analyze': 30,      # 30 requests per minute
            'generate': 10,     # 10 content generations per minute
            'image-prompts': 20, # 20 image prompt requests per minute
            'optimization-suggestions': 40  # 40 suggestions per minute
        }
        
    async def verify_api_access(
        self, 
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
        db: Session = Depends(get_db)
    ) -> Dict[str, str]:
        """
        Verify API access and extract user information
        For SaaS, this would integrate with your authentication system
        """
        # Extract user info from request
        user_id = self._extract_user_id(request, credentials)
        project_id = self._extract_project_id(request)
        
        # Check rate limits
        await self._check_rate_limits(user_id, request.url.path)
        
        # Log API usage
        await self._log_api_usage(request, user_id, project_id, db)
        
        return {
            'user_id': user_id,
            'project_id': project_id
        }
    
    def _extract_user_id(
        self, 
        request: Request, 
        credentials: Optional[HTTPAuthorizationCredentials]
    ) -> str:
        """Extract user ID from request (implement your auth logic here)"""
        
        # For development/demo, use IP-based identification
        if not credentials:
            # Use IP address as fallback user ID
            client_ip = request.client.host if request.client else "unknown"
            return f"demo_user_{hashlib.md5(client_ip.encode()).hexdigest()[:8]}"
        
        # In production, decode JWT token or validate API key
        try:
            token = credentials.credentials
            # TODO: Implement JWT validation or API key verification
            # For now, use token as user ID (implement proper auth)
            return f"user_{hashlib.md5(token.encode()).hexdigest()[:8]}"
        except Exception:
            return "anonymous_user"
    
    def _extract_project_id(self, request: Request) -> Optional[str]:
        """Extract project ID from request body or headers"""
        # Check headers first
        project_id = request.headers.get('X-Project-ID')
        if project_id:
            return project_id
        
        # For POST requests, project_id might be in body
        # This would be extracted in the endpoint handler
        return None
    
    async def _check_rate_limits(self, user_id: str, endpoint_path: str):
        """Check rate limits for user and endpoint"""
        current_time = time.time()
        
        # Determine endpoint type
        endpoint_type = self._get_endpoint_type(endpoint_path)
        if not endpoint_type:
            return  # No rate limit for unknown endpoints
        
        # Get rate limit for this endpoint
        limit = self.rate_limits.get(endpoint_type, 60)  # Default 60/min
        
        # Clean old requests (older than 1 minute)
        user_requests = rate_limit_storage[f"{user_id}:{endpoint_type}"]
        rate_limit_storage[f"{user_id}:{endpoint_type}"] = [
            req_time for req_time in user_requests 
            if current_time - req_time < 60
        ]
        
        # Check if limit exceeded
        if len(rate_limit_storage[f"{user_id}:{endpoint_type}"]) >= limit:
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Rate limit exceeded",
                    "limit": limit,
                    "window": "1 minute",
                    "retry_after": 60
                }
            )
        
        # Add current request
        rate_limit_storage[f"{user_id}:{endpoint_type}"].append(current_time)
    
    def _get_endpoint_type(self, path: str) -> Optional[str]:
        """Extract endpoint type from path"""
        if '/analyze' in path:
            return 'analyze'
        elif '/generate' in path:
            return 'generate'
        elif '/image-prompts' in path:
            return 'image-prompts'
        elif '/optimization-suggestions' in path:
            return 'optimization-suggestions'
        return None
    
    async def _log_api_usage(
        self, 
        request: Request, 
        user_id: str, 
        project_id: Optional[str],
        db: Session
    ):
        """Log API usage for analytics and billing"""
        try:
            # Extract request info
            endpoint = request.url.path
            method = request.method
            user_agent = request.headers.get('User-Agent', '')
            ip_address = request.client.host if request.client else None
            
            # Create usage record
            usage_record = APIUsageStats(
                user_id=user_id,
                project_id=project_id,
                endpoint=endpoint,
                method=method,
                status_code=200,  # Will be updated in response middleware
                success=True,     # Will be updated in response middleware
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(usage_record)
            db.commit()
            
            # Store in request state for response middleware
            request.state.usage_record_id = usage_record.id
            
        except Exception as e:
            logger.error(f"Failed to log API usage: {e}")

class ContentSecurityValidator:
    """Validate content for security and compliance"""
    
    @staticmethod
    def validate_content_input(content: str) -> bool:
        """Validate content input for security"""
        if not content or len(content.strip()) == 0:
            raise HTTPException(
                status_code=400,
                detail="Content cannot be empty"
            )
        
        # Check content length limits
        if len(content) > 50000:  # 50KB limit
            raise HTTPException(
                status_code=400,
                detail="Content too large (max 50KB)"
            )
        
        # Check for malicious content patterns
        malicious_patterns = [
            '<script',
            'javascript:',
            'data:text/html',
            'vbscript:',
            'onload=',
            'onerror='
        ]
        
        content_lower = content.lower()
        for pattern in malicious_patterns:
            if pattern in content_lower:
                raise HTTPException(
                    status_code=400,
                    detail="Content contains potentially malicious code"
                )
        
        return True
    
    @staticmethod
    def validate_keywords(keywords: list) -> bool:
        """Validate keyword input"""
        if not keywords:
            return True
        
        if len(keywords) > 20:
            raise HTTPException(
                status_code=400,
                detail="Too many keywords (max 20)"
            )
        
        for keyword in keywords:
            if not isinstance(keyword, str):
                raise HTTPException(
                    status_code=400,
                    detail="Keywords must be strings"
                )
            
            if len(keyword) > 100:
                raise HTTPException(
                    status_code=400,
                    detail="Keyword too long (max 100 characters)"
                )
        
        return True

class UsageTracker:
    """Track usage for SaaS billing and analytics"""
    
    @staticmethod
    async def update_usage_stats(
        request: Request,
        response_size: int,
        processing_time: float,
        status_code: int,
        success: bool,
        error_message: Optional[str] = None,
        db: Session = None
    ):
        """Update usage statistics after request completion"""
        try:
            if not hasattr(request.state, 'usage_record_id'):
                return
            
            if not db:
                return
            
            # Update the usage record
            usage_record = db.query(APIUsageStats).filter(
                APIUsageStats.id == request.state.usage_record_id
            ).first()
            
            if usage_record:
                usage_record.response_size = response_size
                usage_record.processing_time = processing_time
                usage_record.status_code = status_code
                usage_record.success = success
                usage_record.error_message = error_message
                
                # Calculate cost (simplified billing)
                usage_record.cost = UsageTracker._calculate_cost(
                    request.url.path, processing_time, response_size
                )
                
                db.commit()
                
        except Exception as e:
            logger.error(f"Failed to update usage stats: {e}")
    
    @staticmethod
    def _calculate_cost(endpoint: str, processing_time: float, response_size: int) -> float:
        """Calculate cost for API usage (simplified billing model)"""
        base_costs = {
            '/analyze': 0.01,      # $0.01 per analysis
            '/generate': 0.05,     # $0.05 per generation
            '/image-prompts': 0.02, # $0.02 per image prompt
            '/optimization-suggestions': 0.01  # $0.01 per suggestion
        }
        
        # Get base cost
        base_cost = 0.01  # Default cost
        for path, cost in base_costs.items():
            if path in endpoint:
                base_cost = cost
                break
        
        # Add processing time cost (for expensive operations)
        time_cost = max(0, (processing_time - 1.0) * 0.001)  # $0.001 per second over 1s
        
        # Add size cost for large responses
        size_cost = max(0, (response_size - 10000) * 0.000001)  # $0.000001 per byte over 10KB
        
        return round(base_cost + time_cost + size_cost, 6)

# Middleware instances
seo_security = SEOIntelligenceSecurityMiddleware()
content_validator = ContentSecurityValidator()
usage_tracker = UsageTracker()
