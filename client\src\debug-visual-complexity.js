// Script de diagnóstico para el Visual Complexity Analyzer
// Ejecutar en la consola del navegador para diagnosticar problemas

console.log('🔍 Iniciando diagnóstico del Visual Complexity Analyzer...');

// 1. Verificar que estamos en la página correcta
function checkCurrentPage() {
  const currentUrl = window.location.href;
  console.log('📍 URL actual:', currentUrl);
  
  if (currentUrl.includes('design-complexity-analyzer')) {
    console.log('✅ Estamos en la página correcta del Visual Complexity Analyzer');
    return true;
  } else {
    console.log('❌ No estamos en la página del Visual Complexity Analyzer');
    console.log('💡 Navega a: http://localhost:3002/dashboard/herramientas/design-complexity-analyzer');
    return false;
  }
}

// 2. Verificar que el componente se está renderizando
function checkComponentRendering() {
  console.log('\n🎨 Verificando renderizado del componente...');
  
  // Buscar elementos característicos del componente
  const title = document.querySelector('h1');
  const tabs = document.querySelectorAll('[role="tablist"] button');
  const debugIndicator = document.querySelector('.fixed.top-4.right-4');
  
  console.log('📝 Título encontrado:', title?.textContent);
  console.log('📋 Pestañas encontradas:', tabs.length);
  console.log('🐛 Indicador de debug:', debugIndicator?.textContent);
  
  if (title && title.textContent.includes('Analizador de Complejidad Visual')) {
    console.log('✅ El componente se está renderizando correctamente');
    return true;
  } else {
    console.log('❌ El componente no se está renderizando');
    return false;
  }
}

// 3. Verificar las pestañas
function checkTabs() {
  console.log('\n📋 Verificando pestañas...');
  
  const tabs = document.querySelectorAll('[role="tablist"] button');
  const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
  
  console.log('Pestañas encontradas:', tabTexts);
  
  const expectedTabs = ['Análisis de Complejidad', 'Resultados Detallados', 'Historial', 'Favoritos'];
  const hasAllTabs = expectedTabs.every(expectedTab => 
    tabTexts.some(tabText => tabText.includes(expectedTab.split(' ')[0]))
  );
  
  if (hasAllTabs) {
    console.log('✅ Todas las pestañas están presentes');
    
    // Verificar contadores
    const historyTab = tabTexts.find(text => text.includes('Historial'));
    const favoritesTab = tabTexts.find(text => text.includes('Favoritos'));
    
    console.log('📊 Pestaña de historial:', historyTab);
    console.log('❤️ Pestaña de favoritos:', favoritesTab);
    
    return true;
  } else {
    console.log('❌ Faltan algunas pestañas');
    console.log('💡 Pestañas esperadas:', expectedTabs);
    return false;
  }
}

// 4. Verificar errores de JavaScript
function checkJavaScriptErrors() {
  console.log('\n🐛 Verificando errores de JavaScript...');
  
  // Capturar errores futuros
  const originalError = console.error;
  const errors = [];
  
  console.error = function(...args) {
    errors.push(args);
    originalError.apply(console, args);
  };
  
  // Verificar si hay errores en React
  const reactErrors = document.querySelectorAll('[data-reactroot] *').length === 0;
  
  if (reactErrors) {
    console.log('❌ Posibles errores de React detectados');
  } else {
    console.log('✅ No se detectaron errores obvios de React');
  }
  
  setTimeout(() => {
    if (errors.length > 0) {
      console.log('❌ Errores de JavaScript detectados:', errors);
    } else {
      console.log('✅ No se detectaron errores de JavaScript');
    }
  }, 1000);
}

// 5. Verificar datos de prueba
function checkMockData() {
  console.log('\n📊 Verificando datos de prueba...');
  
  // Intentar navegar a la pestaña de historial
  const historyTab = Array.from(document.querySelectorAll('[role="tablist"] button'))
    .find(tab => tab.textContent.includes('Historial'));
  
  if (historyTab) {
    console.log('🔄 Navegando a la pestaña de historial...');
    historyTab.click();
    
    setTimeout(() => {
      const analysisCards = document.querySelectorAll('.grid.gap-4 > *');
      console.log('📋 Tarjetas de análisis encontradas:', analysisCards.length);
      
      if (analysisCards.length > 0) {
        console.log('✅ Se encontraron datos de prueba');
        
        // Verificar contenido de las tarjetas
        analysisCards.forEach((card, index) => {
          const title = card.querySelector('h3');
          const filename = card.querySelector('[class*="text-gray-600"]');
          console.log(`📄 Tarjeta ${index + 1}:`, {
            title: title?.textContent?.trim(),
            filename: filename?.textContent?.trim()
          });
        });
      } else {
        console.log('❌ No se encontraron datos de prueba');
        
        // Verificar si hay mensaje de estado vacío
        const emptyState = document.querySelector('.text-center.py-8');
        if (emptyState) {
          console.log('📝 Estado vacío encontrado:', emptyState.textContent.trim());
        }
      }
    }, 1000);
  } else {
    console.log('❌ No se encontró la pestaña de historial');
  }
}

// 6. Verificar servicios y APIs
function checkServices() {
  console.log('\n🔧 Verificando servicios...');
  
  // Verificar si el servicio está disponible
  if (window.designAnalysisService) {
    console.log('✅ designAnalysisService está disponible');
  } else {
    console.log('❌ designAnalysisService no está disponible');
  }
  
  // Verificar conexión a Supabase
  if (window.supabase) {
    console.log('✅ Supabase está disponible');
  } else {
    console.log('❌ Supabase no está disponible');
  }
}

// Función principal de diagnóstico
function runDiagnosis() {
  console.log('🚀 Ejecutando diagnóstico completo...\n');
  
  const results = {
    page: checkCurrentPage(),
    component: false,
    tabs: false,
    errors: false,
    data: false,
    services: false
  };
  
  if (results.page) {
    results.component = checkComponentRendering();
    
    if (results.component) {
      results.tabs = checkTabs();
      checkJavaScriptErrors();
      checkMockData();
      checkServices();
    }
  }
  
  setTimeout(() => {
    console.log('\n📋 Resumen del diagnóstico:');
    console.log('✅ Página correcta:', results.page);
    console.log('✅ Componente renderizado:', results.component);
    console.log('✅ Pestañas presentes:', results.tabs);
    
    if (results.page && results.component && results.tabs) {
      console.log('\n🎉 ¡El Visual Complexity Analyzer parece estar funcionando correctamente!');
      console.log('💡 Si no ves la interfaz, intenta refrescar la página (F5)');
    } else {
      console.log('\n⚠️ Se detectaron algunos problemas. Revisa los detalles arriba.');
    }
  }, 3000);
}

// Ejecutar diagnóstico automáticamente
runDiagnosis();

// Exportar funciones para uso manual
window.visualComplexityDiagnosis = {
  runDiagnosis,
  checkCurrentPage,
  checkComponentRendering,
  checkTabs,
  checkJavaScriptErrors,
  checkMockData,
  checkServices
};

console.log('\n📝 Funciones disponibles en window.visualComplexityDiagnosis:');
console.log('- runDiagnosis(): Ejecuta diagnóstico completo');
console.log('- checkCurrentPage(): Verifica la página actual');
console.log('- checkComponentRendering(): Verifica renderizado del componente');
console.log('- checkTabs(): Verifica las pestañas');
console.log('- checkJavaScriptErrors(): Verifica errores de JavaScript');
console.log('- checkMockData(): Verifica datos de prueba');
console.log('- checkServices(): Verifica servicios disponibles');
