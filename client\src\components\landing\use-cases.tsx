import { motion } from "framer-motion";

export default function UseCases() {
  const cases = [
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
      title: "Marketers Digitales",
      description:
        "Amplía tus capacidades, automatiza tareas repetitivas y enfócate en la estrategia de alto nivel mientras la IA se encarga del resto.",
      quote:
        "Ahora puedo gestionar el marketing de 5 clientes simultáneamente con la misma calidad que antes ofrecía a uno solo.",
      color: "blue",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-pink-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
          />
        </svg>
      ),
      title: "Agencias Creativas",
      description:
        "Escala tu producción, reduce costos operativos y ofrece servicios adicionales sin aumentar tu plantilla humana.",
      quote:
        "Hemos reducido nuestros tiempos de entrega en un 70% y mejorado nuestros márgenes sin sacrificar calidad.",
      color: "pink",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-10 w-10 text-green-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
      title: "Emprendedores",
      description:
        "Accede a marketing profesional de nivel enterprise sin necesidad de contratar un equipo completo ni gastar fortunas.",
      quote:
        "Mi startup tiene ahora el alcance de grandes empresas con un presupuesto de marketing fraccionado.",
      color: "green",
    },
  ];

  return (
    <section id="casosdeuso" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Para Quién Es Ideal
          </h2>
          <p className="text-xl font-bold max-w-3xl mx-auto">
            Soluciones adaptadas para diferentes necesidades
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {cases.map((item, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ y: -10 }}
            >
              <motion.div
                className={`w-20 h-20 bg-${item.color}-100 rounded-full border-3 border-black flex items-center justify-center mb-6 mx-auto`}
                whileHover={{ rotate: 10 }}
                animate={{
                  scale: [1, 1.05, 1],
                  y: [0, -5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              >
                {item.icon}
              </motion.div>
              <h3 className="text-xl font-black text-center mb-4">
                {item.title}
              </h3>
              <p className="text-center mb-6">{item.description}</p>
              <motion.div
                className={`bg-${item.color}-50 rounded-lg border-2 border-black p-4`}
                whileHover={{ y: -5 }}
              >
                <p className="font-medium text-sm italic">"{item.quote}"</p>
              </motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
