import React, { useState } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
// import { auth } from "../../firebase/config";
// import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";

export function AuthForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [activeTab, setActiveTab] = useState("login");
  const { loginMutation, registerMutation } = useAuth();
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Función de inicio de sesión normal
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Add basic validation
    if (!email || !password) {
      toast({
        title: "Campos requeridos",
        description: "Por favor ingresa tu correo electrónico y contraseña",
        variant: "destructive",
      });
      return;
    }

    try {
      // Use the login mutation from the auth hook
      await loginMutation.mutateAsync({
        username: email,
        password: password,
      });

      // Navigation happens automatically via useEffect in AuthProvider
    } catch (error) {
      // Errors are handled in the hook, this is just for additional logging
      console.error("Error al iniciar sesión:", error);
    }
  };

  // Función de registro
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!email || !password || !confirmPassword) {
      toast({
        title: "Campos requeridos",
        description: "Por favor complete todos los campos",
        variant: "destructive",
      });
      return;
    }

    if (password !== confirmPassword) {
      toast({
        title: "Las contraseñas no coinciden",
        description: "Por favor asegúrate que las contraseñas coincidan",
        variant: "destructive",
      });
      return;
    }

    try {
      // Use the register mutation from the auth hook
      await registerMutation.mutateAsync({
        username: email.split("@")[0] || "usuario",
        email: email,
        password: password,
        fullName: email.split("@")[0] || "Usuario",
      });

      // Navigation happens automatically via useEffect in AuthProvider
    } catch (error) {
      // Errors are handled in the hook, this is just for additional logging
      console.error("Error al registrarse:", error);
    }
  };

  // Inicio de sesión con Google (deshabilitado temporalmente)
  const handleGoogleLogin = async () => {
    toast({
      title: "Funcionalidad no disponible",
      description: "El inicio de sesión con Google está temporalmente deshabilitado. Usa el modo desarrollador.",
      variant: "destructive",
    });
  };

  // Inicio de sesión para desarrolladores (mock)
  const handleDeveloperLogin = () => {
    toast({
      title: "Modo Desarrollador",
      description: "Iniciando sesión como desarrollador...",
    });

    // Simular inicio de sesión inmediato
    setTimeout(() => {
      // Fingir una respuesta exitosa
      toast({
        title: "Inicio de sesión exitoso",
        description: "Has iniciado sesión como desarrollador",
      });

      // Navegar al dashboard
      console.log("Redirigiendo en modo desarrollador al dashboard");
      navigate("/dashboard");
    }, 1000);
  };

  const handleResetPassword = async () => {
    if (!email) {
      toast({
        title: "Campo requerido",
        description:
          "Por favor, ingresa tu correo electrónico para restablecer la contraseña",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Funcionalidad no disponible",
      description:
        "El restablecimiento de contraseña se implementará próximamente",
    });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl text-center">
          Plataforma de Marketing
        </CardTitle>
        <CardDescription className="text-center">
          Accede a todas las herramientas de marketing en un solo lugar
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">Iniciar Sesión</TabsTrigger>
            <TabsTrigger value="register">Registrarse</TabsTrigger>
          </TabsList>

          <TabsContent value="login">
            <form onSubmit={handleLogin} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="email">Correo electrónico</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Contraseña</Label>
                  <button
                    type="button"
                    onClick={handleResetPassword}
                    className="text-sm text-primary hover:underline"
                  >
                    ¿Olvidaste tu contraseña?
                  </button>
                </div>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={loginMutation.isPending}
              >
                {loginMutation.isPending
                  ? "Iniciando sesión..."
                  : "Iniciar Sesión"}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="register">
            <form onSubmit={handleRegister} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="register-email">Correo electrónico</Label>
                <Input
                  id="register-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="register-password">Contraseña</Label>
                <Input
                  id="register-password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirmar contraseña</Label>
                <Input
                  id="confirm-password"
                  type="password"
                  placeholder="••••••••"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={registerMutation.isPending}
              >
                {registerMutation.isPending ? "Registrando..." : "Registrarse"}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex flex-col">
        <div className="relative w-full my-4">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-300"></span>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-card px-2 text-muted-foreground">
              O continúa con
            </span>
          </div>
        </div>

        {/* Botón de Google */}
        <Button
          variant="outline"
          type="button"
          className="w-full mb-2"
          onClick={handleGoogleLogin}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            className="w-5 h-5 mr-2"
          >
            <path
              fill="currentColor"
              d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
            />
          </svg>
          Google
        </Button>

        {/* Botón de Desarrollador */}
        {import.meta.env.DEV && (
          <Button
            variant="secondary"
            type="button"
            className="w-full mt-2"
            onClick={handleDeveloperLogin}
          >
            💻 Modo Desarrollador
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
