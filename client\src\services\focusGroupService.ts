import { supabase } from '@/lib/supabase'

/**
 * Interface for Focus Group Simulation data
 */
export interface FocusGroupSimulation {
  id: string
  created_at: string
  updated_at: string
  
  // Usuario propietario
  user_id: string
  
  // Parámetros de entrada
  content: string
  product_category?: string
  context?: string
  custom_questions: string[]
  num_participants: number
  discussion_rounds: number
  
  // Resultados de la simulación
  simulation_results: any
  participants: any[]
  discussions: any[]
  summary: any
  
  // Metadata
  simulation_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string
  
  // Gestión de favoritos y organización
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string
  
  // Estadísticas
  view_count: number
  last_viewed_at?: string
  regeneration_count: number
}

/**
 * Interface for creating a new focus group simulation
 */
export interface CreateFocusGroupSimulationData {
  content: string
  product_category?: string
  context?: string
  custom_questions?: string[]
  num_participants?: number
  discussion_rounds?: number
  simulation_results: any
  participants: any[]
  discussions: any[]
  summary: any
  simulation_duration_ms?: number
  custom_name?: string
  tags?: string[]
  notes?: string
}

/**
 * Interface for updating a focus group simulation
 */
export interface UpdateFocusGroupSimulationData {
  custom_name?: string
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  view_count?: number
  last_viewed_at?: string
  regeneration_count?: number
}

/**
 * Service for managing focus group simulation data via API
 */
export class FocusGroupService {
  private baseUrl = '/api'

  private async getAuthHeaders(): Promise<HeadersInit> {
    const { data: { session } } = await supabase.auth.getSession()

    if (!session?.access_token) {
      throw new Error('No authentication token available')
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`
    }
  }

  /**
   * Get recent focus group simulations (last 5)
   */
  async getRecentSimulations(): Promise<FocusGroupSimulation[]> {
    try {
      const headers = await this.getAuthHeaders()
      const response = await fetch(`${this.baseUrl}/focus-group/recent`, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.simulations || []
    } catch (error) {
      console.error('Error fetching recent focus groups:', error)
      throw new Error('Failed to fetch recent focus groups')
    }
  }

  /**
   * Get favorite focus group simulations
   */
  async getFavoriteSimulations(): Promise<FocusGroupSimulation[]> {
    try {
      const headers = await this.getAuthHeaders()
      const response = await fetch(`${this.baseUrl}/focus-group/favorites`, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.simulations || []
    } catch (error) {
      console.error('Error fetching favorite focus groups:', error)
      throw new Error('Failed to fetch favorite focus groups')
    }
  }

  /**
   * Get a specific focus group simulation by ID
   */
  async getSimulationById(id: string): Promise<FocusGroupSimulation | null> {
    try {
      const headers = await this.getAuthHeaders()
      const response = await fetch(`${this.baseUrl}/focus-group/${id}`, {
        method: 'GET',
        headers
      })

      if (response.status === 404) {
        return null
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.simulation
    } catch (error) {
      console.error('Error fetching focus group simulation:', error)
      throw new Error('Failed to fetch focus group simulation')
    }
  }

  /**
   * Toggle favorite status of a simulation
   */
  async toggleFavorite(id: string): Promise<FocusGroupSimulation> {
    try {
      const headers = await this.getAuthHeaders()
      const response = await fetch(`${this.baseUrl}/focus-group/${id}/toggle-favorite`, {
        method: 'POST',
        headers
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.simulation
    } catch (error) {
      console.error('Error toggling favorite status:', error)
      throw new Error('Failed to toggle favorite status')
    }
  }

  /**
   * Rename a simulation
   */
  async renameSimulation(id: string, newName: string): Promise<FocusGroupSimulation> {
    try {
      const headers = await this.getAuthHeaders()
      const response = await fetch(`${this.baseUrl}/focus-group/${id}/rename`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ name: newName.trim() })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.simulation
    } catch (error) {
      console.error('Error renaming simulation:', error)
      throw new Error('Failed to rename simulation')
    }
  }

  /**
   * Delete a focus group simulation
   */
  async deleteSimulation(id: string): Promise<boolean> {
    try {
      const headers = await this.getAuthHeaders()
      const response = await fetch(`${this.baseUrl}/focus-group/${id}`, {
        method: 'DELETE',
        headers
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return true
    } catch (error) {
      console.error('Error deleting focus group simulation:', error)
      throw new Error('Failed to delete simulation')
    }
  }

  /**
   * Update view count and last viewed timestamp
   * This is handled automatically by the backend when getting a simulation by ID
   */
  async recordView(id: string): Promise<void> {
    // The backend automatically records views when fetching by ID
    await this.getSimulationById(id)
  }

  /**
   * Increment regeneration count
   * This would be handled by the backend when regenerating
   */
  async recordRegeneration(id: string): Promise<void> {
    // This functionality would be implemented in the backend
    // when the regenerate endpoint is called
    console.log(`Recording regeneration for focus group ${id}`)
  }
}

// Export a singleton instance
export const focusGroupService = new FocusGroupService()
