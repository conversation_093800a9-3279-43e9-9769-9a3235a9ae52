/**
 * Debug Upload Process for Visual Complexity Analyzer
 * This script tests the complete upload and save flow to identify where it's failing
 * 
 * Run this in the browser console on the Visual Complexity Analyzer page
 */

console.log('🔍 Starting Upload Process Debug...');

class UploadProcessDebugger {
  constructor() {
    this.results = {
      auth: null,
      testFile: null,
      uploadTest: null,
      saveTest: null,
      storageTest: null,
      errors: []
    };
    this.supabase = window.supabase;
    this.designAnalysisService = window.designAnalysisService;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`[${timestamp}] ${emoji} ${message}`);
  }

  async checkAuth() {
    this.log('🔐 Checking authentication status...');
    
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();
      
      if (error) {
        this.log(`Authentication error: ${error.message}`, 'error');
        return false;
      }
      
      if (!user) {
        this.log('No authenticated user found', 'warn');
        return false;
      }
      
      this.results.auth = {
        userId: user.id,
        email: user.email
      };
      
      this.log(`✅ Authenticated as: ${user.email} (${user.id})`);
      return true;
    } catch (error) {
      this.log(`Failed to check auth: ${error.message}`, 'error');
      return false;
    }
  }

  createTestFile() {
    this.log('📄 Creating test image file...');
    
    // Create a small test image (1x1 pixel PNG)
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(0, 0, 1, 1);
    
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        const testFile = new File([blob], 'test-image.png', { type: 'image/png' });
        this.results.testFile = {
          name: testFile.name,
          size: testFile.size,
          type: testFile.type
        };
        this.log(`✅ Test file created: ${testFile.name} (${testFile.size} bytes)`);
        resolve(testFile);
      }, 'image/png');
    });
  }

  async testUploadImage(file) {
    this.log('📤 Testing uploadImage method...');
    
    try {
      if (!this.designAnalysisService) {
        throw new Error('designAnalysisService not available');
      }

      const uploadResult = await this.designAnalysisService.uploadImage(file, this.results.auth.userId);
      
      this.results.uploadTest = {
        success: true,
        filePath: uploadResult,
        pathType: uploadResult.startsWith('http') ? 'HTTP_URL' : 'FILE_PATH'
      };
      
      this.log(`✅ Upload successful: ${uploadResult}`);
      return uploadResult;
    } catch (error) {
      this.results.uploadTest = {
        success: false,
        error: error.message,
        stack: error.stack
      };
      
      this.log(`❌ Upload failed: ${error.message}`, 'error');
      this.results.errors.push(`Upload error: ${error.message}`);
      return null;
    }
  }

  async testStorageAccess() {
    this.log('🪣 Testing storage access...');
    
    try {
      // Test bucket listing
      const { data: files, error } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, { limit: 5 });

      if (error) {
        throw error;
      }

      this.results.storageTest = {
        success: true,
        filesFound: files.length,
        files: files.map(f => ({ name: f.name, size: f.metadata?.size }))
      };

      this.log(`✅ Storage access successful: ${files.length} files found`);
      return true;
    } catch (error) {
      this.results.storageTest = {
        success: false,
        error: error.message
      };

      this.log(`❌ Storage access failed: ${error.message}`, 'error');
      this.results.errors.push(`Storage error: ${error.message}`);
      return false;
    }
  }

  async testSaveAnalysis(file) {
    this.log('💾 Testing saveAnalysis method...');
    
    try {
      if (!this.designAnalysisService) {
        throw new Error('designAnalysisService not available');
      }

      const testAnalysisData = {
        user_id: this.results.auth.userId,
        tool_type: 'visual-complexity',
        original_filename: file.name,
        file_size: file.size,
        file_type: file.type,
        overall_score: 75,
        complexity_scores: {
          color: 7,
          layout: 8,
          typography: 7,
          elements: 8
        },
        analysis_areas: [
          {
            name: 'Test Area',
            score: 7,
            description: 'Test description'
          }
        ],
        recommendations: [
          {
            category: 'Test',
            issue: 'Test issue',
            importance: 'media',
            recommendation: 'Test recommendation'
          }
        ],
        ai_analysis_summary: 'Test analysis summary',
        agent_message: 'Test agent message',
        visuai_insights: 'Test insights',
        tags: ['debug-test']
      };

      const savedAnalysis = await this.designAnalysisService.saveAnalysis(testAnalysisData, file);
      
      this.results.saveTest = {
        success: true,
        analysisId: savedAnalysis.id,
        file_url: savedAnalysis.file_url,
        hasFileUrl: !!savedAnalysis.file_url
      };
      
      this.log(`✅ Save successful: ${savedAnalysis.id}`);
      this.log(`📁 File URL: ${savedAnalysis.file_url || 'NULL'}`);
      
      return savedAnalysis;
    } catch (error) {
      this.results.saveTest = {
        success: false,
        error: error.message,
        stack: error.stack
      };
      
      this.log(`❌ Save failed: ${error.message}`, 'error');
      this.results.errors.push(`Save error: ${error.message}`);
      return null;
    }
  }

  async testImageRetrieval(filePath) {
    if (!filePath) {
      this.log('⚠️ No file path to test retrieval', 'warn');
      return false;
    }

    this.log('🖼️ Testing image retrieval...');
    
    try {
      const imageUrl = await this.designAnalysisService.getImageUrl(filePath);
      
      if (imageUrl) {
        this.log(`✅ Image retrieval successful: ${imageUrl.substring(0, 50)}...`);
        
        // Test if the URL actually loads
        const img = new Image();
        return new Promise((resolve) => {
          img.onload = () => {
            this.log(`✅ Image loads successfully: ${img.naturalWidth}x${img.naturalHeight}`);
            resolve(true);
          };
          img.onerror = () => {
            this.log(`❌ Image failed to load`, 'error');
            resolve(false);
          };
          img.src = imageUrl;
        });
      } else {
        this.log(`❌ Image retrieval returned null`, 'error');
        return false;
      }
    } catch (error) {
      this.log(`❌ Image retrieval failed: ${error.message}`, 'error');
      return false;
    }
  }

  async runFullTest() {
    this.log('🚀 Starting full upload process test...');
    
    // Step 1: Check authentication
    const authOk = await this.checkAuth();
    if (!authOk) {
      this.log('❌ Cannot proceed without authentication', 'error');
      return this.results;
    }

    // Step 2: Test storage access
    await this.testStorageAccess();

    // Step 3: Create test file
    const testFile = await this.createTestFile();

    // Step 4: Test individual upload
    const uploadPath = await this.testUploadImage(testFile);

    // Step 5: Test complete save process
    const savedAnalysis = await this.testSaveAnalysis(testFile);

    // Step 6: Test image retrieval if we have a file path
    if (savedAnalysis && savedAnalysis.file_url) {
      await this.testImageRetrieval(savedAnalysis.file_url);
    }

    // Step 7: Generate summary
    this.generateSummary();
    
    this.log('✅ Full test complete!');
    return this.results;
  }

  generateSummary() {
    this.log('📊 TEST SUMMARY:');
    this.log(`  Authentication: ${this.results.auth ? '✅ Success' : '❌ Failed'}`);
    this.log(`  Storage Access: ${this.results.storageTest?.success ? '✅ Success' : '❌ Failed'}`);
    this.log(`  Upload Test: ${this.results.uploadTest?.success ? '✅ Success' : '❌ Failed'}`);
    this.log(`  Save Test: ${this.results.saveTest?.success ? '✅ Success' : '❌ Failed'}`);
    
    if (this.results.saveTest?.success) {
      this.log(`  File URL Saved: ${this.results.saveTest.hasFileUrl ? '✅ Yes' : '❌ No'}`);
    }
    
    if (this.results.errors.length > 0) {
      this.log('  Errors found:');
      this.results.errors.forEach(error => {
        this.log(`    - ${error}`);
      });
    }

    // Identify the root cause
    if (!this.results.uploadTest?.success) {
      this.log('🎯 ROOT CAUSE: Upload method is failing', 'error');
    } else if (this.results.saveTest?.success && !this.results.saveTest.hasFileUrl) {
      this.log('🎯 ROOT CAUSE: Upload succeeds but file_url not saved to database', 'error');
    } else if (!this.results.saveTest?.success) {
      this.log('🎯 ROOT CAUSE: Save method is failing', 'error');
    } else {
      this.log('🎯 All tests passed - issue may be intermittent', 'success');
    }
  }

  async cleanupTestData() {
    this.log('🧹 Cleaning up test data...');
    
    if (this.results.saveTest?.analysisId) {
      try {
        await this.designAnalysisService.deleteAnalysis(this.results.saveTest.analysisId);
        this.log('✅ Test analysis deleted');
      } catch (error) {
        this.log(`⚠️ Failed to delete test analysis: ${error.message}`, 'warn');
      }
    }
  }
}

// Create and run the debugger
const uploadDebugger = new UploadProcessDebugger();

// Export to global scope for manual testing
window.uploadDebugger = uploadDebugger;

// Auto-run the test
uploadDebugger.runFullTest().then(results => {
  console.log('🎯 Upload process test results:', results);
  console.log('💡 Use window.uploadDebugger.cleanupTestData() to clean up test data');
  console.log('💡 Use window.uploadDebugger.runFullTest() to run the test again');
}).catch(error => {
  console.error('❌ Upload process test failed:', error);
});
