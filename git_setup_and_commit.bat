@echo off
echo ========================================
echo Emma Studio Git Setup and Commit
echo ========================================
echo.

REM Add Git to PATH for this session
set PATH=%PATH%;C:\Program Files\Git\bin

echo Checking Git installation...
git --version
if errorlevel 1 (
    echo ERROR: Git not found. Please install Git first.
    pause
    exit /b 1
)

echo.
echo Initializing Git repository...
git init
if errorlevel 1 (
    echo ERROR: Failed to initialize Git repository
    pause
    exit /b 1
)

echo.
echo Configuring Git user (if not already configured)...
git config --global user.name "Emma Studio User" 2>nul
git config --global user.email "<EMAIL>" 2>nul

echo.
echo Adding all files to Git...
git add .
if errorlevel 1 (
    echo ERROR: Failed to add files to Git
    pause
    exit /b 1
)

echo.
echo Creating commit "Setting windows"...
git commit -m "Setting windows"
if errorlevel 1 (
    echo ERROR: Failed to create commit
    pause
    exit /b 1
)

echo.
echo ========================================
echo Git setup and commit completed successfully!
echo ========================================
echo.
echo Repository initialized and first commit created.
echo.
echo To add a remote repository and push, run:
echo git remote add origin [your-repository-url]
echo git branch -M main
echo git push -u origin main
echo.
pause
