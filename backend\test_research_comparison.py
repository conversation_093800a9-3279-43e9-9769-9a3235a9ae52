#!/usr/bin/env python3
"""
Test script para comparar lo que se genera vs lo que se guarda
"""

import asyncio
import json
import sys
import os
import requests
import time

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.seo_gpt_research import SEOGPTResearchService

async def test_direct_research():
    """Test directo del servicio de investigación."""
    print("🔍 PRUEBA DIRECTA DEL SERVICIO DE INVESTIGACIÓN")
    print("=" * 60)
    
    research_service = SEOGPTResearchService()
    topic = "beneficios del yoga para principiantes"
    
    print(f"📊 Investigando: '{topic}'")
    
    # Hacer investigación directa
    research_results = await research_service.conduct_comprehensive_research(
        topic=topic,
        target_language="es",
        include_reddit=True,
        include_quora=True
    )
    
    print("\n🎯 RESULTADOS GENERADOS POR EL SERVICIO:")
    print("=" * 60)
    
    # Mostrar estructura completa
    print(f"📝 Status: {research_results.get('status')}")
    print(f"📝 Topic: {research_results.get('topic')}")
    print(f"⏱️ Processing Time: {research_results.get('processing_time'):.2f}s")
    
    # Verificar cada sección
    sections = [
        'intent_analysis',
        'google_results', 
        'social_insights',
        'gpt_reference',
        'entities_and_questions',
        'content_opportunities',
        'research_summary',
        'research_quality_metrics'
    ]
    
    print("\n📊 SECCIONES GENERADAS:")
    for section in sections:
        data = research_results.get(section)
        if data:
            if section == 'google_results':
                results_count = len(data.get('results', []))
                print(f"✅ {section}: {results_count} resultados")
                if results_count > 0:
                    print(f"   - Primer resultado: {data['results'][0].get('title', 'Sin título')[:50]}...")
            elif section == 'social_insights':
                reddit_count = len(data.get('reddit', {}).get('insights', []))
                quora_count = len(data.get('quora', {}).get('insights', []))
                print(f"✅ {section}: Reddit={reddit_count}, Quora={quora_count}")
            elif section == 'entities_and_questions':
                questions_count = len(data.get('common_questions', []))
                entities_count = len(data.get('main_entities', []))
                print(f"✅ {section}: {questions_count} preguntas, {entities_count} entidades")
            else:
                print(f"✅ {section}: Presente")
        else:
            print(f"❌ {section}: Ausente")
    
    # Guardar resultado completo para comparación
    with open('research_generated.json', 'w', encoding='utf-8') as f:
        json.dump(research_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Resultado completo guardado en: research_generated.json")
    print(f"📏 Tamaño total del JSON: {len(json.dumps(research_results))} caracteres")
    
    return research_results

def test_api_research():
    """Test de la API REST."""
    print("\n🌐 PRUEBA DE LA API REST")
    print("=" * 60)
    
    url = "http://localhost:8000/api/v1/seo-gpt/research"
    payload = {
        "topic": "beneficios del yoga para principiantes",
        "target_language": "es",
        "include_reddit": True,
        "include_quora": True
    }
    
    print(f"📡 Enviando request a: {url}")
    print(f"📦 Payload: {payload}")
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            api_results = response.json()
            
            print(f"\n✅ API Response Status: {response.status_code}")
            print(f"📝 Status: {api_results.get('status')}")
            print(f"📝 Message: {api_results.get('message')}")
            
            # Guardar resultado de API para comparación
            with open('research_api.json', 'w', encoding='utf-8') as f:
                json.dump(api_results, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Resultado de API guardado en: research_api.json")
            print(f"📏 Tamaño total del JSON: {len(json.dumps(api_results))} caracteres")
            
            return api_results
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"❌ Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error calling API: {str(e)}")
        return None

def compare_results():
    """Comparar los resultados."""
    print("\n🔍 COMPARACIÓN DE RESULTADOS")
    print("=" * 60)
    
    try:
        # Cargar resultados
        with open('research_generated.json', 'r', encoding='utf-8') as f:
            direct_results = json.load(f)
        
        with open('research_api.json', 'r', encoding='utf-8') as f:
            api_results = json.load(f)
        
        # Comparar tamaños
        direct_size = len(json.dumps(direct_results))
        api_size = len(json.dumps(api_results))
        
        print(f"📏 Tamaño directo: {direct_size} caracteres")
        print(f"📏 Tamaño API: {api_size} caracteres")
        print(f"📊 Diferencia: {direct_size - api_size} caracteres ({((direct_size - api_size) / direct_size * 100):.1f}%)")
        
        # Comparar secciones
        sections = [
            'intent_analysis',
            'google_results', 
            'social_insights',
            'gpt_reference',
            'entities_and_questions',
            'content_opportunities',
            'research_summary',
            'research_quality_metrics'
        ]
        
        print(f"\n📊 COMPARACIÓN POR SECCIONES:")
        for section in sections:
            direct_has = section in direct_results and direct_results[section]
            api_has = section in api_results and api_results[section]
            
            if direct_has and api_has:
                direct_section_size = len(json.dumps(direct_results[section]))
                api_section_size = len(json.dumps(api_results[section]))
                diff = direct_section_size - api_section_size
                print(f"✅ {section}: Directo={direct_section_size}, API={api_section_size}, Diff={diff}")
            elif direct_has and not api_has:
                print(f"❌ {section}: Presente en directo, AUSENTE en API")
            elif not direct_has and api_has:
                print(f"⚠️ {section}: Ausente en directo, presente en API")
            else:
                print(f"❌ {section}: Ausente en ambos")
        
    except Exception as e:
        print(f"❌ Error comparing results: {str(e)}")

async def main():
    """Función principal."""
    print("🚀 INICIANDO PRUEBA DE COMPARACIÓN")
    print("=" * 60)
    
    # Prueba directa
    await test_direct_research()
    
    # Esperar un poco
    time.sleep(2)
    
    # Prueba API
    test_api_research()
    
    # Comparar
    compare_results()
    
    print("\n🎉 PRUEBA COMPLETADA")
    print("Revisa los archivos research_generated.json y research_api.json para ver las diferencias")

if __name__ == "__main__":
    asyncio.run(main())
