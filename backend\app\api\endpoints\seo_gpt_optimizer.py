"""
SEO & GPT Optimizer™ - API Endpoints
Main API endpoints for the SEO & GPT Optimizer tool
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.db.session import get_db
from app.db.seo_gpt_models import SEOGPTProject, ContentAnalysis, GPTRankHistory, KeywordResearch, ProjectStatus, ContentType
from app.services.seo_gpt_research import SEOGPTResearchService
from app.services.gpt_rank_engine import GPTRankEngineService
from app.services.creative_genius_service import CreativeGeniusService
from app.services.ideogram_service import IdeogramService
from app.services.saio_agent_service import SAIOAgentService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
research_service = SEOGPTResearchService()
gpt_rank_service = GPTRankEngineService()
creative_genius_service = CreativeGeniusService()
ideogram_service = IdeogramService()
saio_agent_service = SAIOAgentService()

# Pydantic models for request/response
class ResearchRequest(BaseModel):
    topic: str = Field(..., description="Main topic/keyword to research")
    target_language: str = Field(default="es", description="Target language for analysis")
    include_reddit: bool = Field(default=True, description="Include Reddit insights")
    include_quora: bool = Field(default=True, description="Include Quora insights")
    # Geographic targeting
    target_country: Optional[str] = Field(default="ES", description="Target country code")
    # Additional sources
    include_news: bool = Field(default=False, description="Include news articles")
    # Competitive analysis
    competitor_domains: Optional[List[str]] = Field(default=[], description="Competitor domains to analyze")

class ProjectCreateRequest(BaseModel):
    title: str = Field(..., description="Project title")
    topic: str = Field(..., description="Main topic/keyword")
    target_language: str = Field(default="es", description="Target language")
    content_type: ContentType = Field(default=ContentType.ARTICLE, description="Type of content")
    target_gpt_rank_score: float = Field(default=80.0, description="Target GPT Rank score")
    user_id: Optional[str] = Field(None, description="User ID")

class ContentAnalysisRequest(BaseModel):
    project_id: str = Field(..., description="Project ID")
    content: str = Field(..., description="Content to analyze")
    analysis_type: str = Field(default="full", description="Type of analysis")

class ContentUpdateRequest(BaseModel):
    project_id: str = Field(..., description="Project ID")
    content: str = Field(..., description="Updated content")
    title: Optional[str] = Field(None, description="Updated title")

class BlogGenerationRequest(BaseModel):
    project_id: str = Field(..., description="Project ID")
    topic: str = Field(..., description="Blog topic")
    content_type: str = Field(default="educational", description="Type of content (educational, motivational, balanced)")
    include_images: bool = Field(default=True, description="Whether to generate images")
    num_images: int = Field(default=3, description="Number of images to generate")
    target_length: str = Field(default="medium", description="Target length (short, medium, long)")

class SAIOContentRequest(BaseModel):
    project_id: str = Field(..., description="Project ID")
    topic: str = Field(..., description="Content topic")
    content_type: str = Field(default="how_to", description="SAIO template type (how_to, list_article, comparison)")
    optimize_for: List[str] = Field(default=["chatgpt", "perplexity", "google_sge"], description="AI platforms to optimize for")

@router.post("/research")
async def conduct_research(
    request: ResearchRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Conduct comprehensive research for a topic.
    
    This endpoint initiates a comprehensive research process including:
    - Search intent analysis
    - Google Top 10 + Reddit + Quora scraping
    - GPT reference responses
    - Entity extraction and common questions
    """
    try:
        logger.info(f"🔍 Starting research for topic: '{request.topic}'")
        
        # Conduct research
        research_results = await research_service.conduct_comprehensive_research(
            topic=request.topic,
            target_language=request.target_language,
            include_reddit=request.include_reddit,
            include_quora=request.include_quora,
            target_country=request.target_country or "ES",
            include_news=request.include_news or False
        )
        
        if research_results.get("status") == "error":
            raise HTTPException(status_code=500, detail=research_results.get("error_message"))
        
        # Store research results in database
        research_id = str(uuid.uuid4())
        keyword_research = KeywordResearch(
            research_id=research_id,
            topic=request.topic,
            target_language=request.target_language,
            research_type="comprehensive",
            intent_analysis=research_results.get("intent_analysis"),
            google_results=research_results.get("google_results"),
            social_insights=research_results.get("social_insights"),
            gpt_reference=research_results.get("gpt_reference"),
            entities_and_questions=research_results.get("entities_and_questions"),
            content_opportunities=research_results.get("content_opportunities"),
            research_confidence=research_results.get("research_summary", {}).get("research_confidence", 0.0),
            processing_time=research_results.get("processing_time", 0.0),
            total_sources_analyzed=len(research_results.get("google_results", {}).get("results", [])),
            status="completed"
        )
        
        db.add(keyword_research)
        db.commit()
        db.refresh(keyword_research)
        
        logger.info(f"✅ Research completed and stored for topic: '{request.topic}'")
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "research_id": research_id,
                "message": "Research completed successfully",
                **research_results
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Research failed for topic '{request.topic}': {str(e)}")
        raise HTTPException(status_code=500, detail=f"Research failed: {str(e)}")

@router.post("/projects")
async def create_project(
    request: ProjectCreateRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Create a new SEO GPT Optimizer project."""
    try:
        project_id = str(uuid.uuid4())
        
        project = SEOGPTProject(
            project_id=project_id,
            user_id=request.user_id,
            title=request.title,
            topic=request.topic,
            target_language=request.target_language,
            content_type=request.content_type,
            target_gpt_rank_score=request.target_gpt_rank_score,
            status=ProjectStatus.DRAFT
        )
        
        db.add(project)
        db.commit()
        db.refresh(project)
        
        logger.info(f"✅ Project created: {project_id}")
        
        return JSONResponse(
            status_code=201,
            content={
                "status": "success",
                "project_id": project_id,
                "message": "Project created successfully",
                "project": {
                    "id": project_id,
                    "title": project.title,
                    "topic": project.topic,
                    "status": project.status.value,
                    "created_at": project.created_at.isoformat()
                }
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Project creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")

@router.post("/content/analyze")
async def analyze_content(
    request: ContentAnalysisRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content and calculate GPT Rank Score in real-time.
    
    This endpoint provides real-time content analysis including:
    - GPT Rank Score calculation
    - Component score breakdown
    - Improvement suggestions
    - Content statistics
    """
    try:
        logger.info(f"🧮 Analyzing content for project: {request.project_id}")
        
        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Calculate GPT Rank Score
        gpt_rank_results = await gpt_rank_service.calculate_gpt_rank_score(
            content=request.content,
            topic=project.topic
        )
        
        if gpt_rank_results.get("status") == "error":
            raise HTTPException(status_code=500, detail=gpt_rank_results.get("error_message"))
        
        # Store analysis results
        analysis_id = str(uuid.uuid4())
        content_analysis = ContentAnalysis(
            analysis_id=analysis_id,
            project_id=project.id,
            analysis_type=request.analysis_type,
            semantic_similarity_score=gpt_rank_results["component_scores"]["semantic_similarity"],
            logical_coherence_score=gpt_rank_results["component_scores"]["logical_coherence"],
            authority_signals_score=gpt_rank_results["component_scores"]["authority_signals"],
            citability_score=gpt_rank_results["component_scores"]["citability_score"],
            clarity_score=gpt_rank_results["component_scores"]["clarity_score"],
            completeness_score=gpt_rank_results["component_scores"]["completeness_score"],
            gpt_rank_score=gpt_rank_results["gpt_rank_score"],
            confidence_level=gpt_rank_results["confidence_level"],
            score_grade=gpt_rank_results["score_grade"],
            word_count=gpt_rank_results["content_stats"]["word_count"],
            character_count=gpt_rank_results["content_stats"]["character_count"],
            paragraph_count=gpt_rank_results["content_stats"]["paragraph_count"],
            sentence_count=gpt_rank_results["content_stats"]["sentence_count"],
            improvement_suggestions=gpt_rank_results["improvement_suggestions"],
            processing_time=gpt_rank_results.get("analysis_timestamp", 0.0)
        )
        
        db.add(content_analysis)
        
        # Update project with latest score
        project.current_gpt_rank_score = gpt_rank_results["gpt_rank_score"]
        project.content_text = request.content
        project.content_length = len(request.content)
        project.word_count = gpt_rank_results["content_stats"]["word_count"]
        project.updated_at = datetime.utcnow()
        
        # Update best score if this is better
        if gpt_rank_results["gpt_rank_score"] > project.best_gpt_rank_score:
            project.best_gpt_rank_score = gpt_rank_results["gpt_rank_score"]
        
        # Add to GPT Rank history
        score_change = 0.0
        last_history = db.query(GPTRankHistory).filter(
            GPTRankHistory.project_id == project.id
        ).order_by(GPTRankHistory.created_at.desc()).first()
        
        if last_history:
            score_change = gpt_rank_results["gpt_rank_score"] - last_history.gpt_rank_score
        
        gpt_rank_history = GPTRankHistory(
            project_id=project.id,
            gpt_rank_score=gpt_rank_results["gpt_rank_score"],
            score_change=score_change,
            score_grade=gpt_rank_results["score_grade"],
            semantic_similarity=gpt_rank_results["component_scores"]["semantic_similarity"],
            logical_coherence=gpt_rank_results["component_scores"]["logical_coherence"],
            authority_signals=gpt_rank_results["component_scores"]["authority_signals"],
            citability_score=gpt_rank_results["component_scores"]["citability_score"],
            clarity_score=gpt_rank_results["component_scores"]["clarity_score"],
            completeness_score=gpt_rank_results["component_scores"]["completeness_score"],
            content_length=len(request.content),
            trigger_event="content_analysis",
            user_action="manual_analysis"
        )
        
        db.add(gpt_rank_history)
        db.commit()
        
        logger.info(f"✅ Content analysis completed for project: {request.project_id}")
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "analysis_id": analysis_id,
                "message": "Content analysis completed",
                **gpt_rank_results,
                "score_change": score_change
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Content analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Content analysis failed: {str(e)}")

@router.post("/content/generate-blog")
async def generate_blog_content(
    request: BlogGenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate complete blog content using Creative Genius.

    This endpoint creates a full blog post including:
    - AI-generated content using Creative Genius
    - Contextual images with Ideogram
    - Real-time GPT Rank analysis
    - Structured blog format
    """
    try:
        logger.info(f"🎨 Generating blog content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Create user context for Creative Genius
        user_context = {
            "businessName": project.title,
            "industry": "general",
            "topic": request.topic,
            "target_audience": "general audience"
        }

        logger.info(f"🧠 Using Creative Genius to generate blog content...")

        # Generate breakthrough concept with Creative Genius
        breakthrough = await creative_genius_service.create_breakthrough_content(
            user_context=user_context,
            content_type=request.content_type
        )

        logger.info(f"🚀 Creative breakthrough generated! Viral score: {breakthrough.viral_score}")

        # Generate blog content based on breakthrough
        blog_content = await _generate_blog_content(breakthrough, request, user_context)

        # Generate images if requested
        image_urls = []
        if request.include_images:
            logger.info(f"🖼️ Generating {request.num_images} images for blog...")

            for i in range(request.num_images):
                try:
                    # Create image prompt based on blog section
                    image_prompt = await _create_blog_image_prompt(breakthrough, i, request.num_images)

                    # Generate image with Ideogram
                    image_result = await ideogram_service.generate_image(
                        prompt=image_prompt,
                        aspect_ratio="16:9",
                        style_type="DESIGN"
                    )

                    if image_result and image_result.get("success"):
                        image_urls.append(image_result["image_url"])
                        logger.info(f"✅ Generated blog image {i+1}/{request.num_images}")
                    else:
                        image_urls.append(None)
                        logger.warning(f"❌ Failed to generate image {i+1}")

                except Exception as e:
                    logger.error(f"❌ Error generating image {i+1}: {e}")
                    image_urls.append(None)

        # Update project with generated content
        project.content_text = blog_content
        project.content_length = len(blog_content)
        project.word_count = len(blog_content.split())
        project.updated_at = datetime.utcnow()

        db.commit()

        logger.info(f"✅ Blog content generated successfully for project: {request.project_id}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": request.project_id,
                "message": "Blog content generated successfully",
                "data": {
                    "content": blog_content,
                    "images": image_urls,
                    "creative_concept": {
                        "visual_concept": breakthrough.visual_concept,
                        "hook": breakthrough.hook,
                        "content_angle": breakthrough.content_angle,
                        "viral_score": breakthrough.viral_score,
                        "why_brilliant": breakthrough.why_its_brilliant
                    },
                    "content_stats": {
                        "word_count": len(blog_content.split()),
                        "character_count": len(blog_content),
                        "estimated_reading_time": f"{max(1, len(blog_content.split()) // 200)} min"
                    }
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Blog generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Blog generation failed: {str(e)}")

@router.post("/content/analyze-saio")
async def analyze_saio_content(
    request: ContentAnalysisRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Analyze content with SAIO Agent - the world's first real SAIO optimizer.

    Based on comprehensive research:
    - 73% similarity between ChatGPT and Bing results
    - 100% of Perplexity citations have images
    - 90% of YMYL citations are lists/how-tos
    - E-E-A-T crucial for AI trust
    """
    try:
        logger.info(f"🧠 SAIO Agent analyzing content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Analyze with SAIO Agent
        saio_results = await saio_agent_service.analyze_saio_score(
            content=request.content,
            title=project.title,
            url=""
        )

        if "error" in saio_results:
            raise HTTPException(status_code=500, detail=saio_results["error"])

        logger.info(f"✅ SAIO analysis completed - Score: {saio_results['saio_score']}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": request.project_id,
                "message": "SAIO analysis completed",
                "saio_results": saio_results,
                "analysis_type": "research_based_saio",
                "world_first": True
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SAIO analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SAIO analysis failed: {str(e)}")

@router.post("/content/generate-saio")
async def generate_saio_content(
    request: SAIOContentRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Generate SAIO-optimized content - World's first real SAIO content generator.

    Uses research-backed templates and optimization strategies for:
    - Google SGE
    - ChatGPT
    - Perplexity
    - Bing Chat
    """
    try:
        logger.info(f"🚀 Generating SAIO content for project: {request.project_id}")

        # Get project
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == request.project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Generate SAIO-optimized content
        saio_content = await saio_agent_service.generate_saio_optimized_content(
            topic=request.topic,
            content_type=request.content_type
        )

        if "error" in saio_content:
            raise HTTPException(status_code=500, detail=saio_content["error"])

        # Update project with generated content
        project.content_text = saio_content["content"]
        project.content_length = len(saio_content["content"])
        project.word_count = len(saio_content["content"].split())
        project.updated_at = datetime.utcnow()

        db.commit()

        logger.info(f"✅ SAIO content generated - Score: {saio_content['saio_analysis']['saio_score']}")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": request.project_id,
                "message": "SAIO content generated successfully",
                "data": {
                    "content": saio_content["content"],
                    "saio_analysis": saio_content["saio_analysis"],
                    "template_used": saio_content["template_used"],
                    "optimization_targets": request.optimize_for,
                    "world_first": True,
                    "research_based": True
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ SAIO content generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SAIO content generation failed: {str(e)}")

@router.get("/projects/{project_id}/score")
async def get_project_score(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get current GPT Rank Score for a project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get latest analysis
        latest_analysis = db.query(ContentAnalysis).filter(
            ContentAnalysis.project_id == project.id
        ).order_by(ContentAnalysis.created_at.desc()).first()
        
        # Get score history
        score_history = db.query(GPTRankHistory).filter(
            GPTRankHistory.project_id == project.id
        ).order_by(GPTRankHistory.created_at.desc()).limit(10).all()
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "project_id": project_id,
                "current_score": project.current_gpt_rank_score,
                "best_score": project.best_gpt_rank_score,
                "target_score": project.target_gpt_rank_score,
                "score_grade": latest_analysis.score_grade if latest_analysis else "N/A",
                "confidence_level": latest_analysis.confidence_level if latest_analysis else "unknown",
                "latest_analysis": {
                    "analysis_id": latest_analysis.analysis_id,
                    "created_at": latest_analysis.created_at.isoformat(),
                    "component_scores": {
                        "semantic_similarity": latest_analysis.semantic_similarity_score,
                        "logical_coherence": latest_analysis.logical_coherence_score,
                        "authority_signals": latest_analysis.authority_signals_score,
                        "citability_score": latest_analysis.citability_score,
                        "clarity_score": latest_analysis.clarity_score,
                        "completeness_score": latest_analysis.completeness_score
                    }
                } if latest_analysis else None,
                "score_history": [
                    {
                        "score": history.gpt_rank_score,
                        "change": history.score_change,
                        "grade": history.score_grade,
                        "created_at": history.created_at.isoformat()
                    }
                    for history in score_history
                ]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project score: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project score: {str(e)}")

@router.get("/projects/{project_id}")
async def get_project(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get a specific SEO GPT Optimizer project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "project_id": project.project_id,
                    "title": project.title,
                    "topic": project.topic,
                    "status": project.status.value,
                    "content_type": project.content_type.value,
                    "target_language": project.target_language,
                    "current_gpt_rank_score": project.current_gpt_rank_score,
                    "target_gpt_rank_score": project.target_gpt_rank_score,
                    "best_gpt_rank_score": project.best_gpt_rank_score,
                    "content_text": project.content_text,
                    "content_length": project.content_length,
                    "word_count": project.word_count,
                    "created_at": project.created_at.isoformat(),
                    "updated_at": project.updated_at.isoformat()
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")

@router.patch("/projects/{project_id}")
async def update_project(
    project_id: str,
    updates: dict,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Update a specific SEO GPT Optimizer project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Update allowed fields
        allowed_fields = [
            'title', 'topic', 'content_text', 'content_length', 'word_count',
            'current_gpt_rank_score', 'status', 'target_gpt_rank_score'
        ]

        for field, value in updates.items():
            if field in allowed_fields and hasattr(project, field):
                setattr(project, field, value)

        project.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(project)

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "project_id": project.project_id,
                    "title": project.title,
                    "topic": project.topic,
                    "status": project.status.value,
                    "content_type": project.content_type.value,
                    "target_language": project.target_language,
                    "current_gpt_rank_score": project.current_gpt_rank_score,
                    "target_gpt_rank_score": project.target_gpt_rank_score,
                    "best_gpt_rank_score": project.best_gpt_rank_score,
                    "content_text": project.content_text,
                    "content_length": project.content_length,
                    "word_count": project.word_count,
                    "created_at": project.created_at.isoformat(),
                    "updated_at": project.updated_at.isoformat()
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update project: {str(e)}")

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Delete a specific SEO GPT Optimizer project."""
    try:
        project = db.query(SEOGPTProject).filter(
            SEOGPTProject.project_id == project_id
        ).first()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        db.delete(project)
        db.commit()

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": "Project deleted successfully"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}")

@router.get("/projects")
async def list_projects(
    user_id: Optional[str] = None,
    status: Optional[ProjectStatus] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """List SEO GPT Optimizer projects."""
    try:
        query = db.query(SEOGPTProject)
        
        if user_id:
            query = query.filter(SEOGPTProject.user_id == user_id)
        
        if status:
            query = query.filter(SEOGPTProject.status == status)
        
        projects = query.order_by(SEOGPTProject.updated_at.desc()).offset(offset).limit(limit).all()
        
        project_list = []
        for project in projects:
            project_list.append({
                "project_id": project.project_id,
                "title": project.title,
                "topic": project.topic,
                "status": project.status.value,
                "content_type": project.content_type.value,
                "current_gpt_rank_score": project.current_gpt_rank_score,
                "target_gpt_rank_score": project.target_gpt_rank_score,
                "best_gpt_rank_score": project.best_gpt_rank_score,
                "word_count": project.word_count,
                "created_at": project.created_at.isoformat(),
                "updated_at": project.updated_at.isoformat()
            })
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "projects": project_list,
                "total": len(project_list),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to list projects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")

@router.get("/dashboard/stats")
async def get_dashboard_stats(
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get dashboard analytics statistics."""
    try:
        # Base query
        query = db.query(SEOGPTProject)
        if user_id:
            query = query.filter(SEOGPTProject.user_id == user_id)

        # Total projects
        total_projects = query.count()

        # Active projects (not draft or completed)
        active_projects = query.filter(
            SEOGPTProject.status.in_([ProjectStatus.RESEARCHING, ProjectStatus.WRITING, ProjectStatus.OPTIMIZING])
        ).count()

        # Average scores
        avg_current_score = db.query(func.avg(SEOGPTProject.current_gpt_rank_score)).filter(
            SEOGPTProject.current_gpt_rank_score > 0
        ).scalar() or 0.0

        # Recent activity (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_projects = query.filter(SEOGPTProject.created_at >= thirty_days_ago).count()

        # Total research conducted
        total_research = db.query(KeywordResearch).count()

        # Score improvements this month
        score_improvements = db.query(GPTRankHistory).filter(
            GPTRankHistory.created_at >= thirty_days_ago,
            GPTRankHistory.score_change > 0
        ).count()

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "data": {
                    "total_projects": total_projects,
                    "active_projects": active_projects,
                    "avg_gpt_rank_score": round(avg_current_score, 2),
                    "total_research_conducted": total_research,
                    "improvement_this_month": score_improvements
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Failed to get dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard stats: {str(e)}")

@router.get("/analytics/dashboard-stats")
async def get_dashboard_stats(
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get dashboard analytics statistics."""
    try:
        # Base query
        query = db.query(SEOGPTProject)
        if user_id:
            query = query.filter(SEOGPTProject.user_id == user_id)

        # Total projects
        total_projects = query.count()

        # Projects by status
        active_projects = query.filter(SEOGPTProject.status == "active").count()
        completed_projects = query.filter(SEOGPTProject.status == "completed").count()
        draft_projects = query.filter(SEOGPTProject.status == "draft").count()

        # Average scores
        avg_current_score = db.query(func.avg(SEOGPTProject.current_gpt_rank_score)).filter(
            SEOGPTProject.current_gpt_rank_score > 0
        ).scalar() or 0.0

        avg_target_score = db.query(func.avg(SEOGPTProject.target_gpt_rank_score)).scalar() or 0.0

        # Recent activity (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_projects = query.filter(SEOGPTProject.created_at >= thirty_days_ago).count()

        # Total analyses performed
        total_analyses = db.query(ContentAnalysis).count()
        recent_analyses = db.query(ContentAnalysis).filter(
            ContentAnalysis.created_at >= thirty_days_ago
        ).count()

        # Score improvements (projects that improved in last 30 days)
        score_improvements = db.query(GPTRankHistory).filter(
            GPTRankHistory.created_at >= thirty_days_ago,
            GPTRankHistory.score_change > 0
        ).count()

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "dashboard_stats": {
                    "total_projects": total_projects,
                    "active_projects": active_projects,
                    "completed_projects": completed_projects,
                    "draft_projects": draft_projects,
                    "avg_current_score": round(avg_current_score, 2),
                    "avg_target_score": round(avg_target_score, 2),
                    "recent_projects": recent_projects,
                    "total_analyses": total_analyses,
                    "recent_analyses": recent_analyses,
                    "score_improvements": score_improvements,
                    "success_rate": round((score_improvements / max(recent_analyses, 1)) * 100, 2)
                }
            }
        )

    except Exception as e:
        logger.error(f"❌ Failed to get dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard stats: {str(e)}")

@router.get("/analytics/score-trends")
async def get_score_trends(
    user_id: Optional[str] = None,
    days: int = 30,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """Get score trends over time."""
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # Get score history
        query = db.query(GPTRankHistory).filter(
            GPTRankHistory.created_at >= start_date,
            GPTRankHistory.created_at <= end_date
        )

        if user_id:
            # Join with projects to filter by user
            query = query.join(SEOGPTProject).filter(SEOGPTProject.user_id == user_id)

        score_history = query.order_by(GPTRankHistory.created_at.asc()).all()

        # Group by date
        daily_scores = {}
        for history in score_history:
            date_key = history.created_at.date().isoformat()
            if date_key not in daily_scores:
                daily_scores[date_key] = {
                    "date": date_key,
                    "scores": [],
                    "avg_score": 0.0,
                    "improvements": 0,
                    "total_analyses": 0
                }

            daily_scores[date_key]["scores"].append(history.gpt_rank_score)
            daily_scores[date_key]["total_analyses"] += 1
            if history.score_change > 0:
                daily_scores[date_key]["improvements"] += 1

        # Calculate averages
        trends = []
        for date_data in daily_scores.values():
            date_data["avg_score"] = round(sum(date_data["scores"]) / len(date_data["scores"]), 2)
            del date_data["scores"]  # Remove raw scores to reduce response size
            trends.append(date_data)

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "score_trends": sorted(trends, key=lambda x: x["date"]),
                "period_days": days,
                "total_data_points": len(trends)
            }
        )

    except Exception as e:
        logger.error(f"❌ Failed to get score trends: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get score trends: {str(e)}")

# Helper functions for blog generation
async def _generate_blog_content(breakthrough, request: BlogGenerationRequest, user_context: Dict[str, Any]) -> str:
    """Generate complete blog content using Creative Genius breakthrough."""
    try:
        import google.generativeai as genai
        from app.core.config import settings

        if not settings.GEMINI_API_KEY:
            return _generate_fallback_blog_content(request.topic)

        genai.configure(api_key=settings.GEMINI_API_KEY)
        model = genai.GenerativeModel('gemini-1.5-flash')

        # Determine target length
        length_guide = {
            "short": "800-1200 palabras",
            "medium": "1500-2500 palabras",
            "long": "3000-5000 palabras"
        }
        target_length = length_guide.get(request.target_length, "1500-2500 palabras")

        # Create intelligent blog generation prompt
        prompt = f"""
Crea un blog completo y profesional sobre: "{request.topic}"

CONTEXTO CREATIVO:
- Concepto visual: {breakthrough.visual_concept}
- Ángulo revolucionario: {breakthrough.content_angle}
- Hook principal: {breakthrough.hook}
- Tipo de contenido: {request.content_type}

ESTRUCTURA REQUERIDA:
1. Título atractivo (basado en el hook)
2. Introducción que enganche (2-3 párrafos)
3. 4-6 secciones principales con subtítulos
4. Conclusión poderosa
5. Call-to-action

REQUISITOS:
- Longitud: {target_length}
- Tono: Profesional pero accesible
- Incluir datos y estadísticas cuando sea relevante
- Usar el hook creativo como hilo conductor
- Optimizado para SEO y GPT Rank
- En español

IMPORTANTE: Crea contenido original, útil y que realmente aporte valor. No contenido genérico de marketing.
"""

        response = model.generate_content(prompt)

        if response and response.text:
            return response.text.strip()
        else:
            return _generate_fallback_blog_content(request.topic)

    except Exception as e:
        logger.error(f"Error generating blog content: {e}")
        return _generate_fallback_blog_content(request.topic)

async def _create_blog_image_prompt(breakthrough, image_index: int, total_images: int) -> str:
    """Create contextual image prompt for blog sections."""

    # Define image purposes based on position
    image_purposes = [
        "Hero image - main concept visualization",
        "Supporting concept illustration",
        "Data or process visualization",
        "Conclusion or call-to-action visual"
    ]

    # Get purpose for this image
    purpose_index = min(image_index, len(image_purposes) - 1)
    purpose = image_purposes[purpose_index]

    # Create contextual prompt
    base_prompt = f"""
{breakthrough.ideogram_prompt}

Purpose: {purpose}
Style: Professional, clean, modern design
Context: Blog article illustration
Art direction: {breakthrough.art_direction}

High quality, 16:9 aspect ratio, suitable for blog content.
"""

    return base_prompt.strip()

def _generate_fallback_blog_content(topic: str) -> str:
    """Generate fallback blog content when AI is not available."""
    return f"""
# {topic}

## Introducción

En este artículo exploraremos todo lo que necesitas saber sobre {topic}.

## ¿Qué es {topic}?

[Contenido generado automáticamente - Se recomienda editar manualmente]

## Beneficios principales

1. Primer beneficio importante
2. Segundo beneficio clave
3. Tercer beneficio relevante

## Cómo implementar

Pasos básicos para comenzar:

1. Primer paso
2. Segundo paso
3. Tercer paso

## Conclusión

{topic} es un tema importante que merece atención. Con la información correcta, puedes aprovechar al máximo sus beneficios.

## ¿Listo para comenzar?

Comienza hoy mismo aplicando estos conceptos en tu día a día.
"""
