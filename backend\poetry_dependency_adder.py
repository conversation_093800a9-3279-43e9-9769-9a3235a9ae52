#!/usr/bin/env python3
"""
Poetry Dependency Adder - Incrementally adds dependencies from requirements.txt to Poetry.

This script helps migrate from requirements.txt to Poetry by adding dependencies in batches.
It parses requirements.txt and adds dependencies to Poetry, tracking progress to resume later.
"""

import os
import sys
import subprocess
import json
import re
import time
from pathlib import Path

# ANSI colors for prettier output
GREEN = "\033[0;32m"
YELLOW = "\033[1;33m"
RED = "\033[0;31m"
BLUE = "\033[0;34m"
NC = "\033[0m"  # No Color

# Progress tracking file
PROGRESS_FILE = "poetry_migration_progress.json"

def check_poetry_installation():
    """Check if Poetry is installed."""
    try:
        subprocess.run(["poetry", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

def parse_requirements(file_path="requirements.txt"):
    """Parse requirements from requirements.txt."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        requirements = []
        for line in content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Handle special cases
            line = re.sub(r'--.*$', '', line).strip()  # Remove options like --no-binary
            
            # Extract package name and version constraint
            match = re.match(r'^([a-zA-Z0-9_.-]+)(?:[<>=!~]+.*)?$', line)
            if match:
                package_name = match.group(1).lower()
                requirements.append({
                    'name': package_name,
                    'raw': line,
                    'added': False
                })
        
        return requirements
    except FileNotFoundError:
        print(f"{RED}Error: {file_path} not found{NC}")
        return []

def load_progress():
    """Load progress from file."""
    if not os.path.exists(PROGRESS_FILE):
        return []
    
    try:
        with open(PROGRESS_FILE, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return []

def save_progress(requirements):
    """Save progress to file."""
    with open(PROGRESS_FILE, 'w') as f:
        json.dump(requirements, f, indent=2)

def add_dependency(package, requirements, batch_index, total_batches):
    """Add a single dependency to Poetry."""
    print(f"{BLUE}[{batch_index}/{total_batches}] Adding {package['name']}{NC}")
    
    try:
        result = subprocess.run(
            ["poetry", "add", package['name']], 
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            print(f"{GREEN}✓ Successfully added {package['name']}{NC}")
            package['added'] = True
            return True
        else:
            print(f"{RED}✗ Failed to add {package['name']}: {result.stderr.strip()}{NC}")
            return False
    except subprocess.SubprocessError as e:
        print(f"{RED}✗ Error adding {package['name']}: {str(e)}{NC}")
        return False

def add_dependencies_in_batches(requirements, batch_size=5, start_from=0):
    """Add dependencies to Poetry in batches."""
    total_deps = len(requirements)
    to_add = [pkg for pkg in requirements if not pkg.get('added', False)]
    
    if not to_add:
        print(f"{GREEN}All dependencies have already been added!{NC}")
        return requirements
    
    print(f"{BLUE}Starting dependency addition from index {start_from}{NC}")
    print(f"{BLUE}Will add {len(to_add)} remaining dependencies out of {total_deps} total{NC}")
    
    for i in range(start_from, len(requirements), batch_size):
        batch = requirements[i:i+batch_size]
        print(f"\n{YELLOW}Processing batch {i//batch_size + 1}/{(total_deps+batch_size-1)//batch_size}{NC}")
        
        for index, package in enumerate(batch):
            if package.get('added', False):
                print(f"{GREEN}✓ {package['name']} already added, skipping{NC}")
                continue
                
            success = add_dependency(package, requirements, i + index + 1, total_deps)
            
            # Save progress after each package
            save_progress(requirements)
            
            if not success:
                print(f"{YELLOW}Continuing with next package...{NC}")
            
            # Small delay to avoid overwhelming the system
            time.sleep(0.5)
        
        # Ask user if they want to continue after each batch
        if i + batch_size < len(requirements):
            print(f"\n{BLUE}Completed batch {i//batch_size + 1}/{(total_deps+batch_size-1)//batch_size}{NC}")
            choice = input(f"{YELLOW}Continue with next batch? (Y/n): {NC}")
            if choice.lower() == 'n':
                print(f"{BLUE}Pausing migration. Run the script again to continue.{NC}")
                return requirements
    
    print(f"\n{GREEN}All dependencies have been processed!{NC}")
    return requirements

def main():
    print(f"{BLUE}Poetry Dependency Adder{NC}")
    print(f"{BLUE}======================{NC}")
    
    if not check_poetry_installation():
        print(f"{RED}Poetry is not installed. Please install Poetry first:{NC}")
        print("curl -sSL https://install.python-poetry.org | python3 -")
        return
    
    print(f"{GREEN}Poetry is installed.{NC}")
    
    # Check if pyproject.toml exists
    if not os.path.exists("pyproject.toml"):
        print(f"{RED}pyproject.toml not found. Please create it first using poetry init.{NC}")
        return
    
    # Load requirements
    requirements = parse_requirements()
    if not requirements:
        print(f"{RED}No requirements found in requirements.txt.{NC}")
        return
    
    # Load progress
    saved_progress = load_progress()
    if saved_progress:
        # Merge saved progress with current requirements
        for saved_pkg in saved_progress:
            for i, pkg in enumerate(requirements):
                if pkg['name'] == saved_pkg['name']:
                    requirements[i] = saved_pkg
                    break
    
    # Get starting index
    added_count = sum(1 for pkg in requirements if pkg.get('added', False))
    
    print(f"{GREEN}Found {len(requirements)} requirements in requirements.txt.{NC}")
    print(f"{GREEN}{added_count} dependencies have already been added.{NC}")
    
    # Get batch size from user
    batch_size_input = input(f"{YELLOW}Enter batch size (default: 5): {NC}")
    batch_size = int(batch_size_input) if batch_size_input.isdigit() else 5
    
    # Add dependencies
    requirements = add_dependencies_in_batches(requirements, batch_size, added_count)
    
    # Save final progress
    save_progress(requirements)
    
    # Show summary
    added_count = sum(1 for pkg in requirements if pkg.get('added', True))
    print(f"\n{BLUE}=== Migration Summary ==={NC}")
    print(f"{GREEN}{added_count}/{len(requirements)} dependencies added to Poetry.{NC}")
    
    if added_count == len(requirements):
        print(f"\n{GREEN}All dependencies have been added to Poetry!{NC}")
        print(f"\n{BLUE}Next steps:{NC}")
        print(f"1. Run {YELLOW}poetry install{NC} to install all dependencies")
        print(f"2. Test your application with Poetry")
        print(f"3. Update your Docker files to use Poetry")
        print(f"4. Update CI/CD pipelines to use Poetry")
    else:
        print(f"\n{YELLOW}Some dependencies could not be added. Check the error messages above.{NC}")
        print(f"Run this script again to continue the migration.")

if __name__ == "__main__":
    main()