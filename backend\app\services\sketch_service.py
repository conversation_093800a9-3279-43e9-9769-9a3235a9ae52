"""Service for sketch-to-image generation using Stability AI v2beta API."""

import logging
import httpx
import io
from typing import Optional
from fastapi import HTTPEx<PERSON>, UploadFile
from PIL import Image

from app.schemas.sketch import SketchToImageRequest, SketchToImageResponse
from app.core.config import settings

logger = logging.getLogger(__name__)


def get_stability_headers_json():
    """Get headers for Stability AI API requests expecting JSON response."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"
    }


async def validate_sketch_image(image_file: UploadFile) -> bytes:
    """
    Validate and process the sketch image file.
    
    Args:
        image_file: The uploaded image file
        
    Returns:
        bytes: The processed image bytes
        
    Raises:
        HTTPException: If validation fails
    """
    # Verificar que hay un archivo
    if not image_file:
        raise HTTPException(status_code=400, detail="No image file provided")
    
    # Verificar el tipo de contenido
    allowed_types = ["image/jpeg", "image/png", "image/webp"]
    if image_file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported image format. Allowed: {', '.join(allowed_types)}"
        )
    
    # Leer el contenido del archivo
    try:
        image_content = await image_file.read()
    except Exception as e:
        logger.error(f"Error reading image file: {str(e)}")
        raise HTTPException(status_code=400, detail="Error reading image file")
    
    # Verificar el tamaño del archivo (máximo 10MB)
    max_size = 10 * 1024 * 1024  # 10MB
    if len(image_content) > max_size:
        raise HTTPException(
            status_code=413, 
            detail="Image file too large. Maximum size is 10MB"
        )
    
    # Validar que es una imagen válida usando PIL
    try:
        image = Image.open(io.BytesIO(image_content))
        
        # Verificar dimensiones mínimas (64px por lado según documentación)
        width, height = image.size
        if width < 64 or height < 64:
            raise HTTPException(
                status_code=400,
                detail="Image dimensions too small. Minimum 64x64 pixels"
            )
        
        # Verificar límite de píxeles totales (9,437,184 según documentación)
        max_pixels = 9437184
        if width * height > max_pixels:
            raise HTTPException(
                status_code=400,
                detail=f"Image too large. Maximum {max_pixels} pixels total"
            )
        
        # Verificar aspect ratio (entre 1:2.5 y 2.5:1)
        aspect_ratio = width / height
        if aspect_ratio < 0.4 or aspect_ratio > 2.5:
            raise HTTPException(
                status_code=400,
                detail="Invalid aspect ratio. Must be between 1:2.5 and 2.5:1"
            )
        
        logger.info(f"Image validation successful: {width}x{height}, {len(image_content)} bytes")
        return image_content
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating image: {str(e)}")
        raise HTTPException(status_code=400, detail="Invalid image file")


async def sketch_to_image_stability(
    image_file: UploadFile, 
    request: SketchToImageRequest
) -> SketchToImageResponse:
    """
    Generate an image from a sketch using Stability AI v2beta API.
    
    Args:
        image_file: The sketch image file
        request: The generation parameters
        
    Returns:
        SketchToImageResponse: The generated image and metadata
        
    Raises:
        HTTPException: If generation fails
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar la imagen
        image_content = await validate_sketch_image(image_file)
        
        # URL de la API v2beta para sketch-to-image
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/control/sketch"
        
        # Headers para recibir respuesta JSON con base64
        headers = get_stability_headers_json()

        # Preparar FormData
        form_data = {
            "prompt": request.prompt,
            "control_strength": request.control_strength,
            "output_format": request.output_format or "png"
        }

        # Agregar campos opcionales
        if request.negative_prompt:
            form_data["negative_prompt"] = request.negative_prompt
        if request.seed and request.seed > 0:
            form_data["seed"] = request.seed
        if request.style_preset:
            form_data["style_preset"] = request.style_preset

        # Preparar el archivo para upload
        files = {
            "image": ("sketch.jpg", io.BytesIO(image_content), image_file.content_type)
        }

        logger.info(f"Generating image from sketch using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Prompt: {request.prompt[:100]}...")
        logger.info(f"Control strength: {request.control_strength}")
        logger.info(f"Output format: {request.output_format}")

        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )
            
            logger.info(f"Stability AI response status: {response.status_code}")
            
            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            result = response.json()
            image_data = result.get("image")
            
            if not image_data:
                raise ValueError("No image data in response")

            return SketchToImageResponse(
                image=image_data,
                seed=result.get("seed"),
                finish_reason=result.get("finish_reason", "SUCCESS")
            )

    except httpx.RequestError as e:
        logger.error("Error making request to Stability AI for sketch-to-image: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Error generating image from sketch: {str(e)}"
        ) from e
    except Exception as e:
        logger.error("Unexpected error in sketch-to-image: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        ) from e
