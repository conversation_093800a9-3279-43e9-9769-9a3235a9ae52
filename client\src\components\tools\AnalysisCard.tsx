import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Heart,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  RefreshCw,
  Check,
  X,
  FileImage,
  BarChart3,
  ImageIcon
} from 'lucide-react';
import type { DesignAnalysis } from '@/lib/supabase';
import { designAnalysisService } from '@/services/designAnalysisService';

// Componente para mostrar una tarjeta de análisis de diseño guardado
interface AnalysisCardProps {
  analysis: DesignAnalysis;
  onLoad: () => void;
  onToggleFavorite: () => void;
  onRename: (newName: string) => void;
  onDelete: () => void;
  onRegenerate: () => void;
  isRenaming: boolean;
  onStartRename: () => void;
  onCancelRename: () => void;
  renameValue: string;
  onRenameValueChange: (value: string) => void;
}

function AnalysisCard({
  analysis,
  onLoad,
  onToggleFavorite,
  onRename,
  onDelete,
  onRegenerate,
  isRenaming,
  onStartRename,
  onCancelRename,
  renameValue,
  onRenameValueChange,
}: AnalysisCardProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Cleanup previous object URL when imageUrl changes
  useEffect(() => {
    return () => {
      if (imageUrl && imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [imageUrl]);

  // Load image from Supabase Storage with enhanced error handling
  useEffect(() => {
    let currentImageUrl: string | null = null;

    const loadImage = async () => {
      if (!analysis.file_url) {
        console.log(`📋 Analysis ${analysis.id} has no file_url - skipping image load`);
        setImageLoading(false);
        setImageError(true);
        return;
      }

      try {
        console.log(`🖼️ Loading image for analysis ${analysis.id}:`, {
          filename: analysis.original_filename,
          file_url: analysis.file_url,
          urlType: analysis.file_url.startsWith('http') ? 'HTTP_URL' : 'FILE_PATH'
        });

        setImageLoading(true);
        setImageError(false);

        const url = await designAnalysisService.getImageUrl(analysis.file_url);
        if (url) {
          currentImageUrl = url;
          setImageUrl(url);
          console.log(`✅ Image loaded successfully for analysis ${analysis.id}`);
        } else {
          console.warn(`❌ Failed to get image URL for analysis ${analysis.id}`);
          setImageError(true);
        }
      } catch (error) {
        console.error(`💥 Error loading image for analysis card ${analysis.id}:`, {
          error,
          filename: analysis.original_filename,
          file_url: analysis.file_url
        });
        setImageError(true);
      } finally {
        setImageLoading(false);
      }
    };

    loadImage();

    // Cleanup function to revoke object URLs and prevent memory leaks
    return () => {
      if (currentImageUrl && currentImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(currentImageUrl);
      }
    };
  }, [analysis.file_url, analysis.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDisplayName = () => {
    return analysis.custom_name || `Análisis ${formatDate(analysis.created_at)}`;
  };

  const handleRenameSubmit = () => {
    if (renameValue.trim()) {
      onRename(renameValue.trim());
    } else {
      onCancelRename();
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <Card className="p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-4">
        {/* Image Thumbnail */}
        <div className="flex-shrink-0">
          <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
            {imageLoading ? (
              <div className="w-full h-full flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : imageError || !imageUrl ? (
              <div className="w-full h-full flex items-center justify-center bg-gray-50">
                <ImageIcon className="h-8 w-8 text-gray-400" />
              </div>
            ) : (
              <img
                src={imageUrl}
                alt={analysis.original_filename}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {isRenaming ? (
            <div className="flex items-center gap-2 mb-2">
              <Input
                value={renameValue}
                onChange={(e) => onRenameValueChange(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleRenameSubmit();
                  if (e.key === 'Escape') onCancelRename();
                }}
                className="text-lg font-semibold"
                autoFocus
              />
              <Button size="sm" onClick={handleRenameSubmit}>
                <Check className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={onCancelRename}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
              {getDisplayName()}
            </h3>
          )}

          <div className="flex items-center gap-3 mb-3">
            <div className="flex items-center gap-1">
              <FileImage className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600 truncate">
                {analysis.original_filename}
              </span>
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getScoreBadgeColor(analysis.overall_score)}`}>
              <BarChart3 className="h-3 w-3 inline mr-1" />
              {analysis.overall_score}/100
            </div>
          </div>

          {analysis.ai_analysis_summary && (
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
              {analysis.ai_analysis_summary}
            </p>
          )}

          <div className="flex items-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {formatDate(analysis.created_at)}
            </div>
            {analysis.view_count > 0 && (
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                {analysis.view_count} vistas
              </div>
            )}
            {analysis.regeneration_count > 0 && (
              <div className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3" />
                {analysis.regeneration_count} regeneraciones
              </div>
            )}
            {analysis.file_size && (
              <div className="flex items-center gap-1">
                <FileImage className="h-3 w-3" />
                {(analysis.file_size / 1024).toFixed(1)} KB
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 ml-4">
          <Button
            size="sm"
            variant="outline"
            onClick={onToggleFavorite}
            className={analysis.is_favorite ? "text-red-600 border-red-200" : ""}
          >
            {analysis.is_favorite ? (
              <Heart className="h-4 w-4 fill-current" />
            ) : (
              <Heart className="h-4 w-4" />
            )}
          </Button>

          <Button size="sm" variant="outline" onClick={onStartRename}>
            <Edit3 className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onRegenerate} className="text-blue-600 hover:text-blue-700">
            <RefreshCw className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onDelete} className="text-red-600 hover:text-red-700">
            <Trash2 className="h-4 w-4" />
          </Button>

          <Button size="sm" onClick={onLoad}>
            Cargar
          </Button>
        </div>
      </div>
    </Card>
  );
}

export default AnalysisCard;
