#!/usr/bin/env python3
"""
Test script for the Smart Routing System in Emma Studio

This script demonstrates the new intelligent routing capabilities that fix
the architectural problems in the agent orchestration system.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.schemas.crew import AgentChatRequest
from app.services.smart_routing_service import SmartRoutingService, RoutingDecision


def load_test_agent_configs():
    """Load test agent configurations for demonstration."""
    return {
        "emma": {
            "name": "<PERSON>",
            "agent_type": "orchestrator",
            "routing_mode": "orchestration_only",
            "orchestration_triggers": [
                "multi_agent_required",
                "complex_strategy_needed",
                "cross_domain_expertise"
            ],
            "bypass_conditions": [
                "explicit_agent_request",
                "simple_single_domain_task"
            ]
        },
        "content agent": {
            "name": "Content Agent",
            "agent_type": "autonomous",
            "routing_mode": "direct_access",
            "direct_access_keywords": [
                "content", "blog", "article", "copy", "writing",
                "contenido", "escribir", "redactar"
            ],
            "specialties": [
                "content_creation", "copywriting", "blog_writing"
            ]
        },
        "seo agent": {
            "name": "SEO Agent",
            "agent_type": "autonomous",
            "routing_mode": "direct_access",
            "direct_access_keywords": [
                "seo", "keywords", "optimization", "ranking",
                "search", "optimización", "posicionamiento"
            ],
            "specialties": [
                "keyword_research", "on_page_seo", "technical_seo"
            ]
        }
    }


def test_routing_scenarios():
    """Test various routing scenarios to demonstrate the smart routing system."""
    
    # Initialize smart routing service
    agent_configs = load_test_agent_configs()
    smart_routing = SmartRoutingService(agent_configs)
    
    # Test scenarios
    test_cases = [
        # Direct agent access scenarios
        {
            "name": "Direct Content Agent Request",
            "request": AgentChatRequest(
                agent_id="content",
                message="Write a blog post about artificial intelligence",
                context={}
            ),
            "expected": RoutingDecision.DIRECT_TO_AGENT
        },
        {
            "name": "Direct SEO Agent Request",
            "request": AgentChatRequest(
                agent_id="seo",
                message="Optimize this content for search engines",
                context={}
            ),
            "expected": RoutingDecision.DIRECT_TO_AGENT
        },
        {
            "name": "Explicit Agent Selection",
            "request": AgentChatRequest(
                agent_id="content",
                message="I want to talk directly to the content agent about writing",
                context={}
            ),
            "expected": RoutingDecision.DIRECT_TO_AGENT
        },
        
        # Multi-agent coordination scenarios
        {
            "name": "Multi-Agent Coordination",
            "request": AgentChatRequest(
                agent_id="emma",
                message="Create a comprehensive blog post with SEO optimization",
                context={}
            ),
            "expected": RoutingDecision.MULTI_AGENT_COORDINATION
        },
        {
            "name": "Strategy Request",
            "request": AgentChatRequest(
                agent_id="emma",
                message="Develop a complete content strategy for our website",
                context={}
            ),
            "expected": RoutingDecision.ORCHESTRATE_WITH_EMMA
        },
        
        # Emma orchestration scenarios
        {
            "name": "General Question",
            "request": AgentChatRequest(
                agent_id="emma",
                message="Hello, how can you help me today?",
                context={}
            ),
            "expected": RoutingDecision.ORCHESTRATE_WITH_EMMA
        },
        {
            "name": "Complex Request",
            "request": AgentChatRequest(
                agent_id="emma",
                message="I need help with my marketing campaign",
                context={}
            ),
            "expected": RoutingDecision.ORCHESTRATE_WITH_EMMA
        }
    ]
    
    print("🚀 Emma Studio Smart Routing System Test")
    print("=" * 60)
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print(f"Request: {test_case['request'].message}")
        print(f"Agent ID: {test_case['request'].agent_id}")
        
        # Analyze routing
        result = smart_routing.analyze_request(test_case['request'])
        
        print(f"🎯 Routing Decision: {result.decision.value}")
        print(f"📍 Target Agent: {result.target_agent_id}")
        print(f"🤝 Target Agents: {result.target_agents}")
        print(f"📊 Confidence: {result.confidence:.2f}")
        print(f"💭 Reasoning: {result.reasoning}")
        print(f"📋 Metadata: {result.metadata}")
        
        # Check if routing matches expectation
        if result.decision == test_case['expected']:
            print("✅ PASS - Routing decision matches expected outcome")
        else:
            print(f"❌ FAIL - Expected {test_case['expected'].value}, got {result.decision.value}")
        
        print("-" * 60)
        print()
    
    print("🎉 Smart Routing Test Complete!")
    print()
    print("Key Improvements Demonstrated:")
    print("✅ Direct agent access bypasses unnecessary Emma orchestration")
    print("✅ Multi-agent coordination is properly identified")
    print("✅ Complex requests are routed to Emma when appropriate")
    print("✅ Clear reasoning and confidence scores for all decisions")
    print("✅ Metadata provides detailed routing information")


def demonstrate_routing_benefits():
    """Demonstrate the benefits of the new routing system."""
    
    print("\n🔄 Before vs After: Routing Architecture Comparison")
    print("=" * 60)
    
    print("\n❌ OLD SYSTEM (Problematic):")
    print("   User → Emma → Agent → Emma → User")
    print("   - ALL requests forced through Emma")
    print("   - Unnecessary delegation analysis for simple requests")
    print("   - Confusing role boundaries")
    print("   - Poor user experience with latency")
    print("   - Emma acts as both agent and orchestrator")
    
    print("\n✅ NEW SYSTEM (Smart Routing):")
    print("   Direct Access: User → API → Specific Agent → User")
    print("   Orchestration: User → API → Emma → Multi-Agent → User")
    print("   - Intelligent routing based on request analysis")
    print("   - Direct access for explicit agent requests")
    print("   - Emma focused on coordination tasks only")
    print("   - Reduced latency and improved user experience")
    print("   - Clear separation of autonomous agents vs orchestrators")
    
    print("\n🎯 Routing Decision Factors:")
    print("   1. Explicit agent selection by user")
    print("   2. Keyword analysis for domain-specific requests")
    print("   3. Multi-agent requirement detection")
    print("   4. Complexity analysis for orchestration needs")
    print("   5. Agent capability matching")


if __name__ == "__main__":
    print("Emma Studio - Smart Routing System Demonstration")
    print("This script shows how the new architecture fixes orchestration problems")
    print()
    
    try:
        test_routing_scenarios()
        demonstrate_routing_benefits()
        
        print("\n🚀 Ready to test with the API!")
        print("Try these endpoints:")
        print("  POST /api/v1/agent/chat - Smart-routed agent communication")
        print("  POST /api/v1/agent/routing-analysis - Analyze routing decisions")
        print("  GET /api/v1/agents - List available agents")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
