#!/usr/bin/env python3
"""
Test script para verificar la integración completa:
Emma AI → Content Generation → Visual Ad Generation
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ad_creator_agent_service import AdCreatorAgentService
from app.services.ad_service import ad_service


async def test_full_integration():
    """Test the complete integration from Emma to visual generation."""
    print("🧪 Probando INTEGRACIÓN COMPLETA: Emma → Visual Generation...")
    
    # Initialize Emma
    emma = AdCreatorAgentService()
    
    # Step 1: Emma generates content for supplements
    print("\n💊 Step 1: Emma genera contenido para suplementos")
    
    # Generate headline
    headline = await emma.generate_specific_field("headline", {
        "platform": "facebook",
        "productDescription": "Proteína whey premium para atletas y personas que van al gym"
    })
    print(f"✅ Headline: '{headline}' ({len(headline)} chars)")
    
    # Generate punchline
    punchline = await emma.generate_specific_field("punchline", {
        "platform": "facebook", 
        "productDescription": "Proteína whey premium para atletas y personas que van al gym"
    })
    print(f"✅ Punchline: '{punchline}' ({len(punchline)} chars)")
    
    # Generate CTA
    cta = await emma.generate_specific_field("cta", {
        "platform": "facebook",
        "productDescription": "Proteína whey premium para atletas y personas que van al gym"
    })
    print(f"✅ CTA: '{cta}' ({len(cta)} chars)")
    
    # Step 2: Build enhanced prompt with Emma's content
    print("\n🎨 Step 2: Construyendo prompt mejorado con contenido de Emma")
    
    base_prompt = "Anuncio profesional de proteína whey premium para atletas y personas que van al gym"
    
    enhanced_prompt = f"""{base_prompt}

TEXTO PARA INCLUIR EN EL ANUNCIO:
Headline: "{headline}"
Punchline: "{punchline}"
CTA: "{cta}"

Asegúrate de incluir estos textos de manera prominente y legible en el diseño del anuncio."""
    
    print(f"📝 Prompt mejorado:")
    print(f"   Base: {base_prompt}")
    print(f"   + Headline: {headline}")
    print(f"   + Punchline: {punchline}")
    print(f"   + CTA: {cta}")
    
    # Step 3: Generate visual ad
    print("\n🖼️ Step 3: Generando anuncio visual con OpenAI")
    
    result = await ad_service.generate_ad(
        prompt=enhanced_prompt,
        size="1024x1024"
    )
    
    if result["success"]:
        print(f"✅ Anuncio generado exitosamente!")
        print(f"   URL: {result['image_url'][:50]}...")
        print(f"   Metadata: {result.get('metadata', {})}")
        
        # Verify that Emma's content is in the enhanced prompt
        enhanced_in_metadata = result.get('metadata', {}).get('enhanced_prompt', '')
        if headline in enhanced_in_metadata:
            print(f"✅ Headline incluido en prompt final")
        if punchline in enhanced_in_metadata:
            print(f"✅ Punchline incluido en prompt final")
        if cta in enhanced_in_metadata:
            print(f"✅ CTA incluido en prompt final")
            
    else:
        print(f"❌ Error generando anuncio: {result.get('error')}")
    
    print("\n🎉 Integración completa probada!")
    print("\n📋 RESUMEN:")
    print(f"   Emma generó: {headline} | {punchline} | {cta}")
    print(f"   Visual: {'✅ Generado' if result['success'] else '❌ Error'}")
    print(f"   Integración: {'✅ Completa' if result['success'] else '❌ Fallida'}")


if __name__ == "__main__":
    asyncio.run(test_full_integration())
