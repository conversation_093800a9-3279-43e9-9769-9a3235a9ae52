"""
Pydantic schemas for influencer generation and management.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any


class TestimonialGenerationRequest(BaseModel):
    """Request schema for generating testimonials."""
    product_name: str = Field(..., description="Name of the product being endorsed")
    testimonial_text: str = Field(..., description="The testimonial text content")
    influencer_style: str = Field(default="lifestyle", description="Style of influencer (lifestyle, fitness, beauty, tech, fashion, travel)")
    resolution: Optional[str] = Field(default=None, description="Ideogram resolution (e.g., 1024x1024)")
    aspect_ratio: Optional[str] = Field(default=None, description="Ideogram aspect ratio (e.g., 1x1)")
    rendering_speed: str = Field(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY")
    magic_prompt: str = Field(default="AUTO", description="Magic prompt enhancement: AUTO/ON/OFF")
    negative_prompt: Optional[str] = Field(default=None, description="What to exclude from image")
    num_images: int = Field(default=1, description="Number of images to generate (1-8)")
    style_type: str = Field(default="REALISTIC", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")


class ProductPlacementRequest(BaseModel):
    """Request schema for generating product placement content."""
    product_name: str = Field(..., description="Name of the product to place")
    placement_context: str = Field(..., description="Context for the placement (morning routine, workout, etc.)")
    influencer_style: str = Field(default="lifestyle", description="Style of influencer (lifestyle, fitness, beauty, tech, fashion, travel)")
    resolution: Optional[str] = Field(default=None, description="Ideogram resolution (e.g., 1024x1024)")
    aspect_ratio: Optional[str] = Field(default=None, description="Ideogram aspect ratio (e.g., 1x1)")
    rendering_speed: str = Field(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY")
    magic_prompt: str = Field(default="AUTO", description="Magic prompt enhancement: AUTO/ON/OFF")
    negative_prompt: Optional[str] = Field(default=None, description="What to exclude from image")
    num_images: int = Field(default=1, description="Number of images to generate (1-8)")
    style_type: str = Field(default="REALISTIC", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")


class InfluencerGenerationResponse(BaseModel):
    """Response schema for influencer generation."""
    success: bool
    image_url: Optional[str] = None
    revised_prompt: Optional[str] = None
    response_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class MultiTurnEditRequest(BaseModel):
    """Request schema for multi-turn editing."""
    previous_response_id: str = Field(..., description="ID of the previous response to build upon")
    edit_prompt: str = Field(..., description="Description of the changes to make")


class MultiTurnEditResponse(BaseModel):
    """Response schema for multi-turn editing."""
    success: bool
    image_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class StreamGenerationResponse(BaseModel):
    """Response schema for streaming generation."""
    success: bool
    partial_image: Optional[bool] = None
    image_url: Optional[str] = None
    index: Optional[int] = None
    progress: Optional[int] = None
    error: Optional[str] = None


class ReferenceEditRequest(BaseModel):
    """Request schema for editing with reference images."""
    prompt: str = Field(..., description="Description of the influencer content to create")
    # reference_images will be handled as UploadFile in the endpoint


class ReferenceEditResponse(BaseModel):
    """Response schema for reference editing."""
    success: bool
    image_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class MaskEditRequest(BaseModel):
    """Request schema for mask-based editing."""
    prompt: str = Field(..., description="Description of what to put in the masked area")
    # image and mask will be handled as UploadFile in the endpoint


class MaskEditResponse(BaseModel):
    """Response schema for mask editing."""
    success: bool
    image_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


# Frontend response schemas for consistency with other tools
class FrontendInfluencerResponse(BaseModel):
    """Frontend response schema for influencer operations."""
    success: bool
    images: Optional[List[str]] = None
    image_url: Optional[str] = None
    revised_prompt: Optional[str] = None
    response_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    @classmethod
    def from_service_response(cls, service_response: Dict[str, Any]) -> "FrontendInfluencerResponse":
        """Convert service response to frontend response format."""
        return cls(
            success=service_response.get("success", False),
            images=service_response.get("images", []),
            image_url=service_response.get("image_url"),
            revised_prompt=service_response.get("revised_prompt"),
            response_id=service_response.get("response_id"),
            metadata=service_response.get("metadata"),
            error=service_response.get("error")
        )


class FrontendStreamResponse(BaseModel):
    """Frontend response schema for streaming operations."""
    success: bool
    partial_image: Optional[bool] = None
    image_url: Optional[str] = None
    index: Optional[int] = None
    progress: Optional[int] = None
    error: Optional[str] = None

    @classmethod
    def from_service_response(cls, service_response: Dict[str, Any]) -> "FrontendStreamResponse":
        """Convert service response to frontend stream response format."""
        return cls(
            success=service_response.get("success", False),
            partial_image=service_response.get("partial_image"),
            image_url=service_response.get("image_url"),
            index=service_response.get("index"),
            progress=service_response.get("progress"),
            error=service_response.get("error")
        )


# User-specific influencer storage schemas
class InfluencerProfile(BaseModel):
    """Schema for storing influencer profiles."""
    id: str
    user_id: str
    name: str
    style: str
    description: str
    image_url: str
    metadata: Optional[Dict[str, Any]] = None
    created_at: str
    updated_at: str


class InfluencerGalleryResponse(BaseModel):
    """Response schema for influencer gallery."""
    success: bool
    influencers: List[InfluencerProfile] = []
    total_count: int = 0
    error: Optional[str] = None


class SaveInfluencerRequest(BaseModel):
    """Request schema for saving an influencer to user's gallery."""
    name: str = Field(..., description="Name for the influencer")
    style: str = Field(..., description="Style category of the influencer")
    description: str = Field(..., description="Description of the influencer")
    image_url: str = Field(..., description="URL of the generated image")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class SaveInfluencerResponse(BaseModel):
    """Response schema for saving an influencer."""
    success: bool
    influencer_id: Optional[str] = None
    error: Optional[str] = None
