"""
Real SEO Analysis Engine for Emma Studio
Implements proven SEO algorithms for actual ranking improvements
"""

import re
import math
import logging
from typing import Dict, <PERSON>, Tuple, Optional
from collections import Counter
from dataclasses import dataclass
from bs4 import BeautifulSoup
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from textstat import flesch_kincaid_grade, flesch_reading_ease, smog_index

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

logger = logging.getLogger(__name__)

@dataclass
class SEOAnalysisResult:
    """Real SEO analysis results with proven metrics"""
    overall_score: float
    keyword_density: Dict[str, float]
    tf_idf_scores: Dict[str, float]
    readability_score: float
    readability_grade: float
    content_structure_score: float
    heading_analysis: Dict[str, int]
    word_count: int
    sentence_count: int
    paragraph_count: int
    suggestions: List[str]
    issues: List[str]

class RealSEOAnalysisEngine:
    """
    Real SEO Analysis Engine using proven algorithms
    Implements actual SEO metrics that correlate with ranking improvements
    """
    
    def __init__(self):
        self.stop_words = set(stopwords.words('spanish') + stopwords.words('english'))
        self.optimal_keyword_density = (1.0, 3.0)  # 1-3% proven optimal range
        self.optimal_readability = (60, 80)  # Flesch Reading Ease optimal range
        
    def analyze_content(self, content: str, target_keywords: List[str] = None) -> SEOAnalysisResult:
        """
        Perform comprehensive SEO analysis using proven algorithms
        
        Args:
            content: HTML or plain text content to analyze
            target_keywords: List of target keywords to analyze
            
        Returns:
            SEOAnalysisResult with actual SEO metrics
        """
        try:
            # Clean and prepare content
            clean_text = self._clean_html(content)
            
            # Basic content metrics
            word_count = self._count_words(clean_text)
            sentence_count = len(sent_tokenize(clean_text))
            paragraph_count = self._count_paragraphs(content)
            
            # Keyword analysis
            keyword_density = self._calculate_keyword_density(clean_text, target_keywords or [])
            tf_idf_scores = self._calculate_tf_idf(clean_text, target_keywords or [])
            
            # Readability analysis
            readability_score = self._calculate_readability(clean_text)
            readability_grade = self._calculate_readability_grade(clean_text)
            
            # Content structure analysis
            heading_analysis = self._analyze_headings(content)
            structure_score = self._calculate_structure_score(content, word_count)
            
            # Generate overall score
            overall_score = self._calculate_overall_score(
                keyword_density, readability_score, structure_score, word_count
            )
            
            # Generate actionable suggestions
            suggestions = self._generate_suggestions(
                keyword_density, readability_score, structure_score, 
                word_count, heading_analysis
            )
            
            # Identify issues
            issues = self._identify_issues(
                keyword_density, readability_score, word_count, heading_analysis
            )
            
            return SEOAnalysisResult(
                overall_score=overall_score,
                keyword_density=keyword_density,
                tf_idf_scores=tf_idf_scores,
                readability_score=readability_score,
                readability_grade=readability_grade,
                content_structure_score=structure_score,
                heading_analysis=heading_analysis,
                word_count=word_count,
                sentence_count=sentence_count,
                paragraph_count=paragraph_count,
                suggestions=suggestions,
                issues=issues
            )
            
        except Exception as e:
            logger.error(f"SEO analysis failed: {str(e)}")
            raise
    
    def _clean_html(self, content: str) -> str:
        """Remove HTML tags and clean text for analysis"""
        soup = BeautifulSoup(content, 'html.parser')
        return soup.get_text().strip()
    
    def _count_words(self, text: str) -> int:
        """Count words excluding stop words"""
        words = word_tokenize(text.lower())
        return len([word for word in words if word.isalpha() and word not in self.stop_words])
    
    def _count_paragraphs(self, content: str) -> int:
        """Count paragraphs in content"""
        soup = BeautifulSoup(content, 'html.parser')
        paragraphs = soup.find_all('p')
        if paragraphs:
            return len(paragraphs)
        # Fallback for plain text
        return len([p for p in content.split('\n\n') if p.strip()])
    
    def _calculate_keyword_density(self, text: str, keywords: List[str]) -> Dict[str, float]:
        """
        Calculate keyword density using proven SEO formula
        Optimal range: 1-3% for primary keywords
        """
        if not keywords:
            return {}
            
        words = word_tokenize(text.lower())
        total_words = len(words)
        
        if total_words == 0:
            return {}
        
        density = {}
        for keyword in keywords:
            keyword_lower = keyword.lower()
            # Count exact matches and partial matches
            exact_count = text.lower().count(keyword_lower)
            density[keyword] = (exact_count / total_words) * 100
            
        return density
    
    def _calculate_tf_idf(self, text: str, keywords: List[str]) -> Dict[str, float]:
        """
        Calculate TF-IDF scores for keywords
        Higher scores indicate better keyword relevance
        """
        if not keywords:
            return {}
            
        words = word_tokenize(text.lower())
        word_count = Counter(words)
        total_words = len(words)
        
        tf_idf_scores = {}
        for keyword in keywords:
            keyword_lower = keyword.lower()
            
            # Term Frequency (TF)
            tf = word_count.get(keyword_lower, 0) / total_words
            
            # Simplified IDF (in real implementation, use corpus)
            # For now, use inverse of word frequency in general language
            idf = math.log(1000 / (word_count.get(keyword_lower, 0) + 1))
            
            tf_idf_scores[keyword] = tf * idf
            
        return tf_idf_scores
    
    def _calculate_readability(self, text: str) -> float:
        """
        Calculate Flesch Reading Ease score
        60-80 is optimal for web content
        """
        try:
            return flesch_reading_ease(text)
        except:
            return 50.0  # Default moderate score
    
    def _calculate_readability_grade(self, text: str) -> float:
        """Calculate Flesch-Kincaid Grade Level"""
        try:
            return flesch_kincaid_grade(text)
        except:
            return 8.0  # Default 8th grade level
    
    def _analyze_headings(self, content: str) -> Dict[str, int]:
        """Analyze heading structure (H1-H6)"""
        soup = BeautifulSoup(content, 'html.parser')
        headings = {}
        
        for i in range(1, 7):
            headings[f'h{i}'] = len(soup.find_all(f'h{i}'))
            
        return headings
    
    def _calculate_structure_score(self, content: str, word_count: int) -> float:
        """
        Calculate content structure score based on SEO best practices
        """
        soup = BeautifulSoup(content, 'html.parser')
        score = 0.0
        
        # H1 tag presence (critical for SEO)
        h1_tags = soup.find_all('h1')
        if len(h1_tags) == 1:
            score += 25  # Perfect
        elif len(h1_tags) == 0:
            score += 0   # Missing H1
        else:
            score += 10  # Multiple H1s (not ideal)
        
        # Heading hierarchy
        h2_tags = len(soup.find_all('h2'))
        h3_tags = len(soup.find_all('h3'))
        
        if h2_tags > 0:
            score += 20
        if h3_tags > 0 and h2_tags > 0:
            score += 15
        
        # Content length
        if word_count >= 300:
            score += 20
        elif word_count >= 150:
            score += 10
        
        # Paragraph structure
        paragraphs = soup.find_all('p')
        if len(paragraphs) > 0:
            avg_paragraph_length = word_count / len(paragraphs)
            if 50 <= avg_paragraph_length <= 150:  # Optimal paragraph length
                score += 20
            elif avg_paragraph_length <= 200:
                score += 10
        
        return min(score, 100.0)
    
    def _calculate_overall_score(self, keyword_density: Dict[str, float], 
                                readability: float, structure: float, 
                                word_count: int) -> float:
        """Calculate overall SEO score using weighted factors"""
        score = 0.0
        
        # Keyword density score (30% weight)
        if keyword_density:
            avg_density = sum(keyword_density.values()) / len(keyword_density)
            if self.optimal_keyword_density[0] <= avg_density <= self.optimal_keyword_density[1]:
                score += 30
            elif avg_density < self.optimal_keyword_density[0]:
                score += 15  # Too low
            else:
                score += 10  # Too high
        else:
            score += 5  # No keywords analyzed
        
        # Readability score (25% weight)
        if self.optimal_readability[0] <= readability <= self.optimal_readability[1]:
            score += 25
        else:
            score += 15
        
        # Structure score (25% weight)
        score += structure * 0.25
        
        # Content length score (20% weight)
        if word_count >= 300:
            score += 20
        elif word_count >= 150:
            score += 15
        elif word_count >= 100:
            score += 10
        else:
            score += 5
        
        return min(score, 100.0)
    
    def _generate_suggestions(self, keyword_density: Dict[str, float], 
                            readability: float, structure: float,
                            word_count: int, headings: Dict[str, int]) -> List[str]:
        """Generate actionable SEO improvement suggestions"""
        suggestions = []
        
        # Keyword density suggestions
        for keyword, density in keyword_density.items():
            if density < self.optimal_keyword_density[0]:
                suggestions.append(f"Aumenta la densidad de '{keyword}' (actual: {density:.1f}%, óptimo: 1-3%)")
            elif density > self.optimal_keyword_density[1]:
                suggestions.append(f"Reduce la densidad de '{keyword}' (actual: {density:.1f}%, óptimo: 1-3%)")
        
        # Readability suggestions
        if readability < self.optimal_readability[0]:
            suggestions.append("Mejora la legibilidad usando oraciones más cortas y vocabulario simple")
        elif readability > self.optimal_readability[1]:
            suggestions.append("El contenido puede ser demasiado simple, considera añadir más profundidad")
        
        # Structure suggestions
        if headings.get('h1', 0) == 0:
            suggestions.append("Añade un título H1 principal")
        elif headings.get('h1', 0) > 1:
            suggestions.append("Usa solo un título H1 por página")
        
        if headings.get('h2', 0) == 0 and word_count > 300:
            suggestions.append("Añade subtítulos H2 para mejorar la estructura")
        
        # Content length suggestions
        if word_count < 300:
            suggestions.append(f"Aumenta la longitud del contenido (actual: {word_count}, mínimo: 300 palabras)")
        
        return suggestions
    
    def _identify_issues(self, keyword_density: Dict[str, float], 
                        readability: float, word_count: int,
                        headings: Dict[str, int]) -> List[str]:
        """Identify critical SEO issues that hurt rankings"""
        issues = []
        
        # Critical issues
        if headings.get('h1', 0) == 0:
            issues.append("CRÍTICO: Falta título H1")
        
        if word_count < 150:
            issues.append("CRÍTICO: Contenido demasiado corto para posicionar")
        
        # High-impact issues
        for keyword, density in keyword_density.items():
            if density > 5.0:
                issues.append(f"ALTO IMPACTO: Keyword stuffing detectado en '{keyword}' ({density:.1f}%)")
        
        if readability < 30:
            issues.append("ALTO IMPACTO: Contenido muy difícil de leer")
        
        return issues

# Singleton instance
seo_analysis_engine = RealSEOAnalysisEngine()
