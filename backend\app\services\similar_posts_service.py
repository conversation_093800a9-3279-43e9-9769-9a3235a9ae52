"""
Similar Posts Service - Dedicated service for "Más como este" functionality.

This service is specifically designed to:
1. Analyze a reference post to understand its characteristics
2. Generate posts that maintain the same style, tone, and visual consistency
3. Use Ideogram parameters (seed, style_type) for visual similarity
4. Maintain content type consistency (educational, motivational, etc.)
"""

import logging
from typing import Dict, Any, List
import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)


class SimilarPostsService:
    """Dedicated service for generating posts similar to a reference post."""
    
    def __init__(self):
        """Initialize the Similar Posts Service."""
        self.model = None
        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("✅ Similar Posts Service initialized with Gemini")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini for Similar Posts Service: {e}")
        else:
            logger.warning("⚠️ Similar Posts Service initialized without <PERSON> (no API key)")
    
    async def generate_similar_posts(self, reference_post: Dict[str, Any], user_context: Dict[str, Any], count: int = 3) -> List[Dict[str, Any]]:
        """
        Generate posts similar to a reference post.
        
        Args:
            reference_post: The specific post selected by the user as reference
            user_context: User and brand context information
            count: Number of similar posts to generate
            
        Returns:
            List of similar posts with consistent style and content
        """
        logger.info(f"🎯 Starting similar posts generation: {count} posts based on specific reference")
        
        try:
            # Step 1: Analyze the reference post
            reference_analysis = await self._analyze_reference_post(reference_post, user_context)
            logger.info(f"📊 Reference analysis: {reference_analysis.get('content_type', 'unknown')} style")
            
            # Step 2: Generate similar posts
            similar_posts = []
            for i in range(count):
                try:
                    similar_post = await self._generate_single_similar_post(
                        reference_post, reference_analysis, user_context, i
                    )
                    similar_posts.append(similar_post)
                    logger.info(f"✅ Generated similar post {i+1}/{count}")
                except Exception as e:
                    logger.error(f"❌ Error generating similar post {i+1}: {e}")
                    continue
            
            logger.info(f"🎉 Similar posts generation completed: {len(similar_posts)}/{count} posts generated")
            return similar_posts
            
        except Exception as e:
            logger.error(f"❌ Critical error in similar posts generation: {e}")
            return []
    
    async def _analyze_reference_post(self, reference_post: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the reference post to understand its characteristics."""
        try:
            if not self.model:
                return self._get_simple_reference_analysis(reference_post)
            
            reference_content = reference_post.get("content", "")
            business_name = user_context.get("businessName", "Business")
            industry = user_context.get("industry", "general")
            
            analysis_prompt = f"""
Analiza este post de {business_name} en {industry} para generar posts similares:

POST DE REFERENCIA:
"{reference_content}"

Identifica:
1. TIPO: ¿Es educativo, motivacional, promocional, casual, profesional?
2. TONO: ¿Cómo se comunica? (formal, casual, inspirador, informativo)
3. ESTRUCTURA: ¿Cómo está organizado el contenido?
4. TEMA: ¿De qué habla específicamente?
5. ESTILO: ¿Qué elementos lo hacen único?

Responde en JSON:
{{
  "content_type": "educativo/motivacional/promocional/etc",
  "tone": "descripción del tono",
  "structure": "descripción de la estructura",
  "main_topic": "tema específico",
  "style_elements": "elementos de estilo únicos"
}}
"""
            
            response = self.model.generate_content(analysis_prompt)
            
            if response and response.text:
                try:
                    import json
                    analysis = json.loads(response.text.strip())
                    logger.info(f"✅ Reference post analyzed: {analysis.get('content_type', 'unknown')} style")
                    return analysis
                except json.JSONDecodeError:
                    logger.warning("Failed to parse reference analysis JSON")
                    return self._get_simple_reference_analysis(reference_post)
            else:
                return self._get_simple_reference_analysis(reference_post)
                
        except Exception as e:
            logger.error(f"Error analyzing reference post: {e}")
            return self._get_simple_reference_analysis(reference_post)
    
    def _get_simple_reference_analysis(self, reference_post: Dict[str, Any]) -> Dict[str, Any]:
        """Simple fallback analysis of reference post."""
        content = reference_post.get("content", "").lower()
        
        # Determine content type based on content
        if any(word in content for word in ["aprende", "sabías", "dato", "tip", "cómo"]):
            content_type = "educativo"
        elif any(word in content for word in ["historia", "transformación", "logré", "cambió", "inspirar"]):
            content_type = "motivacional"
        elif any(word in content for word in ["compra", "descuento", "oferta", "precio"]):
            content_type = "promocional"
        else:
            content_type = "general"
        
        return {
            "content_type": content_type,
            "tone": "similar al original",
            "structure": "mantener estructura original",
            "main_topic": "tema relacionado",
            "style_elements": "estilo consistente"
        }
    
    async def _generate_single_similar_post(self, reference_post: Dict[str, Any], analysis: Dict[str, Any], user_context: Dict[str, Any], variation_index: int) -> Dict[str, Any]:
        """Generate a single post similar to the reference."""
        try:
            # Extract key information
            business_name = user_context.get("businessName", "Business")
            industry = user_context.get("industry", "general")
            content_type = analysis.get("content_type", "general")
            tone = analysis.get("tone", "similar")
            main_topic = analysis.get("main_topic", "tema relacionado")
            
            # Generate similar content
            similar_content = await self._generate_similar_content(
                business_name, industry, content_type, tone, analysis, variation_index
            )
            
            # Generate similar visual hook
            similar_hook = await self._generate_similar_hook(
                content_type, main_topic, business_name, variation_index
            )
            
            # Generate similar image using reference metadata
            similar_image_url = await self._generate_similar_image(
                reference_post, similar_hook, variation_index
            )
            
            return {
                "visual_hook": similar_hook,
                "content": similar_content,
                "image_url": similar_image_url,
                "content_type": content_type,
                "similarity_analysis": analysis,
                "variation_index": variation_index,
                "reference_used": True
            }
            
        except Exception as e:
            logger.error(f"Error generating single similar post: {e}")
            # Simple fallback
            return {
                "visual_hook": f"Más sobre {main_topic}",
                "content": f"Descubre más contenido increíble de {business_name}.\n\n¿Te gustó este estilo? 💭\n\n#Contenido #Marketing",
                "image_url": None,
                "content_type": "general",
                "similarity_analysis": analysis,
                "variation_index": variation_index,
                "reference_used": False
            }

    async def _generate_similar_content(self, business_name: str, industry: str, content_type: str, tone: str, analysis: Dict[str, Any], variation_index: int) -> str:
        """Generate content similar to the reference."""
        try:
            if not self.model:
                return self._get_fallback_similar_content(business_name, industry, content_type)

            prompt = f"""
Crea un post SIMILAR para {business_name} en {industry}.

CARACTERÍSTICAS A MANTENER:
- Tipo: {content_type}
- Tono: {tone}
- Estructura: {analysis.get('structure', 'similar')}
- Tema: {analysis.get('main_topic', 'relacionado')}

INSTRUCCIONES:
- Mantén el MISMO TIPO de contenido ({content_type})
- Usa el MISMO TONO ({tone})
- Habla sobre {industry} de manera {content_type}
- Debe sentirse como parte de la MISMA SERIE de posts
- Máximo 800 caracteres en español
- Incluye hashtags relevantes

Genera el contenido similar:
"""

            response = self.model.generate_content(prompt)

            if response and response.text:
                content = response.text.strip()
                logger.info(f"✅ Generated similar content maintaining reference style")
                return content
            else:
                return self._get_fallback_similar_content(business_name, industry, content_type)

        except Exception as e:
            logger.error(f"Error generating similar content: {e}")
            return self._get_fallback_similar_content(business_name, industry, content_type)

    def _get_fallback_similar_content(self, business_name: str, industry: str, content_type: str) -> str:
        """Fallback content when AI generation fails."""
        if content_type == "educativo":
            return f"¿Sabías esto sobre {industry}?\n\nDescubre más con {business_name}.\n\n#Educativo #{industry.title()}"
        elif content_type == "motivacional":
            return f"Tu historia con {industry} puede cambiar.\n\n{business_name} te acompaña en el camino.\n\n#Motivación #{industry.title()}"
        else:
            return f"Más contenido increíble sobre {industry}.\n\n{business_name} siempre contigo.\n\n#Contenido #{industry.title()}"

    async def _generate_similar_hook(self, content_type: str, main_topic: str, business_name: str, variation_index: int) -> str:
        """Generate a visual hook similar to the reference."""
        try:
            if not self.model:
                return self._get_fallback_similar_hook(content_type, main_topic, variation_index)

            prompt = f"""
Crea un hook visual SIMILAR para contenido {content_type} sobre {main_topic}.

TIPO: {content_type}
TEMA: {main_topic}
VARIACIÓN: #{variation_index + 1}

INSTRUCCIONES:
- Máximo 6 palabras
- Debe ser {content_type}
- Relacionado con {main_topic}
- Diferente pero similar al original
- En español

Genera el hook:
"""

            response = self.model.generate_content(prompt)

            if response and response.text:
                hook = response.text.strip().replace('"', '').replace("'", "")
                # Ensure it's not too long
                words = hook.split()
                if len(words) > 6:
                    hook = ' '.join(words[:6])
                return hook
            else:
                return self._get_fallback_similar_hook(content_type, main_topic, variation_index)

        except Exception as e:
            logger.error(f"Error generating similar hook: {e}")
            return self._get_fallback_similar_hook(content_type, main_topic, variation_index)

    def _get_fallback_similar_hook(self, content_type: str, main_topic: str, variation_index: int) -> str:
        """Fallback hook when AI generation fails."""
        if content_type == "educativo":
            hooks = [
                f"Secretos de {main_topic}",
                f"Aprende sobre {main_topic}",
                f"Datos de {main_topic}",
                f"Tips de {main_topic}"
            ]
        elif content_type == "motivacional":
            hooks = [
                f"Mi historia con {main_topic}",
                f"Transformación con {main_topic}",
                f"Cambié con {main_topic}",
                f"Inspiración de {main_topic}"
            ]
        else:
            hooks = [
                f"Más sobre {main_topic}",
                f"Descubre {main_topic}",
                f"Todo sobre {main_topic}",
                f"Conoce {main_topic}"
            ]

        return hooks[variation_index % len(hooks)]

    async def _generate_similar_image(self, reference_post: Dict[str, Any], similar_hook: str, variation_index: int) -> str:
        """Generate similar image using Ideogram with seed-based similarity (more reliable approach)."""
        try:
            from app.services.ideogram_service import ideogram_service

            # Get reference image URL and metadata
            reference_image_url = reference_post.get("image_url")
            reference_metadata = reference_post.get("metadata", {})

            logger.info(f"🔍 DEBUG - Reference image URL: {reference_image_url}")
            logger.info(f"🔍 DEBUG - Reference metadata: {reference_metadata}")
            logger.info(f"🔍 DEBUG - Full reference post keys: {list(reference_post.keys())}")

            # Create image prompt
            image_prompt = f'Professional social media design with text: "{similar_hook}"'

            # Try seed-based similarity first (more reliable)
            reference_seed = reference_metadata.get("seed")
            reference_style_type = reference_metadata.get("style_type", "DESIGN")

            if reference_seed:
                logger.info(f"🎯 Generating similar image {variation_index + 1} using seed-based approach")
                logger.info(f"🌱 Using reference seed: {reference_seed}")

                # Generate with similar seed (add variation to avoid exact duplicate)
                similar_seed = reference_seed + variation_index

                image_result = await ideogram_service.generate_image_with_seed(
                    prompt=image_prompt,
                    seed=similar_seed,
                    style_type=reference_style_type,
                    size="1024x1024"
                )
            elif reference_image_url and reference_metadata:
                logger.info(f"🎯 Fallback: Trying reference image approach for image {variation_index + 1}")
                logger.info(f"📸 Reference image: {reference_image_url[:100]}...")

                image_result = await ideogram_service.generate_similar_image_with_reference(
                    prompt=image_prompt,
                    reference_image_url=reference_image_url,
                    reference_metadata=reference_metadata,
                    size="1024x1024"
                )
            else:
                logger.warning(f"⚠️ No reference data available, generating regular image {variation_index + 1}")
                image_result = await ideogram_service.generate_image(
                    prompt=image_prompt,
                    dimensions={"width": 1024, "height": 1024}
                )

            if image_result and image_result.get("success") and image_result.get("image_url"):
                logger.info(f"✅ Generated similar image {variation_index + 1}")
                return image_result["image_url"]
            else:
                error_msg = image_result.get('error', 'Unknown error') if image_result else 'No result returned'
                logger.error(f"❌ Failed to generate similar image {variation_index + 1}: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"Error generating similar image: {e}")
            return None


# Global service instance
similar_posts_service = SimilarPostsService()
