#!/usr/bin/env python3
"""
Test script para verificar la integración con Ideogram.ai
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ideogram_service import ideogram_service
from app.services.ad_service import ad_service


async def test_ideogram_integration():
    """Test Ideogram.ai integration for ad generation."""
    print("🧪 Probando integración con Ideogram.ai...")
    
    # Test 1: Check API key configuration
    print("\n🔑 Test 1: Verificando configuración de API")
    if ideogram_service.api_key:
        print(f"✅ Ideogram API key configurada: {ideogram_service.api_key[:10]}...")
    else:
        print("❌ Ideogram API key NO configurada")
        print("💡 Agrega IDEOGRAM_API_KEY a tu archivo .env")
        return
    
    # Test 2: Direct Ideogram service test
    print("\n🎨 Test 2: Generación directa con Ideogram.ai")
    
    test_prompt = """Anuncio profesional de proteína whey premium para atletas y personas que van al gym

TEXTO PARA INCLUIR EN EL ANUNCIO:
Headline: "Máximos Resultados, Rápido"
Punchline: "Domina tu fuerza interior"
CTA: "Resultados Rápidos"

Asegúrate de incluir estos textos de manera prominente y legible en el diseño del anuncio."""
    
    try:
        result = await ideogram_service.generate_ad(test_prompt, "1024x1024")
        
        if result["success"]:
            print(f"✅ Ideogram generación exitosa!")
            print(f"   URL: {result['image_url'][:50]}...")
            print(f"   Modelo: {result['metadata']['model']}")
            print(f"   Tamaño: {result['metadata']['size']}")
            print(f"   Seed: {result['metadata'].get('seed', 'N/A')}")
        else:
            print(f"❌ Error en Ideogram: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Excepción en Ideogram: {e}")
    
    # Test 3: AdService with Ideogram fallback
    print("\n🔄 Test 3: AdService con Ideogram como principal")
    
    try:
        result = await ad_service.generate_ad(test_prompt, "1024x1024")
        
        if result["success"]:
            print(f"✅ AdService generación exitosa!")
            print(f"   URL: {result['image_url'][:50]}...")
            print(f"   Modelo usado: {result['metadata'].get('model', 'unknown')}")
            
            # Check which service was used
            if 'ideogram' in result['metadata'].get('model', '').lower():
                print(f"   🎯 Usó Ideogram.ai (principal)")
            elif 'gpt-image' in result['metadata'].get('model', '').lower():
                print(f"   🔄 Usó OpenAI (fallback)")
            else:
                print(f"   ❓ Servicio desconocido")
                
        else:
            print(f"❌ Error en AdService: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Excepción en AdService: {e}")
    
    # Test 4: Cost comparison
    print("\n💰 Test 4: Comparación de costos")
    print("📊 Costos estimados por imagen:")
    print("   💸 GPT-image-1: $0.040 USD")
    print("   💰 Ideogram.ai: $0.008 USD")
    print("   📈 Ahorro: 80% menos costo!")
    print("   🎯 Para 100 imágenes:")
    print("      - GPT-image-1: $4.00 USD")
    print("      - Ideogram.ai: $0.80 USD")
    print("      - Ahorro total: $3.20 USD")
    
    print("\n🎉 Pruebas de Ideogram.ai completadas!")


if __name__ == "__main__":
    asyncio.run(test_ideogram_integration())
