"""
API endpoints for influencer generation and management using Ideogram AI.
"""

import logging
import httpx
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File, Query
from fastapi.responses import StreamingResponse, Response
from typing import Optional, List
# import json  # Not needed for current implementation

from app.core.auth import verify_api_key
from app.services.influencer_service import influencer_service
from app.schemas.influencer import (
    # TestimonialGenerationRequest,  # Not used in current implementation
    FrontendInfluencerResponse,
    FrontendStreamResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/generate-testimonial",
    response_model=FrontendInfluencerResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_testimonial(
    product_name: str = Form(..., description="Name of the product being endorsed"),
    testimonial_text: str = Form(..., description="The testimonial text content"),
    influencer_style: str = Form(default="lifestyle", description="Style of influencer"),
    resolution: Optional[str] = Form(default=None, description="Ideogram resolution (e.g., 1024x1024)"),
    aspect_ratio: Optional[str] = Form(default=None, description="Ideogram aspect ratio (e.g., 1x1)"),
    rendering_speed: str = Form(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY"),
    magic_prompt: str = Form(default="AUTO", description="Magic prompt enhancement: AUTO/ON/OFF"),
    negative_prompt: Optional[str] = Form(default=None, description="What to exclude from image"),
    num_images: int = Form(default=1, description="Number of images to generate (1-8)"),
    style_type: str = Form(default="REALISTIC", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")
) -> FrontendInfluencerResponse:
    """Generate a testimonial with a virtual influencer using Ideogram AI."""

    try:
        logger.info(f"🎭 Generating testimonial for {product_name} with {influencer_style} influencer...")

        # Call the service with all parameters
        service_response = await influencer_service.generate_testimonial(
            product_name=product_name,
            testimonial_text=testimonial_text,
            influencer_style=influencer_style,
            resolution=resolution,
            aspect_ratio=aspect_ratio,
            rendering_speed=rendering_speed,
            magic_prompt=magic_prompt,
            negative_prompt=negative_prompt,
            num_images=num_images,
            style_type=style_type
        )

        # Convert to frontend response
        return FrontendInfluencerResponse.from_service_response(service_response)

    except Exception as e:
        logger.error(f"Error in generate_testimonial endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during testimonial generation: {e}"
        )


@router.post(
    "/generate-product-placement",
    response_model=FrontendInfluencerResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_product_placement(
    product_name: str = Form(..., description="Name of the product to place"),
    placement_context: str = Form(..., description="Context for the placement"),
    influencer_style: str = Form(default="lifestyle", description="Style of influencer"),
    resolution: Optional[str] = Form(default=None, description="Ideogram resolution (e.g., 1024x1024)"),
    aspect_ratio: Optional[str] = Form(default=None, description="Ideogram aspect ratio (e.g., 1x1)"),
    rendering_speed: str = Form(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY"),
    magic_prompt: str = Form(default="AUTO", description="Magic prompt enhancement: AUTO/ON/OFF"),
    negative_prompt: Optional[str] = Form(default=None, description="What to exclude from image"),
    num_images: int = Form(default=1, description="Number of images to generate (1-8)"),
    style_type: str = Form(default="REALISTIC", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")
) -> FrontendInfluencerResponse:
    """Generate a product placement with a virtual influencer using Ideogram AI."""

    try:
        logger.info(f"🛍️ Generating product placement for {product_name} in {placement_context}...")

        # Call the service with all parameters
        service_response = await influencer_service.generate_product_placement(
            product_name=product_name,
            placement_context=placement_context,
            influencer_style=influencer_style,
            resolution=resolution,
            aspect_ratio=aspect_ratio,
            rendering_speed=rendering_speed,
            magic_prompt=magic_prompt,
            negative_prompt=negative_prompt,
            num_images=num_images,
            style_type=style_type
        )

        # Convert to frontend response
        return FrontendInfluencerResponse.from_service_response(service_response)

    except Exception as e:
        logger.error(f"Error in generate_product_placement endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during product placement generation: {e}"
        )


@router.post(
    "/multi-turn-edit",
    response_model=FrontendInfluencerResponse,
    dependencies=[Depends(verify_api_key)],
)
async def multi_turn_edit(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendInfluencerResponse:
    """Edit an existing influencer image using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing influencer: {edit_prompt[:100]}...")
        
        # Since Ideogram doesn't support multi-turn editing, we generate a new image
        # with the edit prompt as the main prompt
        service_response = await influencer_service.generate_testimonial(
            product_name="product",  # Generic placeholder
            testimonial_text=edit_prompt,
            influencer_style="lifestyle",
            rendering_speed="DEFAULT",
            magic_prompt="ON"
        )
        
        if service_response["success"]:
            # Update metadata to indicate this was an edit
            if "metadata" in service_response:
                service_response["metadata"]["type"] = "multi_turn_edit"
                service_response["metadata"]["edit_prompt"] = edit_prompt
                service_response["metadata"]["previous_response_id"] = previous_response_id
        
        # Convert to frontend response
        return FrontendInfluencerResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn editing: {e}"
        )


@router.post(
    "/stream-generate-testimonial",
    response_model=FrontendStreamResponse,
    dependencies=[Depends(verify_api_key)],
)
async def stream_generate_testimonial(
    product_name: str = Form(..., description="Name of the product being endorsed"),
    testimonial_text: str = Form(..., description="The testimonial text content"),
    influencer_style: str = Form(default="lifestyle", description="Style of influencer"),
    resolution: Optional[str] = Form(default=None, description="Ideogram resolution"),
    aspect_ratio: Optional[str] = Form(default=None, description="Ideogram aspect ratio")
) -> StreamingResponse:
    """Stream testimonial generation progress."""
    
    try:
        logger.info(f"🎭 Streaming testimonial generation for {product_name}...")
        
        async def generate():
            try:
                # Call the service
                service_response = await influencer_service.generate_testimonial(
                    product_name=product_name,
                    testimonial_text=testimonial_text,
                    influencer_style=influencer_style,
                    resolution=resolution,
                    aspect_ratio=aspect_ratio,
                    rendering_speed="DEFAULT",
                    magic_prompt="AUTO"
                )
                
                # Convert to frontend stream response
                stream_response = FrontendStreamResponse.from_service_response(service_response)
                yield f"data: {stream_response.model_dump_json()}\n\n"
                
            except Exception as e:
                logger.error(f"Error in stream generation: {e}", exc_info=True)
                error_response = FrontendStreamResponse(
                    success=False,
                    error=f"Stream generation error: {str(e)}"
                )
                yield f"data: {error_response.model_dump_json()}\n\n"
        
        return StreamingResponse(
            generate(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in stream_generate_testimonial endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during streaming generation: {e}"
        )


@router.post(
    "/edit-with-references",
    response_model=FrontendInfluencerResponse,
    dependencies=[Depends(verify_api_key)],
)
async def edit_with_references(
    prompt: str = Form(..., description="Description of the influencer content to create"),
    resolution: Optional[str] = Form(default=None, description="Ideogram resolution"),
    aspect_ratio: Optional[str] = Form(default=None, description="Ideogram aspect ratio"),
    reference_images: List[UploadFile] = File(..., description="Reference images to use")
) -> FrontendInfluencerResponse:
    """Generate influencer content using reference images."""
    
    try:
        logger.info(f"🎭 Generating influencer content with {len(reference_images)} reference images...")
        
        # Call the service
        service_response = await influencer_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            resolution=resolution,
            aspect_ratio=aspect_ratio
        )
        
        # Convert to frontend response
        return FrontendInfluencerResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in edit_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference editing: {e}"
        )


@router.get(
    "/gallery",
    dependencies=[Depends(verify_api_key)],
)
async def get_user_influencers(
    user_id: str = Query(..., description="User ID to get influencers for"),
    limit: int = Query(default=20, description="Number of influencers to return"),
    offset: int = Query(default=0, description="Offset for pagination")
):
    """Get user's created influencers gallery."""
    
    try:
        logger.info(f"📸 Getting influencer gallery for user {user_id}...")
        
        # For now, return mock data since we don't have database integration yet
        mock_influencers = [
            {
                "id": f"inf_{i}",
                "user_id": user_id,
                "name": f"Influencer {i}",
                "style": ["lifestyle", "fitness", "beauty", "tech"][i % 4],
                "description": f"Virtual influencer {i} for brand endorsements",
                "image_url": f"https://example.com/influencer_{i}.jpg",
                "metadata": {"type": "testimonial", "created_by": "user"},
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
            for i in range(1, 9)
        ]
        
        return {
            "success": True,
            "influencers": mock_influencers[offset:offset+limit],
            "total_count": len(mock_influencers)
        }
        
    except Exception as e:
        logger.error(f"Error in get_user_influencers endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error getting user influencers: {e}"
        )


@router.post(
    "/save",
    dependencies=[Depends(verify_api_key)],
)
async def save_influencer(
    name: str = Form(..., description="Name for the influencer"),
    style: str = Form(..., description="Style category of the influencer"),
    description: str = Form(..., description="Description of the influencer"),
    image_url: str = Form(..., description="URL of the generated image"),
    user_id: str = Form(..., description="User ID"),
    metadata: Optional[str] = Form(default=None, description="Additional metadata as JSON string")
):
    """Save an influencer to user's gallery."""
    
    try:
        logger.info(f"💾 Saving influencer '{name}' for user {user_id}...")
        
        # For now, return success since we don't have database integration yet
        influencer_id = f"inf_{hash(name + user_id) % 10000}"
        
        return {
            "success": True,
            "influencer_id": influencer_id,
            "message": f"Influencer '{name}' saved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error in save_influencer endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error saving influencer: {e}"
        )
