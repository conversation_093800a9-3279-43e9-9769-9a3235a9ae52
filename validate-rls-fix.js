/**
 * Validate RLS Fix for Supabase Storage
 * This script tests that the RLS policies are working correctly
 * Run in browser console on Visual Complexity Analyzer page
 */

console.log('🔐 Validating RLS Fix for Supabase Storage');

class RLSValidationTest {
  constructor() {
    this.results = {};
    this.errors = [];
  }

  async init() {
    try {
      this.supabase = (await import('/src/lib/supabase.ts')).supabase;
      this.designAnalysisService = (await import('/src/services/designAnalysisService.ts')).designAnalysisService;
      console.log('✅ Modules imported successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to import modules:', error);
      this.errors.push(`Module import failed: ${error.message}`);
      return false;
    }
  }

  async testAuthentication() {
    console.log('\n🔐 Testing Authentication...');
    
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        this.errors.push(`Auth error: ${error.message}`);
        console.error('❌ Authentication error:', error);
        return false;
      }

      if (!session) {
        this.errors.push('No active session');
        console.log('❌ No active session - please log in first');
        return false;
      }

      console.log('✅ Authentication successful:', {
        userId: session.user.id,
        email: session.user.email
      });

      this.results.auth = {
        success: true,
        userId: session.user.id,
        email: session.user.email
      };

      return true;
    } catch (error) {
      this.errors.push(`Auth check failed: ${error.message}`);
      console.error('❌ Auth check failed:', error);
      return false;
    }
  }

  async testStoragePermissions() {
    console.log('\n🪣 Testing Storage Permissions...');
    
    if (!this.results.auth?.userId) {
      console.log('❌ No user ID available for testing');
      return false;
    }

    try {
      // Test 1: List files in user's folder (should work with RLS)
      console.log('📋 Testing file listing permissions...');
      const { data: files, error: listError } = await this.supabase.storage
        .from('design-analysis-images')
        .list(this.results.auth.userId, { limit: 5 });

      if (listError) {
        console.error('❌ File listing failed:', listError);
        this.errors.push(`File listing failed: ${listError.message}`);
        return false;
      }

      console.log(`✅ File listing successful - found ${files?.length || 0} files`);
      this.results.fileList = { success: true, count: files?.length || 0 };

      // Test 2: If files exist, test download permissions
      if (files && files.length > 0) {
        console.log('📥 Testing download permissions...');
        const testFile = files[0];
        const filePath = `${this.results.auth.userId}/${testFile.name}`;

        const { data: fileBlob, error: downloadError } = await this.supabase.storage
          .from('design-analysis-images')
          .download(filePath);

        if (downloadError) {
          console.error('❌ Download failed:', downloadError);
          this.errors.push(`Download failed: ${downloadError.message}`);
          this.results.download = { success: false, error: downloadError.message };
        } else {
          console.log('✅ Download successful:', {
            size: fileBlob?.size,
            type: fileBlob?.type
          });
          this.results.download = { 
            success: true, 
            size: fileBlob?.size,
            type: fileBlob?.type
          };
        }

        // Test 3: Test signed URL creation
        console.log('🔐 Testing signed URL creation...');
        const { data: signedData, error: signedError } = await this.supabase.storage
          .from('design-analysis-images')
          .createSignedUrl(filePath, 3600);

        if (signedError) {
          console.error('❌ Signed URL creation failed:', signedError);
          this.errors.push(`Signed URL failed: ${signedError.message}`);
          this.results.signedUrl = { success: false, error: signedError.message };
        } else {
          console.log('✅ Signed URL created successfully');
          this.results.signedUrl = { 
            success: true, 
            url: signedData.signedUrl?.substring(0, 50) + '...'
          };

          // Test if signed URL is accessible
          try {
            const response = await fetch(signedData.signedUrl, { method: 'HEAD' });
            console.log(`📡 Signed URL accessibility: ${response.ok ? '✅ Accessible' : '❌ Not accessible'} (${response.status})`);
            this.results.signedUrl.accessible = response.ok;
            this.results.signedUrl.status = response.status;
          } catch (fetchError) {
            console.error('❌ Signed URL fetch test failed:', fetchError);
            this.results.signedUrl.accessible = false;
          }
        }
      } else {
        console.log('ℹ️ No files found to test download/signed URL permissions');
        this.results.download = { success: true, note: 'No files to test' };
        this.results.signedUrl = { success: true, note: 'No files to test' };
      }

      return true;
    } catch (error) {
      this.errors.push(`Storage permissions test failed: ${error.message}`);
      console.error('❌ Storage permissions test failed:', error);
      return false;
    }
  }

  async testServiceMethods() {
    console.log('\n🛠️ Testing Service Methods...');
    
    if (!this.results.auth?.userId) {
      console.log('❌ No user ID available for testing');
      return false;
    }

    try {
      // Test storage connection
      console.log('🔗 Testing storage connection...');
      const connectionResult = await this.designAnalysisService.testStorageConnection(this.results.auth.userId);
      
      console.log(`Storage connection: ${connectionResult.success ? '✅' : '❌'} ${connectionResult.message}`);
      this.results.storageConnection = connectionResult;

      // Test getImageUrl method if we have files
      if (this.results.fileList?.count > 0) {
        console.log('🖼️ Testing getImageUrl method...');
        
        // Get a sample analysis with file_url
        const { data: analyses, error } = await this.supabase
          .schema('api')
          .from('design_analyses')
          .select('*')
          .eq('user_id', this.results.auth.userId)
          .not('file_url', 'is', null)
          .limit(1);

        if (!error && analyses && analyses.length > 0) {
          const analysis = analyses[0];
          console.log(`Testing getImageUrl with: ${analysis.file_url}`);
          
          const imageUrl = await this.designAnalysisService.getImageUrl(analysis.file_url);
          
          if (imageUrl) {
            console.log('✅ getImageUrl successful:', {
              type: imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL',
              url: imageUrl.substring(0, 50) + '...'
            });
            this.results.getImageUrl = { 
              success: true, 
              type: imageUrl.startsWith('blob:') ? 'object' : 'http',
              url: imageUrl.substring(0, 50) + '...'
            };
          } else {
            console.error('❌ getImageUrl returned null');
            this.errors.push('getImageUrl returned null');
            this.results.getImageUrl = { success: false, error: 'Returned null' };
          }
        } else {
          console.log('ℹ️ No analyses with images found to test getImageUrl');
          this.results.getImageUrl = { success: true, note: 'No analyses to test' };
        }
      }

      return true;
    } catch (error) {
      this.errors.push(`Service methods test failed: ${error.message}`);
      console.error('❌ Service methods test failed:', error);
      return false;
    }
  }

  async runValidation() {
    console.log('🚀 Starting RLS Validation Test...\n');
    
    try {
      const initOk = await this.init();
      if (!initOk) return this.generateReport();

      const authOk = await this.testAuthentication();
      if (!authOk) return this.generateReport();

      await this.testStoragePermissions();
      await this.testServiceMethods();

      return this.generateReport();
    } catch (error) {
      console.error('💥 Validation failed:', error);
      this.errors.push(`Validation failed: ${error.message}`);
      return this.generateReport();
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 RLS VALIDATION REPORT');
    console.log('='.repeat(60));
    
    // Authentication
    console.log(`\n🔐 Authentication: ${this.results.auth?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.auth?.success) {
      console.log(`   User: ${this.results.auth.email}`);
    }
    
    // Storage Permissions
    console.log(`📋 File Listing: ${this.results.fileList?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.fileList?.success) {
      console.log(`   Files found: ${this.results.fileList.count}`);
    }
    
    console.log(`📥 Download: ${this.results.download?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.download?.note) {
      console.log(`   Note: ${this.results.download.note}`);
    }
    
    console.log(`🔐 Signed URL: ${this.results.signedUrl?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.signedUrl?.accessible !== undefined) {
      console.log(`   Accessible: ${this.results.signedUrl.accessible ? '✅' : '❌'}`);
    }
    
    // Service Methods
    console.log(`🔗 Storage Connection: ${this.results.storageConnection?.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`🖼️ Get Image URL: ${this.results.getImageUrl?.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (this.results.getImageUrl?.type) {
      console.log(`   Method: ${this.results.getImageUrl.type}`);
    }
    
    // Overall Result
    const criticalTests = [
      this.results.auth?.success,
      this.results.fileList?.success,
      this.results.storageConnection?.success
    ];
    
    const overallSuccess = criticalTests.every(test => test === true) && this.errors.length === 0;
    
    console.log(`\n🎯 OVERALL RESULT: ${overallSuccess ? '✅ RLS FIX SUCCESSFUL' : '❌ ISSUES REMAIN'}`);
    
    // Errors
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    // Recommendations
    console.log('\n💡 NEXT STEPS:');
    if (overallSuccess) {
      console.log('   🎉 RLS policies are working! Try loading a saved analysis with an image.');
      console.log('   📝 If images still don\'t display, check browser console for additional errors.');
    } else {
      if (!this.results.auth?.success) {
        console.log('   🔐 Please log in to test storage permissions');
      }
      if (!this.results.storageConnection?.success) {
        console.log('   🔧 Check Supabase Storage configuration');
      }
      if (this.errors.length > 0) {
        console.log('   🐛 Review errors above for specific issues');
      }
    }
    
    console.log('='.repeat(60));
    
    return {
      success: overallSuccess,
      results: this.results,
      errors: this.errors
    };
  }
}

// Create global instance
window.rlsTest = new RLSValidationTest();

console.log('🔧 RLS Validation Test loaded');
console.log('📝 Run: rlsTest.runValidation() to test the RLS fix');
