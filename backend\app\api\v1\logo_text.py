"""
Logo Text Generation API endpoints using OpenAI's gpt-image-1 model.
Similar to infographics but specialized for logo with text generation.
"""

import logging
from typing import List
from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from fastapi.responses import JSONResponse

import httpx
import base64
import tempfile
import os
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

class LogoTextService:
    """Service for creating logos with text using OpenAI's gpt-image-1 model."""

    def __init__(self):
        self.api_key = settings.OPENAI_API_KEY
        self.base_url = "https://api.openai.com/v1"

    def encode_image(self, file_content: bytes) -> str:
        """Encode image content to base64."""
        return base64.b64encode(file_content).decode("utf-8")

# Initialize service
logo_text_service = LogoTextService()

@router.post("/generate")
async def generate_logo_text(
    prompt: str = Form(...),
    size: str = Form(default="1024x1024")
):
    """
    Generate a logo with text using OpenAI's gpt-image-1 model.
    """
    try:
        logger.info(f"Generating logo with text: {prompt[:100]}...")
        
        # Validate size parameter
        valid_sizes = ["1024x1024", "1536x1024", "1024x1536"]
        if size not in valid_sizes:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid size. Must be one of: {valid_sizes}"
            )

        # Generate logo with text using OpenAI gpt-image-1
        if not logo_text_service.api_key:
            raise HTTPException(
                status_code=500,
                detail="OpenAI API key not configured"
            )

        payload = {
            "model": "gpt-image-1",
            "prompt": prompt,
            "n": 1,
            "size": size,
            "quality": "auto",
            "output_format": "png",
            "background": "auto"
        }

        headers = {
            "Authorization": f"Bearer {logo_text_service.api_key}",
            "Content-Type": "application/json"
        }

        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                f"{logo_text_service.base_url}/images/generations",
                json=payload,
                headers=headers
            )

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"OpenAI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=500,
                    detail=f"OpenAI error: {error_text}"
                )

            result = response.json()

            if "data" in result and len(result["data"]) > 0:
                image_data = result["data"][0]

                # Get image in different formats
                b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                image_url_direct = image_data.get("url")

                if b64_image:
                    image_url = f"data:image/png;base64,{b64_image}"
                elif image_url_direct:
                    image_url = image_url_direct
                else:
                    raise HTTPException(
                        status_code=500,
                        detail="No image data in response"
                    )

                return JSONResponse(content={
                    "success": True,
                    "image_url": image_url,
                    "revised_prompt": image_data.get("revised_prompt"),
                    "metadata": {
                        "model": "gpt-image-1",
                        "size": size,
                        "type": "basic"
                    }
                })
            else:
                raise HTTPException(
                    status_code=500,
                    detail="No image data in response"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating logo with text: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/edit-with-references")
async def edit_logo_text_with_references(
    prompt: str = Form(...),
    size: str = Form(default="1024x1024"),
    reference_images: List[UploadFile] = File(...)
):
    """
    Generate a logo with text using reference images.
    """
    try:
        logger.info(f"Generating logo with text using references: {prompt[:100]}...")
        
        # Validate inputs
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )

        valid_sizes = ["1024x1024", "1536x1024", "1024x1536"]
        if size not in valid_sizes:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid size. Must be one of: {valid_sizes}"
            )

        # Process reference images
        processed_images = []
        for image in reference_images:
            if not image.content_type or not image.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid file type: {image.content_type}. Only images are allowed."
                )
            
            # Read image content
            image_content = await image.read()
            processed_images.append({
                "filename": image.filename,
                "content": image_content,
                "content_type": image.content_type
            })

        # Generate logo with text using references (similar to infographics)
        if not logo_text_service.api_key:
            raise HTTPException(
                status_code=500,
                detail="OpenAI API key not configured"
            )

        # Step 1: Analyze reference images with GPT-4 Vision
        analysis_content = [
            {
                "type": "text",
                "text": f"Analiza estas imágenes de referencia y describe detalladamente el estilo visual, colores, tipografía, layout, elementos gráficos y diseño que observas. Luego crea un prompt detallado en español para generar un logo profesional con texto con el siguiente tema: '{prompt}'. El prompt debe incorporar los elementos visuales y de estilo que identificaste en las imágenes de referencia. Responde completamente en español."
            }
        ]

        # Add images to content
        for image_data in processed_images:
            base64_image = logo_text_service.encode_image(image_data["content"])
            analysis_content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{base64_image}",
                    "detail": "high"
                }
            })

        # Call GPT-4 Vision to analyze references
        vision_payload = {
            "model": "gpt-4o",
            "messages": [
                {
                    "role": "system",
                    "content": "Eres un experto en diseño de logos y comunicación visual. SIEMPRE respondes en español. Tu tarea es analizar imágenes de referencia y crear prompts detallados en español para generar logos profesionales con texto. Nunca traduzcas al inglés."
                },
                {
                    "role": "user",
                    "content": analysis_content
                }
            ],
            "max_tokens": 1000
        }

        headers = {
            "Authorization": f"Bearer {logo_text_service.api_key}",
            "Content-Type": "application/json"
        }

        async with httpx.AsyncClient(timeout=120.0) as client:
            # Analyze reference images
            vision_response = await client.post(
                f"{logo_text_service.base_url}/chat/completions",
                json=vision_payload,
                headers=headers
            )

            if vision_response.status_code != 200:
                error_text = vision_response.text
                logger.error(f"OpenAI vision analysis error {vision_response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error analyzing references: {error_text}"
                )

            vision_result = vision_response.json()
            enhanced_prompt = vision_result["choices"][0]["message"]["content"]

            logger.info(f"Enhanced prompt created from references: {enhanced_prompt[:200]}...")

            # Step 2: Generate logo with enhanced prompt
            generation_payload = {
                "model": "gpt-image-1",
                "prompt": enhanced_prompt,
                "n": 1,
                "size": size,
                "quality": "auto",
                "output_format": "png",
                "background": "auto"
            }

            generation_response = await client.post(
                f"{logo_text_service.base_url}/images/generations",
                json=generation_payload,
                headers=headers
            )

            if generation_response.status_code != 200:
                error_text = generation_response.text
                logger.error(f"OpenAI generation error {generation_response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error generating image: {error_text}"
                )

            result = generation_response.json()

            if "data" in result and len(result["data"]) > 0:
                image_data = result["data"][0]

                # Get image in different formats
                b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                image_url_direct = image_data.get("url")

                if b64_image:
                    image_url = f"data:image/png;base64,{b64_image}"
                elif image_url_direct:
                    image_url = image_url_direct
                else:
                    raise HTTPException(
                        status_code=500,
                        detail="No image data in response"
                    )

                return JSONResponse(content={
                    "success": True,
                    "image_url": image_url,
                    "revised_prompt": enhanced_prompt,
                    "metadata": {
                        "model": "gpt-image-1",
                        "original_prompt": prompt,
                        "enhanced_prompt": enhanced_prompt,
                        "reference_count": len(reference_images),
                        "type": "reference",
                        "size": size
                    }
                })
            else:
                raise HTTPException(
                    status_code=500,
                    detail="No image data in response"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating logo with text using references: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/edit-with-mask")
async def edit_logo_text_with_mask(
    prompt: str = Form(...),
    image: UploadFile = File(...),
    mask: UploadFile = File(...)
):
    """
    Edit a logo with text using a mask to specify areas to change.
    """
    try:
        logger.info(f"Editing logo with text using mask: {prompt[:100]}...")
        
        # Validate file types
        if not image.content_type or not image.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid image file type: {image.content_type}"
            )
        
        if not mask.content_type or not mask.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid mask file type: {mask.content_type}"
            )

        # Read file contents
        image_content = await image.read()
        mask_content = await mask.read()

        # Edit logo with text using mask (similar to infographics)
        if not logo_text_service.api_key:
            raise HTTPException(
                status_code=500,
                detail="OpenAI API key not configured"
            )

        # Save to temporary files for OpenAI API
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as img_temp:
            img_temp.write(image_content)
            img_temp_path = img_temp.name

        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as mask_temp:
            mask_temp.write(mask_content)
            mask_temp_path = mask_temp.name

        try:
            headers = {
                "Authorization": f"Bearer {logo_text_service.api_key}"
            }

            files = {
                "image": ("image.png", open(img_temp_path, "rb"), "image/png"),
                "mask": ("mask.png", open(mask_temp_path, "rb"), "image/png"),
                "prompt": (None, prompt),
                "model": (None, "gpt-image-1"),
                "n": (None, "1"),
                "output_format": (None, "png")
            }

            logger.info(f"Editing logo with mask: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{logo_text_service.base_url}/images/edits",
                    headers=headers,
                    files=files
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"OpenAI mask edit error {response.status_code}: {error_text}")
                    raise HTTPException(
                        status_code=500,
                        detail=f"OpenAI error: {error_text}"
                    )

                result = response.json()

                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    # Get image in different formats
                    b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                    image_url_direct = image_data.get("url")

                    if b64_image:
                        image_url = f"data:image/png;base64,{b64_image}"
                    elif image_url_direct:
                        image_url = image_url_direct
                    else:
                        raise HTTPException(
                            status_code=500,
                            detail="No image data in response"
                        )

                    return JSONResponse(content={
                        "success": True,
                        "image_url": image_url,
                        "revised_prompt": image_data.get("revised_prompt"),
                        "metadata": {
                            "model": "gpt-image-1",
                            "size": "1024x1024",
                            "type": "mask_edit"
                        }
                    })
                else:
                    raise HTTPException(
                        status_code=500,
                        detail="No image data in response"
                    )

        finally:
            # Clean up temporary files
            try:
                os.unlink(img_temp_path)
                os.unlink(mask_temp_path)
            except Exception as e:
                logger.warning(f"Failed to clean up temp files: {e}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error editing logo with text using mask: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
