#!/usr/bin/env python3
"""
Script de prueba para Emma Visual Workflows
Verifica que todas las funcionalidades del backend estén funcionando correctamente
"""

import asyncio
import httpx
import json
import sys
import os

# Agregar el directorio backend al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

BASE_URL = "http://localhost:8000/api/v1/emma-workflows"

async def test_health_check():
    """Prueba el endpoint de health check"""
    print("🔍 Probando health check...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Health check OK: {result['message']}")
                print(f"   Nodos disponibles: {result['available_nodes']}")
                print(f"   Servicios: {result['services']}")
                return True
            else:
                print(f"❌ Health check falló: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error en health check: {e}")
            return False

async def test_get_nodes():
    """Prueba el endpoint de obtener nodos"""
    print("\n🔍 Probando obtener nodos disponibles...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/nodes")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Nodos obtenidos: {result['total_nodes']} nodos")
                print(f"   Categorías: {list(result['categories'].keys())}")
                return True
            else:
                print(f"❌ Error obteniendo nodos: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error obteniendo nodos: {e}")
            return False

async def test_get_templates():
    """Prueba el endpoint de templates"""
    print("\n🔍 Probando obtener templates...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/templates")
            if response.status_code == 200:
                result = response.json()
                templates = result['templates']
                print(f"✅ Templates obtenidos: {len(templates)} templates")
                print(f"   Templates disponibles: {list(templates.keys())}")
                return True
            else:
                print(f"❌ Error obteniendo templates: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error obteniendo templates: {e}")
            return False

async def test_validate_workflow():
    """Prueba la validación de workflows"""
    print("\n🔍 Probando validación de workflow...")
    
    # Workflow de prueba simple
    test_workflow = {
        "nodes": [
            {
                "id": "text-1",
                "type": "text-input",
                "position": {"x": 100, "y": 100},
                "inputs": {"text": "Un hermoso paisaje"}
            },
            {
                "id": "ideogram-1",
                "type": "ideogram-generator", 
                "position": {"x": 400, "y": 100},
                "inputs": {"model": "ideogram-3.0", "aspect_ratio": "1:1"}
            }
        ],
        "edges": [
            {
                "source": "text-1",
                "target": "ideogram-1",
                "sourceHandle": "text",
                "targetHandle": "prompt"
            }
        ]
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}/validate",
                json={"workflow": test_workflow}
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Validación completada")
                print(f"   Válido: {result['valid']}")
                print(f"   Nodos: {result['node_count']}, Conexiones: {result['edge_count']}")
                if result['errors']:
                    print(f"   Errores: {result['errors']}")
                if result['warnings']:
                    print(f"   Advertencias: {result['warnings']}")
                return True
            else:
                print(f"❌ Error en validación: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error en validación: {e}")
            return False

async def test_save_workflow():
    """Prueba guardar un workflow"""
    print("\n🔍 Probando guardar workflow...")
    
    test_workflow = {
        "nodes": [
            {
                "id": "text-1",
                "type": "text-input",
                "position": {"x": 100, "y": 100},
                "inputs": {"text": "Workflow de prueba"}
            }
        ],
        "edges": []
    }
    
    save_request = {
        "name": "Workflow de Prueba",
        "description": "Un workflow simple para testing",
        "workflow": test_workflow,
        "tags": ["test", "demo"],
        "category": "testing"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}/save",
                json=save_request
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Workflow guardado: {result['workflow_id']}")
                return result['workflow_id']
            else:
                print(f"❌ Error guardando workflow: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error guardando workflow: {e}")
            return None

async def test_list_workflows():
    """Prueba listar workflows"""
    print("\n🔍 Probando listar workflows...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/list")
            if response.status_code == 200:
                result = response.json()
                workflows = result['workflows']
                print(f"✅ Workflows listados: {len(workflows)} workflows")
                for wf in workflows:
                    print(f"   - {wf['name']} ({wf['id']})")
                return True
            else:
                print(f"❌ Error listando workflows: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error listando workflows: {e}")
            return False

async def test_load_workflow(workflow_id):
    """Prueba cargar un workflow específico"""
    if not workflow_id:
        print("\n⏭️ Saltando test de carga (no hay workflow_id)")
        return True
        
    print(f"\n🔍 Probando cargar workflow {workflow_id}...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/load/{workflow_id}")
            if response.status_code == 200:
                result = response.json()
                workflow = result['workflow']
                print(f"✅ Workflow cargado: {workflow['name']}")
                return True
            else:
                print(f"❌ Error cargando workflow: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error cargando workflow: {e}")
            return False

async def main():
    """Ejecuta todas las pruebas"""
    print("🚀 Iniciando pruebas de Emma Visual Workflows Backend\n")
    
    tests = [
        test_health_check(),
        test_get_nodes(),
        test_get_templates(),
        test_validate_workflow(),
    ]
    
    # Ejecutar pruebas básicas
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    # Pruebas de persistencia
    workflow_id = await test_save_workflow()
    await test_list_workflows()
    await test_load_workflow(workflow_id)
    
    # Resumen
    passed = sum(1 for r in results if r is True)
    total = len(results) + 3  # +3 por las pruebas de persistencia
    
    print(f"\n📊 Resumen de pruebas:")
    print(f"   ✅ Pasaron: {passed + 3}/{total}")
    print(f"   ❌ Fallaron: {total - passed - 3}/{total}")
    
    if passed + 3 == total:
        print("\n🎉 ¡Todas las pruebas pasaron! El backend está funcionando correctamente.")
    else:
        print("\n⚠️ Algunas pruebas fallaron. Revisa los logs arriba.")

if __name__ == "__main__":
    asyncio.run(main())
