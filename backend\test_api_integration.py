"""
Test API Integration
A script to test the API endpoints with the new agent system
"""

import os
import asyncio
import logging
import json
import httpx
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API base URL
API_BASE_URL = "http://localhost:8000"


async def test_crew_run_endpoint():
    """Test the /api/v1/crew/run endpoint."""
    logger.info("Testing /api/v1/crew/run endpoint")
    
    # Create request payload
    payload = {
        "crew_id": "test-crew",
        "prompt": "Create a marketing campaign for a new smartphone",
        "inputs": {
            "product_name": "SuperPhone X",
            "target_audience": "tech enthusiasts",
            "key_features": ["AI capabilities", "long battery life", "high-quality camera"]
        }
    }
    
    # Send request
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{API_BASE_URL}/api/v1/crew/run",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # Check response
            if response.status_code == 200:
                logger.info("Request successful!")
                result = response.json()
                logger.info(f"Response: {json.dumps(result, indent=2)}")
                return True
            else:
                logger.error(f"Request failed with status code {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error sending request: {e}")
            return False


async def test_agent_chat_endpoint():
    """Test the /api/v1/crew/agent/chat endpoint."""
    logger.info("Testing /api/v1/crew/agent/chat endpoint")
    
    # Create request payload
    payload = {
        "agent_id": "emma",
        "message": "Create a social media post about our new smartphone",
        "context": {
            "product_name": "SuperPhone X",
            "platform": "Instagram"
        }
    }
    
    # Send request
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{API_BASE_URL}/api/v1/crew/agent/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # Check response
            if response.status_code == 200:
                logger.info("Request successful!")
                result = response.json()
                logger.info(f"Response: {json.dumps(result, indent=2)}")
                return True
            else:
                logger.error(f"Request failed with status code {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error sending request: {e}")
            return False


async def main():
    """Run the tests."""
    logger.info("Starting API integration tests")
    
    # Check if the API server is running
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{API_BASE_URL}/docs")
            if response.status_code != 200:
                logger.error("API server is not running. Please start the server before running the tests.")
                return
    except Exception:
        logger.error("API server is not running. Please start the server before running the tests.")
        return
    
    # Run tests
    crew_run_success = await test_crew_run_endpoint()
    agent_chat_success = await test_agent_chat_endpoint()
    
    # Print summary
    logger.info("Test summary:")
    logger.info(f"- /api/v1/crew/run: {'SUCCESS' if crew_run_success else 'FAILED'}")
    logger.info(f"- /api/v1/crew/agent/chat: {'SUCCESS' if agent_chat_success else 'FAILED'}")


if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
