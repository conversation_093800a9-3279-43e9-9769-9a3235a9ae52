"""
API endpoints for poster generation and editing using Ideogram AI.
"""

import logging
import httpx
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File, Query
from fastapi.responses import StreamingResponse, Response
from typing import Optional, List
# import json  # Not needed for current implementation

from app.core.auth import verify_api_key
from app.services.poster_service import poster_service
from app.schemas.poster import (
    # PosterGenerationRequest,  # Not used in current implementation
    FrontendPosterResponse,
    FrontendStreamResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/generate",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_poster(
    prompt: str = Form(..., description="Description of the poster to create"),
    resolution: Optional[str] = Form(default=None, description="Ideogram resolution (e.g., 1024x1024)"),
    aspect_ratio: Optional[str] = Form(default=None, description="Ideogram aspect ratio (e.g., 1x1)"),
    rendering_speed: str = Form(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY"),
    magic_prompt: str = Form(default="AUTO", description="Magic prompt enhancement: AUTO/ON/OFF"),
    negative_prompt: Optional[str] = Form(default=None, description="What to exclude from image"),
    num_images: int = Form(default=1, description="Number of images to generate (1-8)"),
    style_type: str = Form(default="DESIGN", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")
) -> FrontendPosterResponse:
    """Generate a poster using Ideogram AI v3 model."""

    try:
        logger.info(f"🎨 Generating poster with Ideogram: {prompt[:100]}...")

        # Call the service with all parameters
        service_response = await poster_service.generate_poster(
            prompt=prompt,
            resolution=resolution,
            aspect_ratio=aspect_ratio,
            rendering_speed=rendering_speed,
            magic_prompt=magic_prompt,
            negative_prompt=negative_prompt,
            num_images=num_images,
            style_type=style_type
        )

        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)

    except Exception as e:
        logger.error(f"Error in generate_poster endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during poster generation: {e}"
        )


@router.post(
    "/multi-turn-edit",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def multi_turn_edit(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendPosterResponse:
    """Edit an existing poster using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await poster_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn edit: {e}"
        )


@router.post(
    "/stream-generate",
    dependencies=[Depends(verify_api_key)],
)
async def stream_generate_poster(
    prompt: str = Form(..., description="Description of the poster to create")
):
    """Generate a poster with streaming partial images."""
    
    try:
        logger.info(f"🌊 Streaming poster generation: {prompt[:100]}...")
        
        async def generate_stream():
            async for chunk in poster_service.stream_generation(prompt):
                # Convert to frontend response format
                frontend_response = FrontendStreamResponse(
                    success=chunk.get("success", False),
                    partial_image=chunk.get("partial_image"),
                    image_url=chunk.get("image_url"),
                    index=chunk.get("index"),
                    progress=chunk.get("progress"),
                    error=chunk.get("error")
                )
                
                # Send as Server-Sent Events format
                yield f"data: {frontend_response.model_dump_json()}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in stream_generate_poster endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during streaming generation: {e}"
        )


@router.post(
    "/edit-with-references",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def edit_with_references(
    prompt: str = Form(..., description="Description of the poster to create"),
    resolution: Optional[str] = Form(default=None, description="Ideogram resolution (e.g., 1024x1024)"),
    aspect_ratio: Optional[str] = Form(default=None, description="Ideogram aspect ratio (e.g., 1x1)"),
    reference_images: List[UploadFile] = File(..., description="Reference images to use")
) -> FrontendPosterResponse:
    """Generate poster using reference images."""
    
    try:
        logger.info(f"🖼️ Generating with {len(reference_images)} reference images: {prompt[:100]}...")
        
        # Validate reference images
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        for ref_image in reference_images:
            if not ref_image.content_type or not ref_image.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid file type: {ref_image.content_type}. Only images are allowed."
                )
        
        # Call the service
        service_response = await poster_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            resolution=resolution,
            aspect_ratio=aspect_ratio
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference edit: {e}"
        )


@router.post(
    "/edit-with-mask",
    response_model=FrontendPosterResponse,
    dependencies=[Depends(verify_api_key)],
)
async def edit_with_mask(
    prompt: str = Form(..., description="Description of what to put in the masked area"),
    image: UploadFile = File(..., description="The original image to edit"),
    mask: UploadFile = File(..., description="The mask image (white areas will be edited)")
) -> FrontendPosterResponse:
    """Edit poster using a mask to specify areas to change."""
    
    try:
        logger.info(f"✏️ Editing with mask: {prompt[:100]}...")
        
        # Validate file types
        if not image.content_type or not image.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid image file type: {image.content_type}. Only images are allowed."
            )
        
        if not mask.content_type or not mask.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid mask file type: {mask.content_type}. Only images are allowed."
            )
        
        # Call the service
        service_response = await poster_service.edit_with_mask(
            image=image,
            mask=mask,
            prompt=prompt
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask edit: {e}"
        )


@router.get(
    "/download-image",
    dependencies=[Depends(verify_api_key)],
)
async def download_image(
    url: str = Query(..., description="The image URL to download")
):
    """
    Proxy endpoint to download images from external sources (like Ideogram)
    and serve them directly to bypass CORS restrictions.
    """
    try:
        logger.info(f"📥 Proxying image download from: {url[:100]}...")

        # Validate that the URL is from a trusted source (Ideogram)
        if not url.startswith("https://ideogram.ai/"):
            raise HTTPException(
                status_code=400,
                detail="Only Ideogram image URLs are allowed"
            )

        logger.info(f"Attempting to fetch image from: {url}")

        # Fetch the image from the external URL with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://ideogram.ai/',
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)

            logger.info(f"External URL response status: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"Failed to fetch image: {response.status_code}, Response: {response.text[:200]}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Failed to fetch image from external source: {response.status_code}"
                )

            # Get content type from the response
            content_type = response.headers.get("content-type", "image/png")

            # Determine file extension based on content type
            if "jpeg" in content_type or "jpg" in content_type:
                file_extension = "jpg"
            elif "png" in content_type:
                file_extension = "png"
            elif "webp" in content_type:
                file_extension = "webp"
            else:
                file_extension = "png"  # Default fallback

            # Create filename with timestamp
            import time
            filename = f"poster-{int(time.time())}.{file_extension}"

            # Return the image as a downloadable response
            return Response(
                content=response.content,
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename={filename}",
                    "Content-Length": str(len(response.content)),
                    "Cache-Control": "no-cache"
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in download_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image download: {e}"
        )
