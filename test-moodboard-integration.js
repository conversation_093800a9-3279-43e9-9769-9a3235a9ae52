/**
 * Test script to verify Supabase moodboard integration
 * This script tests the database schema, RLS policies, and basic CRUD operations
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pthewpjbegkgomvyhkin.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI4NzQsImV4cCI6MjA1MDU0ODg3NH0.Ej6Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testMoodboardIntegration() {
  console.log('🧪 Testing Moodboard Supabase Integration...\n')

  try {
    // Test 1: Check if tables exist
    console.log('1. Testing table existence...')
    const { data: moodboards, error: moodboardsError } = await supabase
      .from('moodboards')
      .select('count')
      .limit(1)

    if (moodboardsError) {
      console.error('❌ Moodboards table not found:', moodboardsError.message)
      return
    }
    console.log('✅ Moodboards table exists')

    const { data: history, error: historyError } = await supabase
      .from('moodboard_history')
      .select('count')
      .limit(1)

    if (historyError) {
      console.error('❌ Moodboard history table not found:', historyError.message)
      return
    }
    console.log('✅ Moodboard history table exists\n')

    // Test 2: Test RLS policies (should fail without authentication)
    console.log('2. Testing RLS policies without authentication...')
    const { data: unauthorizedData, error: unauthorizedError } = await supabase
      .from('moodboards')
      .select('*')

    if (unauthorizedError || !unauthorizedData || unauthorizedData.length === 0) {
      console.log('✅ RLS policies working - unauthorized access blocked')
    } else {
      console.log('⚠️  RLS policies may not be working correctly')
    }

    // Test 3: Test anonymous insert (should fail)
    console.log('\n3. Testing unauthorized insert...')
    const { data: insertData, error: insertError } = await supabase
      .from('moodboards')
      .insert({
        user_id: 'test-user',
        title: 'Test Moodboard',
        description: 'This should fail'
      })

    if (insertError) {
      console.log('✅ RLS policies working - unauthorized insert blocked')
    } else {
      console.log('⚠️  RLS policies may not be working - insert succeeded without auth')
    }

    // Test 4: Test schema structure
    console.log('\n4. Testing schema structure...')
    const { data: schemaData, error: schemaError } = await supabase
      .from('moodboards')
      .select('*')
      .limit(0)

    if (!schemaError) {
      console.log('✅ Schema structure accessible')
    }

    console.log('\n🎉 Basic integration tests completed!')
    console.log('\nNext steps:')
    console.log('- Test with authenticated user')
    console.log('- Test CRUD operations with proper authentication')
    console.log('- Test auto-save functionality')
    console.log('- Test cross-browser compatibility')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testMoodboardIntegration()
