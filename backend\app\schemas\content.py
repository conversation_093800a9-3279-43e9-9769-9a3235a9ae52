"""Schemas for content generation requests and responses."""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum

class ContentStatus(Enum):
    """Enumeration for content generation status."""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"

class GenerateContentRequest(BaseModel):
    """Request schema for CrewAI-powered content generation."""
    prompt: str = Field(..., description="The user's prompt or content request")
    type: str = Field(..., description="Type of content to generate (e.g., social_media, campaign, general_content)")
    topic: Optional[str] = Field(None, description="Main topic for the content")
    audience: Optional[str] = Field(None, description="Target audience for the content")
    tone: Optional[str] = Field(None, description="Tone of the content (e.g., professional, casual, formal)")
    context: Optional[str] = Field(None, description="Additional context for generation")
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    client_info: Optional[Dict[str, Any]] = Field(default_factory=lambda: {}, description="Client information")
    priority: Optional[str] = Field("normal", description="Request priority (high, normal, low)")
    include_visual_concepts: bool = Field(default=False, description="Whether to include visual concept generation")
    include_audio: bool = Field(default=False, description="Whether to include audio script generation")
    max_execution_time: Optional[int] = Field(None, description="Maximum execution time in seconds")
    process_type: Optional[str] = Field(None, description="Specific process type to use (sequential/hierarchical)")

class GenerateContentResponse(BaseModel):
    """Response schema for CrewAI-powered content generation."""
    request_id: str
    status: ContentStatus
    result: str
    error: Optional[str] = None
    reasoning_trace: Optional[Dict[str, Any]] = Field(default_factory=lambda: {}, description="Detailed reasoning trace data")
    metadata: Dict[str, Any] = Field(default_factory=lambda: {}, description="Additional metadata about the generation process")
    
    class Config:
        use_enum_values = True

class ContentGenerationRequest(BaseModel):
    """Request schema for content generation."""
    prompt: str = Field(..., description="The prompt to generate content from")
    max_tokens: int = Field(default=1000, ge=1, le=4000, description="Maximum number of tokens to generate")
    temperature: float = Field(default=0.7, ge=0, le=2.0, description="Sampling temperature")
    model: str = Field(default="gpt-4", description="Model to use for generation")

class ContentGenerationResponse(BaseModel):
    """Response schema for content generation."""
    content: str = Field(..., description="Generated content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the generation")
    error: Optional[str] = Field(None, description="Error message if generation failed")

class ImprovePromptRequest(BaseModel):
    prompt: str
    context: Optional[str] = None

class ImprovePromptResponse(BaseModel):
    original_prompt: str
    improved_prompt: str
    explanation: Optional[str] = None
