# New implementation using SQLite for safe concurrent access
import json
import os
import sqlite3
from datetime import datetime
from typing import Any, Dict, List, Optional

DB_FILE = os.getenv("DB_FILE", "content_history.db")

# --- helpers -----------------------------------------------------------


def _get_conn():
    conn = sqlite3.connect(DB_FILE, check_same_thread=False)
    conn.row_factory = sqlite3.Row
    return conn


# Ensure table exists at module import
with _get_conn() as _conn:
    _conn.execute(
        """
        CREATE TABLE IF NOT EXISTS traces (
            id TEXT PRIMARY KEY,
            user_id TEXT,
            prompt TEXT,
            mode TEXT,
            asset TEXT,
            reasoning_trace TEXT,
            created_at TEXT
        )
        """
    )
    _conn.commit()

# ----------------------------------------------------------------------


def save_trace(
    user_id: str,
    prompt: str,
    mode: str,
    asset: str,
    reasoning_trace: List[Dict[str, Any]],
):
    """Guarda un trace en la base SQLite y devuelve el entry dict."""
    entry_id = f"trace_{int(datetime.utcnow().timestamp()*1000)}"
    entry = {
        "id": entry_id,
        "user_id": user_id,
        "prompt": prompt,
        "mode": mode,
        "asset": asset,
        "reasoning_trace": reasoning_trace,
        "created_at": datetime.utcnow().isoformat() + "Z",
    }
    rt_json = json.dumps(reasoning_trace, ensure_ascii=False)
    with _get_conn() as conn:
        conn.execute(
            "INSERT INTO traces (id, user_id, prompt, mode, asset, reasoning_trace, created_at) VALUES (?,?,?,?,?,?,?)",
            (
                entry_id,
                user_id,
                prompt,
                mode,
                asset,
                rt_json,
                entry["created_at"],
            ),
        )
        conn.commit()
    return entry


def get_history(user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """Devuelve lista de traces (puede filtrar por usuario)."""
    query = "SELECT * FROM traces"
    params: tuple = ()
    if user_id:
        query += " WHERE user_id = ?"
        params = (user_id,)
    query += " ORDER BY created_at DESC LIMIT 100"
    with _get_conn() as conn:
        rows = conn.execute(query, params).fetchall()
    return [
        {
            **dict(row),
            "reasoning_trace": json.loads(row["reasoning_trace"]),
        }
        for row in rows
    ]


def get_trace(trace_id: str) -> Optional[Dict[str, Any]]:
    with _get_conn() as conn:
        row = conn.execute("SELECT * FROM traces WHERE id = ?", (trace_id,)).fetchone()
    if row:
        data = dict(row)
        data["reasoning_trace"] = json.loads(data["reasoning_trace"])
        return data
    return None
