"""
Service for replace background and relight functionality using Stability AI v2beta API.
"""
import httpx
import io
import logging
from fastapi import HTTP<PERSON>x<PERSON>, UploadFile
from PIL import Image

from app.core.config import settings
from app.schemas.replace_background import (
    ReplaceBackgroundRequest,
    ReplaceBackgroundInitialResponse,
    ReplaceBackgroundFinalResponse
)

logger = logging.getLogger(__name__)


def get_stability_headers_json():
    """Get headers for Stability AI API requests expecting JSON response."""
    return {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "application/json"
    }


def get_stability_headers_polling():
    """Get headers for Stability AI API polling requests."""
    headers = {
        "Authorization": f"Bearer {settings.STABILITY_API_KEY}",
        "Accept": "*/*"  # Según documentación línea 2997-3002: usar */* para recibir bytes directamente
    }
    logger.info(f"Headers para polling: {headers}")
    return headers


async def validate_replace_background_image(image_file: UploadFile) -> bytes:
    """
    Validate an image file for replace background operation.

    Args:
        image_file: The uploaded image file

    Returns:
        bytes: The validated image content

    Raises:
        HTTPException: If validation fails
    """
    try:
        # Leer el contenido del archivo
        image_content = await image_file.read()

        # Validar tamaño del archivo
        max_size = 10 * 1024 * 1024  # 10MB
        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"Image file too large. Maximum size is {max_size / (1024*1024):.1f}MB"
            )

        # Validar que sea una imagen válida
        try:
            with Image.open(io.BytesIO(image_content)) as img:
                # Validar dimensiones mínimas (64x64 según documentación)
                if img.width < 64 or img.height < 64:
                    raise HTTPException(
                        status_code=400,
                        detail="Image dimensions too small. Minimum size is 64x64 pixels"
                    )

                # Validar dimensiones máximas (9,437,184 pixels total según documentación)
                total_pixels = img.width * img.height
                max_pixels = 9_437_184
                if total_pixels > max_pixels:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Image too large. Maximum total pixels: {max_pixels:,}"
                    )

                # Validar formato
                if img.format not in ['JPEG', 'PNG', 'WEBP']:
                    raise HTTPException(
                        status_code=400,
                        detail="Unsupported image format. Supported formats: JPEG, PNG, WebP"
                    )

                logger.info(f"Image validated: {img.width}x{img.height}, format: {img.format}")

        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=400, detail="Invalid image file")

        return image_content

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating image: {e}")
        raise HTTPException(status_code=400, detail="Error processing image file")


async def start_replace_background_stability(
    image_file: UploadFile,
    background_reference_file: UploadFile = None,
    light_reference_file: UploadFile = None,
    request: ReplaceBackgroundRequest = ReplaceBackgroundRequest()
) -> ReplaceBackgroundInitialResponse:
    """
    Start replace background and relight operation using Stability AI v2beta API.

    Args:
        image_file: The subject image file to process
        background_reference_file: Optional background reference image
        light_reference_file: Optional light reference image
        request: The replace background parameters

    Returns:
        ReplaceBackgroundInitialResponse: The generation ID for polling

    Raises:
        HTTPException: If replace background operation fails to start
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # Validar la imagen principal
        image_content = await validate_replace_background_image(image_file)

        # URL de la API v2beta para replace background and relight
        url = f"{settings.STABILITY_API_URL}/v2beta/stable-image/edit/replace-background-and-relight"

        # Headers para recibir respuesta JSON
        headers = get_stability_headers_json()

        # Preparar archivos para el request
        files = {
            "subject_image": ("subject.jpg", io.BytesIO(image_content), image_file.content_type or "image/jpeg")
        }

        # Agregar background reference si se proporciona
        if background_reference_file:
            bg_content = await validate_replace_background_image(background_reference_file)
            files["background_reference"] = ("background.jpg", io.BytesIO(bg_content), background_reference_file.content_type or "image/jpeg")

        # Agregar light reference si se proporciona
        if light_reference_file:
            light_content = await validate_replace_background_image(light_reference_file)
            files["light_reference"] = ("light.jpg", io.BytesIO(light_content), light_reference_file.content_type or "image/jpeg")

        # Preparar FormData
        form_data = {
            "output_format": request.output_format
        }

        # Agregar background_prompt si se proporciona
        if request.background_prompt:
            form_data["background_prompt"] = request.background_prompt

        # Agregar foreground_prompt si se proporciona
        if request.foreground_prompt:
            form_data["foreground_prompt"] = request.foreground_prompt

        # Agregar negative_prompt si se proporciona
        if request.negative_prompt:
            form_data["negative_prompt"] = request.negative_prompt

        # Agregar parámetros opcionales
        if request.preserve_original_subject is not None:
            form_data["preserve_original_subject"] = request.preserve_original_subject

        if request.original_background_depth is not None:
            form_data["original_background_depth"] = request.original_background_depth

        if request.keep_original_background is not None:
            form_data["keep_original_background"] = "true" if request.keep_original_background else "false"

        if request.light_source_direction:
            form_data["light_source_direction"] = request.light_source_direction

        if request.light_source_strength is not None and (request.light_source_direction or light_reference_file):
            form_data["light_source_strength"] = request.light_source_strength

        # Agregar seed si se especifica
        if request.seed and request.seed > 0:
            form_data["seed"] = request.seed

        logger.info(f"Starting replace background using Stability AI v2beta")
        logger.info(f"URL: {url}")
        logger.info(f"Background prompt: {request.background_prompt}")
        logger.info(f"Foreground prompt: {request.foreground_prompt}")
        logger.info(f"Output format: {request.output_format}")
        logger.info(f"Form data: {form_data}")
        logger.info(f"Files: {list(files.keys())}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=120.0) as client:  # 2 minutos timeout
            response = await client.post(
                url,
                headers=headers,
                data=form_data,
                files=files
            )

            logger.info(f"Stability AI response status: {response.status_code}")
            logger.info(f"Stability AI response headers: {dict(response.headers)}")

            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI error: {error_text}"
                )

            # Procesar respuesta JSON
            try:
                result = response.json()
                logger.info(f"Stability AI response JSON: {result}")
            except Exception as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.error(f"Response text: {response.text}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Invalid JSON response from Stability AI: {str(e)}"
                )

            generation_id = result.get("id")

            if not generation_id:
                logger.error(f"No generation ID in response: {result}")
                raise ValueError("No generation ID in response")

            logger.info(f"Replace background started successfully. Generation ID: {generation_id}")

            return ReplaceBackgroundInitialResponse(id=generation_id)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error starting replace background operation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error starting replace background operation: {str(e)}"
        )


async def check_replace_background_status_stability(generation_id: str) -> ReplaceBackgroundFinalResponse:
    """
    Check the status of a replace background generation using Stability AI v2beta API.

    Args:
        generation_id: The generation ID to check

    Returns:
        ReplaceBackgroundFinalResponse: The final result or status

    Raises:
        HTTPException: If status check fails or generation is still in progress
    """
    try:
        # Verificar API key
        if not settings.STABILITY_API_KEY:
            logger.error("STABILITY_API_KEY not configured")
            raise HTTPException(status_code=500, detail="Stability AI API key not configured")

        # URL de la API v2beta para consultar resultados
        url = f"{settings.STABILITY_API_URL}/v2beta/results/{generation_id}"

        # Headers para recibir respuesta JSON
        headers = get_stability_headers_polling()

        logger.info(f"Checking replace background status for generation ID: {generation_id}")
        logger.info(f"URL: {url}")

        # Realizar la petición
        async with httpx.AsyncClient(timeout=60.0) as client:  # 1 minuto timeout
            response = await client.get(url, headers=headers)

            logger.info(f"Stability AI status response: {response.status_code}")
            logger.info(f"Stability AI status response headers: {dict(response.headers)}")

            # Si está en progreso (202)
            if response.status_code == 202:
                logger.info("Generation still in progress")
                raise HTTPException(status_code=202, detail="Generation still in progress")

            # Si hay error
            if response.status_code != 200:
                error_text = response.text
                logger.error(f"Stability AI status error {response.status_code}: {error_text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Stability AI status error: {error_text}"
                )

            # Según documentación línea 9779-9780: con Accept: image/* recibimos imagen binaria directa
            try:
                image_bytes = response.content
                if not image_bytes:
                    raise ValueError("Empty response from Stability AI")

                # Convertir imagen binaria a base64
                import base64
                image_data = base64.b64encode(image_bytes).decode('utf-8')

                # Obtener metadatos de headers (si están disponibles)
                seed = response.headers.get("seed")
                finish_reason = response.headers.get("finish-reason", "SUCCESS")

                logger.info(f"Replace background completed successfully. Image size: {len(image_bytes)} bytes")
                logger.info(f"Seed: {seed}, Finish reason: {finish_reason}")

                return ReplaceBackgroundFinalResponse(
                    image=image_data,
                    seed=int(seed) if seed and str(seed).isdigit() else None,
                    finish_reason=finish_reason
                )

            except Exception as e:
                logger.error(f"Failed to process binary response: {e}")
                logger.error(f"Response headers: {dict(response.headers)}")
                logger.error(f"Response size: {len(response.content)} bytes")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to process image response: {str(e)}"
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error checking replace background status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error checking replace background status: {str(e)}"
        )
