"""
Schemas for search and replace (replace objects) functionality using Stability AI v2beta API.
"""

from typing import Optional, Literal
from pydantic import BaseModel, Field


class SearchReplaceRequest(BaseModel):
    """Request schema for Stability AI search and replace API."""
    
    prompt: str = Field(
        min_length=1,
        max_length=10000,
        description="What you wish to see in the output image. A strong, descriptive prompt that clearly defines elements, colors, and subjects will lead to better results."
    )
    
    search_prompt: str = Field(
        max_length=10000,
        description="Short description of what to replace in the image (e.g., 'dog', 'car', 'glasses')"
    )
    
    negative_prompt: Optional[str] = Field(
        default=None,
        max_length=10000,
        description="A blurb of text describing what you do NOT wish to see in the output image"
    )
    
    grow_mask: Optional[int] = Field(
        default=3,
        ge=0,
        le=20,
        description="Grows the edges of the mask outward in all directions by the specified number of pixels"
    )
    
    seed: Optional[int] = Field(
        default=0,
        ge=0,
        le=4294967294,
        description="A specific value that is used to guide the 'randomness' of the generation (0 = random)"
    )
    
    output_format: Optional[Literal["jpeg", "png", "webp"]] = Field(
        default="png",
        description="Dictates the content-type of the generated image"
    )

    style_preset: Optional[Literal[
        "3d-model", "analog-film", "anime", "cinematic", "comic-book", 
        "digital-art", "enhance", "fantasy-art", "isometric", "line-art", 
        "low-poly", "modeling-compound", "neon-punk", "origami", 
        "photographic", "pixel-art", "tile-texture"
    ]] = Field(
        default=None,
        description="Guides the image model towards a particular style"
    )


class SearchReplaceResponse(BaseModel):
    """Response schema from Stability AI search and replace API."""
    
    image: str = Field(description="Base64 encoded image data")
    seed: Optional[int] = Field(description="The seed used for generation")
    finish_reason: str = Field(description="Reason the generation finished")


class FrontendSearchReplaceResponse(BaseModel):
    """Response schema for frontend search and replace requests."""
    
    success: bool = Field(description="Whether the operation was successful")
    image_url: Optional[str] = Field(default=None, description="Data URL of the generated image")
    seed: Optional[int] = Field(default=None, description="The seed used for generation")
    finish_reason: Optional[str] = Field(default=None, description="Reason the generation finished")
    metadata: Optional[dict] = Field(default=None, description="Additional metadata")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")
