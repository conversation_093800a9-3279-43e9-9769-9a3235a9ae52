"""AI provider service for handling different AI models."""

import json
import logging
from typing import Dict, Any
from abc import ABC, abstractmethod

import google.generativeai as genai
from fastapi import HTTPException
from app.core.config import settings

logger = logging.getLogger(__name__)


class AIProvider(ABC):
    """Abstract base class for AI providers."""

    @abstractmethod
    async def generate_content(self, prompt: str) -> str:
        """Generate content using the AI provider."""
        pass

    @abstractmethod
    def parse_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response and extract JSON."""
        pass

    @abstractmethod
    def get_model_name(self) -> str:
        """Get the model name."""
        pass


class GeminiProvider(AIProvider):
    """Gemini AI provider implementation."""

    def __init__(self):
        """Initialize Gemini provider."""
        self.model = None
        self.model_name = 'gemini-1.5-flash'

        if settings.GEMINI_API_KEY:
            try:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.model = genai.GenerativeModel(self.model_name)
                logger.info(f"Gemini AI initialized successfully with model: {self.model_name}")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini AI: {e}")
                self.model = None
        else:
            logger.warning("GEMINI_API_KEY not found.")

    async def generate_content(self, prompt: str) -> str:
        """Generate content using Gemini AI."""
        if not self.model:
            raise HTTPException(
                status_code=503,
                detail="AI service not available. Please ensure Gemini AI is properly configured."
            )

        try:
            # Use higher temperature and different sampling for more diverse results
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.9,  # Higher temperature for more creativity and diversity
                    top_p=0.95,       # Higher top_p for more diverse token selection
                    top_k=50,         # Increased top_k for more variety
                    max_output_tokens=8192,
                    candidate_count=1,
                )
            )

            if not response.text:
                raise ValueError("Empty response from Gemini AI")

            return response.text
        except Exception as e:
            logger.error(f"Error calling Gemini AI: {e}")
            self._handle_ai_error(e)

    def parse_response(self, response: str) -> Dict[str, Any]:
        """Parse Gemini response and extract JSON."""
        try:
            # Clean the response
            response = response.strip()

            # Find JSON content
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")

            json_str = response[start_idx:end_idx]
            parsed_data = json.loads(json_str)

            # Validate that we have personas
            if not parsed_data.get('personas') or len(parsed_data.get('personas', [])) == 0:
                logger.warning("No personas found in AI response")
                raise ValueError("No personas in AI response")

            return parsed_data

        except Exception as e:
            logger.error(f"Error parsing Gemini response: {e}")
            logger.error(f"Raw response: {response[:500]}...")  # Log first 500 chars for debugging
            raise ValueError(f"Failed to parse AI response: {str(e)}")

    def get_model_name(self) -> str:
        """Get the model name."""
        return self.model_name if self.model else "unavailable"

    def _handle_ai_error(self, error: Exception) -> None:
        """Handle AI-specific errors with appropriate HTTP responses."""
        error_str = str(error).lower()
        
        if "quota" in error_str or "limit" in error_str:
            raise HTTPException(
                status_code=429,
                detail="API quota exceeded. Please try again later or contact support."
            )
        elif "authentication" in error_str or "api key" in error_str:
            raise HTTPException(
                status_code=401,
                detail="AI service authentication failed. Please contact support."
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"AI generation failed: {str(error)}. Please try again or contact support if the issue persists."
            )


class MockProvider(AIProvider):
    """Mock AI provider for testing and fallback."""

    def __init__(self):
        """Initialize mock provider."""
        self.model_name = "mock"

    async def generate_content(self, prompt: str) -> str:
        """Generate mock content."""
        # Return a simple mock JSON response
        mock_response = {
            "personas": [
                {
                    "name": "Ana García Martínez",
                    "age": 32,
                    "gender": "Femenino",
                    "location": "Madrid, España",
                    "education": "Máster en Administración de Empresas",
                    "income_level": "Medio-alto",
                    "marital_status": "Casada",
                    "job": {
                        "title": "Directora de Marketing",
                        "company_size": "Mediana",
                        "industry": "Tecnología",
                        "responsibilities": ["Estrategia de marketing", "Gestión de equipos", "Análisis de ROI"]
                    },
                    "personal_background": "Profesional ambiciosa con 8 años de experiencia en marketing digital.",
                    "goals": ["Aumentar la eficiencia del equipo", "Mejorar el ROI de campañas"],
                    "challenges": ["Presupuesto limitado", "Falta de tiempo"],
                    "buying_process": {
                        "research_methods": ["Google", "LinkedIn", "Webinars"],
                        "decision_factors": ["ROI", "Facilidad de uso", "Soporte técnico"],
                        "timeline": "2-6 semanas"
                    },
                    "objections": ["Precio elevado", "Complejidad de implementación"],
                    "communication_channels": ["Email", "LinkedIn", "WhatsApp Business"],
                    "influences": ["Colegas del sector", "Influencers de marketing"],
                    "quotes": ["Necesito soluciones que realmente funcionen"],
                    "typical_day": "Comienza revisando métricas de campañas, tiene reuniones con el equipo.",
                    "brand_affinities": ["HubSpot", "Google", "Adobe"],
                    "avatar_description": "Mujer profesional, vestimenta business casual",
                    "marketing": {
                        "content_types": ["Blog posts", "Case studies"],
                        "messaging_tips": ["Enfócate en beneficios"],
                        "channels": ["LinkedIn", "Email"],
                        "content_topics": ["Productividad", "Eficiencia"]
                    }
                }
            ],
            "insights": [
                "Los buyer personas muestran una clara preferencia por soluciones eficientes",
                "La comunicación digital es el canal preferido para este segmento"
            ]
        }
        return json.dumps(mock_response)

    def parse_response(self, response: str) -> Dict[str, Any]:
        """Parse mock response."""
        return json.loads(response)

    def get_model_name(self) -> str:
        """Get the model name."""
        return self.model_name


class AIProviderService:
    """Service for managing AI providers."""

    def __init__(self):
        """Initialize AI provider service."""
        self.provider = self._initialize_provider()

    def _initialize_provider(self) -> AIProvider:
        """Initialize the appropriate AI provider."""
        if settings.GEMINI_API_KEY:
            gemini_provider = GeminiProvider()
            if gemini_provider.model:
                return gemini_provider
        
        logger.warning("Using mock AI provider as fallback")
        return MockProvider()

    async def generate_content(self, prompt: str) -> str:
        """Generate content using the active provider."""
        return await self.provider.generate_content(prompt)

    def parse_response(self, response: str) -> Dict[str, Any]:
        """Parse response using the active provider."""
        return self.provider.parse_response(response)

    def get_model_name(self) -> str:
        """Get the active model name."""
        return self.provider.get_model_name()

    def is_ai_available(self) -> bool:
        """Check if real AI is available."""
        return not isinstance(self.provider, MockProvider)
