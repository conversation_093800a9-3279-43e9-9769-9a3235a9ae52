{"conversation_id": "0ab00a85-1941-4423-b8d2-d5ece461a2e1", "persona_name": "<PERSON>", "conversation_type": "sales", "status": "active", "created_at": "2025-06-03T00:52:21.722175", "context": {"persona_profile": {"name": "<PERSON>", "age": 32, "job_title": "Marketing Manager", "industry": "Technology", "company_size": "Medium", "income_level": "Medium", "goals": [], "challenges": [], "communication_style": "professional_balanced", "personality_traits": ["decision_maker"], "buying_process": {}, "objections": [], "influences": []}, "conversation_settings": {"type": "sales", "context": "Test conversation", "persona_mood": "neutral", "interest_level": 50, "trust_level": 30, "urgency_level": 20}, "conversation_rules": {"max_response_length": 150, "objection_probability": 0.3, "buying_signal_probability": 0.2, "question_probability": 0.4, "follow_up_probability": 0.6}, "response_guidelines": {"tone": "professional but approachable", "vocabulary": "standard business language", "response_pattern": "balanced questions about features and benefits"}}, "messages": [{"id": "3121ef59-9b06-4629-b95b-f0b8b3e38d60", "sender": "persona", "message": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, Marketing Manager en una empresa tecnológica mediana.  Me interesa explorar su solución, pero antes necesito saber cómo abordan ...", "timestamp": "2025-06-03T00:52:21.722284", "persona_state": {"interest_level": 50, "trust_level": 30, "urgency_level": 20}}], "analytics": {"total_messages": 1, "conversation_score": 50, "key_topics_discussed": [], "objections_raised": [], "buying_signals": []}}