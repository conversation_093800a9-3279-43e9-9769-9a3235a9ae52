"""
Image Text API endpoints for image generation using OpenAI gpt-image-1 model.
Supports initial generation, references, and mask-based editing.
"""

from fastapi import APIRouter, Form, File, UploadFile, HTTPException
from typing import List, Optional
import logging

from app.services.image_text_service import image_text_service
from app.models.image_text_models import FrontendImageTextResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["image-text"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


@router.post("/generate")
async def generate_image_text(
    prompt: str = Form(..., description="Description of the image to create")
) -> FrontendImageTextResponse:
    """Generate an image using OpenAI's gpt-image-1 model."""
    
    try:
        logger.info(f"🎨 Generating image with text: {prompt[:100]}...")
        
        # Call the service
        service_response = await image_text_service.generate_image(
            prompt=prompt
        )
        
        # Convert to frontend response
        return FrontendImageTextResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in generate_image_text endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image generation: {e}"
        )


@router.post("/multi-turn-edit")
async def multi_turn_edit_image_text(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendImageTextResponse:
    """Edit an existing image using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing image with text: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await image_text_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendImageTextResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit_image_text endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn editing: {e}"
        )


@router.post("/edit-with-references")
async def edit_image_text_with_references(
    prompt: str = Form(..., description="Description of the image to create"),
    reference_images: List[UploadFile] = File(..., description="Reference images (max 4)")
) -> FrontendImageTextResponse:
    """Generate image using reference images."""
    
    try:
        logger.info(f"🖼️ Generating image with text using {len(reference_images)} references: {prompt[:100]}...")
        
        # Validate reference images count
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        # Call the service
        service_response = await image_text_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images
        )
        
        # Convert to frontend response
        return FrontendImageTextResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_image_text_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference-based generation: {e}"
        )


@router.post("/edit-with-mask")
async def edit_image_text_with_mask(
    prompt: str = Form(..., description="Description of the changes to make"),
    image: UploadFile = File(..., description="Original image file"),
    mask: UploadFile = File(..., description="Mask image file (white areas will be edited)")
) -> FrontendImageTextResponse:
    """Edit image using mask-based editing."""
    
    try:
        logger.info(f"🎭 Editing image with text using mask: {prompt[:100]}...")
        
        # Call the service
        service_response = await image_text_service.edit_with_mask(
            prompt=prompt,
            image=image,
            mask=mask
        )
        
        # Convert to frontend response
        return FrontendImageTextResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in edit_image_text_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask-based editing: {e}"
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for image text service."""
    return {
        "status": "healthy",
        "service": "image-text",
        "message": "Image Text service is running"
    }


@router.get("/models")
async def get_available_models():
    """Get available models for image text generation."""
    return {
        "models": [
            {
                "id": "gpt-image-1",
                "name": "GPT Image 1",
                "description": "OpenAI's latest image generation model",
                "capabilities": ["generation", "editing"]
            }
        ],
        "default_model": "gpt-image-1"
    }


@router.get("/config")
async def get_service_config():
    """Get service configuration."""
    return {
        "max_prompt_length": 4000,
        "max_file_size": 10 * 1024 * 1024,  # 10MB
        "allowed_file_types": ["image/jpeg", "image/jpg", "image/png", "image/webp"],
        "max_reference_images": 4,
        "supported_operations": [
            "generate",
            "edit-with-references", 
            "edit-with-mask",
            "multi-turn-edit"
        ]
    }


@router.post("/validate-prompt")
async def validate_prompt(
    prompt: str = Form(..., description="Prompt to validate")
):
    """Validate a prompt for image generation."""
    
    try:
        # Basic validation
        if not prompt.strip():
            return {
                "valid": False,
                "errors": ["Prompt cannot be empty"]
            }
        
        if len(prompt) > 4000:
            return {
                "valid": False,
                "errors": ["Prompt too long (max 4000 characters)"]
            }
        
        # Check for potentially problematic content
        warnings = []
        if len(prompt) < 10:
            warnings.append("Very short prompts may produce unpredictable results")
        
        return {
            "valid": True,
            "errors": [],
            "warnings": warnings,
            "character_count": len(prompt),
            "word_count": len(prompt.split())
        }
        
    except Exception as e:
        logger.error(f"Error validating prompt: {e}")
        return {
            "valid": False,
            "errors": [f"Validation error: {str(e)}"]
        }


@router.post("/validate-file")
async def validate_file(
    file: UploadFile = File(..., description="File to validate")
):
    """Validate an uploaded file for image operations."""
    
    try:
        # Check file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
        if file.content_type not in allowed_types:
            return {
                "valid": False,
                "errors": [f"Invalid file type. Allowed: {', '.join(allowed_types)}"]
            }
        
        # Check file size
        file_content = await file.read()
        file_size = len(file_content)
        max_size = 10 * 1024 * 1024  # 10MB
        
        if file_size > max_size:
            return {
                "valid": False,
                "errors": [f"File too large. Max size: {max_size // (1024*1024)}MB"]
            }
        
        # Reset file pointer
        await file.seek(0)
        
        return {
            "valid": True,
            "errors": [],
            "file_size": file_size,
            "file_type": file.content_type,
            "filename": file.filename
        }
        
    except Exception as e:
        logger.error(f"Error validating file: {e}")
        return {
            "valid": False,
            "errors": [f"File validation error: {str(e)}"]
        }
