#!/usr/bin/env python3
"""
Test script for the image generator download API
"""

import requests
import json

def test_download():
    """Test the download endpoint"""
    
    # Use the image URL from our previous successful test
    test_image_url = "https://ideogram.ai/api/images/ephemeral/1XKdlompT_SRukS36mCHFQ.png?exp=1749666751&sig=84e2c2e07f31481fd8280dee65c4cf19a2026be970cdbdf9ffb1759ac7828bd0"
    
    url = f"http://localhost:8000/api/image-generator/download-image?url={requests.utils.quote(test_image_url)}"
    
    print("Testing Download Endpoint...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Download endpoint working!")
            print(f"Content-Type: {response.headers.get('content-type')}")
            print(f"Content-Length: {response.headers.get('content-length')}")
            print(f"Content-Disposition: {response.headers.get('content-disposition')}")
            
            # Save the file to test
            with open("test_download.png", "wb") as f:
                f.write(response.content)
            print("📁 Test file saved as test_download.png")
            
        else:
            print(f"❌ Download failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Download test error: {e}")

if __name__ == "__main__":
    test_download()
