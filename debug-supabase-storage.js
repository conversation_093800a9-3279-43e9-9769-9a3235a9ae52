/**
 * Debug script for Supabase Storage image loading issues
 * Run this in the browser console to diagnose the problem
 */

console.log('🔍 Starting Supabase Storage Debug...');

// Test Supabase Storage access
async function debugSupabaseStorage() {
  try {
    // Import Supabase client
    const { supabase } = await import('/src/lib/supabase.ts');
    
    console.log('📡 Supabase client loaded');
    
    // Check authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    console.log('🔐 Authentication status:', {
      isAuthenticated: !!session,
      userId: session?.user?.id,
      userEmail: session?.user?.email
    });
    
    if (!session) {
      console.log('⚠️ User not authenticated - this might be the issue!');
      return;
    }
    
    // Test bucket access
    console.log('🪣 Testing bucket access...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
    } else {
      console.log('✅ Available buckets:', buckets.map(b => ({ name: b.name, public: b.public })));
    }
    
    // Test listing files in the design-analysis-images bucket
    console.log('📁 Testing file listing...');
    const userId = session.user.id;
    const { data: files, error: filesError } = await supabase.storage
      .from('design-analysis-images')
      .list(userId, { limit: 5 });
    
    if (filesError) {
      console.error('❌ Error listing files:', filesError);
    } else {
      console.log('📄 Files found:', files?.length || 0);
      if (files && files.length > 0) {
        console.log('📋 File details:', files.map(f => ({ name: f.name, size: f.metadata?.size })));
        
        // Test getting public URL for first file
        const firstFile = files[0];
        const filePath = `${userId}/${firstFile.name}`;
        
        console.log('🔗 Testing public URL generation for:', filePath);
        const { data: { publicUrl } } = supabase.storage
          .from('design-analysis-images')
          .getPublicUrl(filePath);
        
        console.log('🌐 Generated public URL:', publicUrl);
        
        // Test if the URL is accessible
        try {
          const response = await fetch(publicUrl, { method: 'HEAD' });
          console.log('📡 URL accessibility test:', {
            status: response.status,
            statusText: response.statusText,
            accessible: response.ok
          });
          
          if (!response.ok) {
            console.log('❌ URL is not accessible - this confirms the issue!');
            console.log('💡 Possible causes:');
            console.log('   1. Bucket is private and lacks RLS policies');
            console.log('   2. Authentication not properly passed');
            console.log('   3. CORS issues');
          }
        } catch (fetchError) {
          console.error('❌ Error testing URL accessibility:', fetchError);
        }
        
        // Test downloading with authentication
        console.log('🔐 Testing authenticated download...');
        const { data: downloadData, error: downloadError } = await supabase.storage
          .from('design-analysis-images')
          .download(filePath);
        
        if (downloadError) {
          console.error('❌ Authenticated download failed:', downloadError);
        } else {
          console.log('✅ Authenticated download successful:', {
            size: downloadData?.size,
            type: downloadData?.type
          });
          
          // Create object URL for authenticated download
          if (downloadData) {
            const objectUrl = URL.createObjectURL(downloadData);
            console.log('🔗 Object URL created:', objectUrl);
            console.log('💡 This is the solution - use authenticated download + createObjectURL!');
          }
        }
      }
    }
    
  } catch (error) {
    console.error('💥 Debug script error:', error);
  }
}

// Test the designAnalysisService getImageUrl method
async function testGetImageUrl() {
  try {
    console.log('\n🧪 Testing designAnalysisService.getImageUrl...');
    
    // Import the service
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    
    // Get a sample analysis with file_url
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('⚠️ Not authenticated, skipping service test');
      return;
    }
    
    const { data: analyses, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('*')
      .eq('user_id', session.user.id)
      .not('file_url', 'is', null)
      .limit(1);
    
    if (error) {
      console.error('❌ Error fetching analyses:', error);
      return;
    }
    
    if (!analyses || analyses.length === 0) {
      console.log('⚠️ No analyses with images found');
      return;
    }
    
    const analysis = analyses[0];
    console.log('📋 Testing with analysis:', {
      id: analysis.id,
      filename: analysis.original_filename,
      file_url: analysis.file_url
    });
    
    const imageUrl = await designAnalysisService.getImageUrl(analysis.file_url);
    console.log('🔗 Service returned URL:', imageUrl);
    
    if (imageUrl) {
      // Test accessibility
      try {
        const response = await fetch(imageUrl, { method: 'HEAD' });
        console.log('📡 Service URL accessibility:', {
          status: response.status,
          accessible: response.ok
        });
      } catch (fetchError) {
        console.error('❌ Service URL not accessible:', fetchError);
      }
    }
    
  } catch (error) {
    console.error('💥 Service test error:', error);
  }
}

// Main debug function
async function runDebug() {
  console.log('🚀 Starting comprehensive Supabase Storage debug...\n');
  
  await debugSupabaseStorage();
  await testGetImageUrl();
  
  console.log('\n📊 Debug Summary:');
  console.log('================');
  console.log('If authenticated download works but public URLs don\'t,');
  console.log('the solution is to use authenticated download + createObjectURL');
  console.log('instead of relying on public URLs for private buckets.');
}

// Make functions available globally
window.debugSupabaseStorage = debugSupabaseStorage;
window.testGetImageUrl = testGetImageUrl;
window.runDebug = runDebug;

console.log('🔧 Debug script loaded. Run runDebug() to start comprehensive testing.');
