// Test script para verificar la funcionalidad del historial del Visual Complexity Analyzer
// Este script se puede ejecutar en la consola del navegador para probar las funciones

console.log('🧪 Iniciando pruebas del Visual Complexity Analyzer - Gestión de Historial');

// Test 1: Verificar que las pestañas estén presentes
function testTabsPresence() {
  console.log('\n📋 Test 1: Verificando presencia de pestañas...');
  
  const tabs = document.querySelectorAll('[role="tablist"] button');
  const tabTexts = Array.from(tabs).map(tab => tab.textContent.trim());
  
  console.log('Pestañas encontradas:', tabTexts);
  
  const expectedTabs = ['Análisis de Complejidad', 'Resultados Detallados', 'Historial', 'Favoritos'];
  const hasAllTabs = expectedTabs.every(expectedTab => 
    tabTexts.some(tabText => tabText.includes(expectedTab.split(' ')[0]))
  );
  
  if (hasAllTabs) {
    console.log('✅ Todas las pestañas están presentes');
  } else {
    console.log('❌ Faltan algunas pestañas');
  }
  
  return hasAllTabs;
}

// Test 2: Verificar que las pestañas de historial y favoritos estén habilitadas en desarrollo
function testTabsEnabled() {
  console.log('\n🔓 Test 2: Verificando que las pestañas estén habilitadas...');
  
  const historyTab = document.querySelector('[value="history"]');
  const favoritesTab = document.querySelector('[value="favorites"]');
  
  const historyEnabled = historyTab && !historyTab.disabled;
  const favoritesEnabled = favoritesTab && !favoritesTab.disabled;
  
  console.log('Pestaña Historial habilitada:', historyEnabled);
  console.log('Pestaña Favoritos habilitada:', favoritesEnabled);
  
  if (historyEnabled && favoritesEnabled) {
    console.log('✅ Las pestañas están habilitadas correctamente');
  } else {
    console.log('❌ Algunas pestañas están deshabilitadas');
  }
  
  return historyEnabled && favoritesEnabled;
}

// Test 3: Verificar navegación entre pestañas
function testTabNavigation() {
  console.log('\n🧭 Test 3: Probando navegación entre pestañas...');
  
  const historyTab = document.querySelector('[value="history"]');
  const favoritesTab = document.querySelector('[value="favorites"]');
  const analyzeTab = document.querySelector('[value="analyze"]');
  
  if (!historyTab || !favoritesTab || !analyzeTab) {
    console.log('❌ No se encontraron todas las pestañas');
    return false;
  }
  
  // Simular clicks en las pestañas
  console.log('Navegando a Historial...');
  historyTab.click();
  
  setTimeout(() => {
    console.log('Navegando a Favoritos...');
    favoritesTab.click();
    
    setTimeout(() => {
      console.log('Regresando a Análisis...');
      analyzeTab.click();
      console.log('✅ Navegación entre pestañas completada');
    }, 1000);
  }, 1000);
  
  return true;
}

// Test 4: Verificar que se muestren los datos de prueba
function testMockData() {
  console.log('\n📊 Test 4: Verificando datos de prueba...');
  
  const historyTab = document.querySelector('[value="history"]');
  if (historyTab) {
    historyTab.click();
    
    setTimeout(() => {
      const analysisCards = document.querySelectorAll('[data-testid="analysis-card"], .analysis-card, [class*="Card"]');
      console.log('Tarjetas de análisis encontradas:', analysisCards.length);
      
      if (analysisCards.length > 0) {
        console.log('✅ Se encontraron datos de prueba en el historial');
        
        // Verificar contenido de las tarjetas
        analysisCards.forEach((card, index) => {
          const title = card.querySelector('h3, [class*="title"]');
          const filename = card.querySelector('[class*="filename"], [class*="file"]');
          console.log(`Tarjeta ${index + 1}:`, {
            title: title?.textContent?.trim(),
            filename: filename?.textContent?.trim()
          });
        });
      } else {
        console.log('❌ No se encontraron datos de prueba');
      }
    }, 1000);
  }
}

// Test 5: Verificar funcionalidad de botones
function testButtonFunctionality() {
  console.log('\n🔘 Test 5: Verificando funcionalidad de botones...');
  
  const historyTab = document.querySelector('[value="history"]');
  if (historyTab) {
    historyTab.click();
    
    setTimeout(() => {
      const favoriteButtons = document.querySelectorAll('button[class*="favorite"], button[aria-label*="favorite"]');
      const editButtons = document.querySelectorAll('button[class*="edit"], button[aria-label*="edit"]');
      const deleteButtons = document.querySelectorAll('button[class*="delete"], button[aria-label*="delete"]');
      
      console.log('Botones de favorito encontrados:', favoriteButtons.length);
      console.log('Botones de edición encontrados:', editButtons.length);
      console.log('Botones de eliminación encontrados:', deleteButtons.length);
      
      if (favoriteButtons.length > 0 && editButtons.length > 0 && deleteButtons.length > 0) {
        console.log('✅ Todos los tipos de botones están presentes');
      } else {
        console.log('❌ Faltan algunos tipos de botones');
      }
    }, 1000);
  }
}

// Ejecutar todas las pruebas
function runAllTests() {
  console.log('🚀 Ejecutando todas las pruebas...\n');
  
  const test1 = testTabsPresence();
  const test2 = testTabsEnabled();
  
  if (test1 && test2) {
    testTabNavigation();
    setTimeout(() => testMockData(), 2000);
    setTimeout(() => testButtonFunctionality(), 4000);
  }
  
  setTimeout(() => {
    console.log('\n🏁 Pruebas completadas. Revisa los resultados arriba.');
    console.log('💡 Tip: Si alguna prueba falló, verifica que estés en la página del Visual Complexity Analyzer');
  }, 6000);
}

// Ejecutar las pruebas automáticamente
runAllTests();

// Exportar funciones para uso manual
window.testVisualComplexityHistory = {
  runAllTests,
  testTabsPresence,
  testTabsEnabled,
  testTabNavigation,
  testMockData,
  testButtonFunctionality
};

console.log('\n📝 Funciones disponibles en window.testVisualComplexityHistory:');
console.log('- runAllTests(): Ejecuta todas las pruebas');
console.log('- testTabsPresence(): Verifica presencia de pestañas');
console.log('- testTabsEnabled(): Verifica que las pestañas estén habilitadas');
console.log('- testTabNavigation(): Prueba navegación entre pestañas');
console.log('- testMockData(): Verifica datos de prueba');
console.log('- testButtonFunctionality(): Verifica botones de gestión');
