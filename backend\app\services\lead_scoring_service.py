"""Service for intelligent lead scoring and qualification."""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class QualificationStatus(Enum):
    """Lead qualification status levels."""
    HOT = "hot"
    WARM = "warm"
    COLD = "cold"
    NURTURE = "nurture"


@dataclass
class LeadScore:
    """Data class for lead scoring results."""
    overall_score: int
    scoring_factors: Dict[str, int]
    qualification_status: QualificationStatus
    next_best_action: str
    confidence_score: float


class LeadScoringService:
    """Service for calculating lead quality scores and recommendations."""

    def __init__(self):
        """Initialize the lead scoring service."""
        self.scoring_weights = self._load_scoring_weights()
        self.industry_modifiers = self._load_industry_modifiers()
        self.role_modifiers = self._load_role_modifiers()

    def calculate_lead_score(
        self,
        persona_data: Dict[str, Any],
        product_info: Dict[str, Any],
        behavioral_data: Optional[Dict[str, Any]] = None
    ) -> LeadScore:
        """
        Calculate comprehensive lead score based on multiple factors.
        
        Args:
            persona_data: Complete buyer persona information
            product_info: Product/service details
            behavioral_data: Additional behavioral insights
            
        Returns:
            LeadScore with detailed scoring and recommendations
        """
        try:
            # Calculate individual scoring factors
            budget_fit = self._calculate_budget_fit(persona_data, product_info)
            authority_level = self._calculate_authority_level(persona_data)
            need_urgency = self._calculate_need_urgency(persona_data, behavioral_data)
            solution_fit = self._calculate_solution_fit(persona_data, product_info)
            timing_alignment = self._calculate_timing_alignment(persona_data, behavioral_data)

            # Apply industry and role modifiers
            industry = persona_data.get('job', {}).get('industry', 'Technology')
            job_title = persona_data.get('job', {}).get('title', '')
            
            industry_modifier = self.industry_modifiers.get(industry.lower(), 1.0)
            role_modifier = self.role_modifiers.get(self._categorize_role(job_title), 1.0)

            # Calculate weighted overall score
            scoring_factors = {
                "budget_fit": budget_fit,
                "authority_level": authority_level,
                "need_urgency": need_urgency,
                "solution_fit": solution_fit,
                "timing_alignment": timing_alignment
            }

            overall_score = self._calculate_weighted_score(
                scoring_factors, industry_modifier, role_modifier
            )

            # Determine qualification status
            qualification_status = self._determine_qualification_status(overall_score)

            # Generate next best action
            next_best_action = self._generate_next_best_action(
                overall_score, scoring_factors, persona_data
            )

            # Calculate confidence
            confidence = self._calculate_scoring_confidence(persona_data, behavioral_data)

            return LeadScore(
                overall_score=overall_score,
                scoring_factors=scoring_factors,
                qualification_status=qualification_status,
                next_best_action=next_best_action,
                confidence_score=confidence
            )

        except Exception as e:
            logger.error(f"Error calculating lead score: {e}")
            return self._get_default_lead_score()

    def _calculate_budget_fit(self, persona_data: Dict[str, Any], product_info: Dict[str, Any]) -> int:
        """Calculate budget fit score (0-100)."""
        score = 50  # Base score

        # Company size indicates budget capacity
        company_size = persona_data.get('job', {}).get('company_size', 'Medium')
        size_scores = {
            'startup': 40,
            'small': 50,
            'medium': 70,
            'large': 85,
            'enterprise': 95
        }
        score = size_scores.get(company_size.lower(), 50)

        # Industry budget patterns
        industry = persona_data.get('job', {}).get('industry', 'Technology')
        industry_budget_multipliers = {
            'technology': 1.2,
            'finance': 1.1,
            'healthcare': 1.0,
            'retail': 0.9,
            'education': 0.8,
            'non-profit': 0.7
        }
        multiplier = industry_budget_multipliers.get(industry.lower(), 1.0)
        score = int(score * multiplier)

        # Role level affects budget authority
        job_title = persona_data.get('job', {}).get('title', '')
        if any(word in job_title.lower() for word in ['ceo', 'director', 'vp']):
            score += 15
        elif any(word in job_title.lower() for word in ['manager', 'lead']):
            score += 5

        # Goals indicating budget availability
        goals = persona_data.get('goals', [])
        budget_positive_goals = ['growth', 'expansion', 'efficiency', 'automation']
        for goal in goals:
            if any(keyword in goal.lower() for keyword in budget_positive_goals):
                score += 5

        return min(score, 100)

    def _calculate_authority_level(self, persona_data: Dict[str, Any]) -> int:
        """Calculate decision-making authority score (0-100)."""
        score = 30  # Base score

        job_title = persona_data.get('job', {}).get('title', '')
        
        # Executive level
        if any(word in job_title.lower() for word in ['ceo', 'president', 'founder']):
            score = 95
        elif any(word in job_title.lower() for word in ['cto', 'cfo', 'cmo', 'vp']):
            score = 85
        elif any(word in job_title.lower() for word in ['director']):
            score = 75
        elif any(word in job_title.lower() for word in ['manager', 'head', 'lead']):
            score = 60
        elif any(word in job_title.lower() for word in ['senior', 'principal']):
            score = 45
        else:
            score = 30

        # Company size affects authority distribution
        company_size = persona_data.get('job', {}).get('company_size', 'Medium')
        if company_size.lower() in ['startup', 'small']:
            score += 10  # More authority in smaller companies
        elif company_size.lower() in ['large', 'enterprise']:
            score -= 5   # More bureaucracy in larger companies

        # Age can indicate seniority
        age = persona_data.get('age', 35)
        if age > 45:
            score += 5
        elif age < 30:
            score -= 5

        return min(score, 100)

    def _calculate_need_urgency(self, persona_data: Dict[str, Any], behavioral_data: Optional[Dict[str, Any]]) -> int:
        """Calculate urgency of need score (0-100)."""
        score = 40  # Base score

        # Challenges indicate urgent needs
        challenges = persona_data.get('challenges', [])
        urgent_keywords = ['urgent', 'immediate', 'crisis', 'problem', 'issue', 'struggling']
        for challenge in challenges:
            if any(keyword in challenge.lower() for keyword in urgent_keywords):
                score += 15

        # Time-sensitive challenges
        time_keywords = ['time', 'deadline', 'quickly', 'asap', 'soon']
        for challenge in challenges:
            if any(keyword in challenge.lower() for keyword in time_keywords):
                score += 10

        # Goals indicating urgency
        goals = persona_data.get('goals', [])
        urgent_goal_keywords = ['increase', 'improve', 'reduce', 'solve', 'fix']
        for goal in goals:
            if any(keyword in goal.lower() for keyword in urgent_goal_keywords):
                score += 8

        # Behavioral indicators of urgency
        if behavioral_data:
            purchase_timeline = behavioral_data.get('purchase_probability', {}).get('timeline', '')
            if 'immediate' in purchase_timeline.lower():
                score += 20
            elif '1-3' in purchase_timeline:
                score += 15
            elif '3-6' in purchase_timeline:
                score += 10

        # Industry urgency patterns
        industry = persona_data.get('job', {}).get('industry', 'Technology')
        urgent_industries = ['technology', 'startup', 'finance']
        if industry.lower() in urgent_industries:
            score += 5

        return min(score, 100)

    def _calculate_solution_fit(self, persona_data: Dict[str, Any], product_info: Dict[str, Any]) -> int:
        """Calculate how well the solution fits the persona's needs (0-100)."""
        score = 60  # Base score

        # Industry fit
        industry = persona_data.get('job', {}).get('industry', 'Technology')
        product_category = product_info.get('category', 'software')
        
        # Industry-product fit matrix
        fit_matrix = {
            'technology': {'software': 90, 'saas': 95, 'hardware': 70, 'consulting': 60},
            'finance': {'software': 85, 'saas': 80, 'compliance': 95, 'consulting': 75},
            'healthcare': {'software': 75, 'saas': 70, 'compliance': 90, 'hardware': 80},
            'retail': {'software': 80, 'saas': 85, 'ecommerce': 95, 'marketing': 90},
            'education': {'software': 70, 'saas': 75, 'edtech': 95, 'consulting': 65}
        }
        
        industry_fit = fit_matrix.get(industry.lower(), {})
        score = industry_fit.get(product_category.lower(), 60)

        # Role fit
        job_title = persona_data.get('job', {}).get('title', '')
        if 'marketing' in product_info.get('description', '').lower():
            if 'marketing' in job_title.lower():
                score += 15
        if 'sales' in product_info.get('description', '').lower():
            if 'sales' in job_title.lower():
                score += 15

        # Goals alignment
        goals = persona_data.get('goals', [])
        product_description = product_info.get('description', '').lower()
        
        goal_keywords = {
            'efficiency': ['automat', 'efficien', 'streamlin', 'optimi'],
            'growth': ['grow', 'increas', 'expand', 'scale'],
            'cost': ['save', 'reduc', 'cost', 'budget'],
            'quality': ['quality', 'improv', 'better', 'enhance']
        }

        for goal in goals:
            goal_lower = goal.lower()
            for goal_type, keywords in goal_keywords.items():
                if any(keyword in goal_lower for keyword in keywords):
                    if any(keyword in product_description for keyword in keywords):
                        score += 10

        return min(score, 100)

    def _calculate_timing_alignment(self, persona_data: Dict[str, Any], behavioral_data: Optional[Dict[str, Any]]) -> int:
        """Calculate timing alignment score (0-100)."""
        score = 50  # Base score

        # Current month timing
        import datetime
        current_month = datetime.datetime.now().month
        
        # Q1 and Q4 are typically better for B2B sales
        if current_month in [1, 2, 3, 10, 11, 12]:
            score += 15
        elif current_month in [7, 8]:  # Summer slowdown
            score -= 10

        # Industry timing patterns
        industry = persona_data.get('job', {}).get('industry', 'Technology')
        industry_timing = {
            'retail': {1: +10, 2: +5, 7: -15, 8: -15, 9: +10, 10: +15, 11: +20, 12: +15},
            'education': {1: +15, 2: +10, 6: -10, 7: -20, 8: +15, 9: +20},
            'finance': {1: +20, 4: +10, 7: +5, 10: +15},
            'technology': {1: +10, 2: +5, 9: +10, 10: +5}
        }
        
        if industry.lower() in industry_timing:
            monthly_modifier = industry_timing[industry.lower()].get(current_month, 0)
            score += monthly_modifier

        # Behavioral timing indicators
        if behavioral_data:
            timeline = behavioral_data.get('purchase_probability', {}).get('timeline', '')
            if 'immediate' in timeline.lower():
                score += 25
            elif '1-3' in timeline:
                score += 20
            elif '3-6' in timeline:
                score += 10

        # Urgency from challenges
        challenges = persona_data.get('challenges', [])
        urgent_timing_keywords = ['urgent', 'immediate', 'asap', 'deadline']
        for challenge in challenges:
            if any(keyword in challenge.lower() for keyword in urgent_timing_keywords):
                score += 15

        return min(score, 100)

    def _calculate_weighted_score(
        self, 
        factors: Dict[str, int], 
        industry_modifier: float, 
        role_modifier: float
    ) -> int:
        """Calculate weighted overall score."""
        weights = self.scoring_weights
        
        weighted_sum = sum(factors[factor] * weights[factor] for factor in factors)
        base_score = weighted_sum / sum(weights.values())
        
        # Apply modifiers
        final_score = base_score * industry_modifier * role_modifier
        
        return min(int(final_score), 100)

    def _determine_qualification_status(self, overall_score: int) -> QualificationStatus:
        """Determine qualification status based on overall score."""
        if overall_score >= 80:
            return QualificationStatus.HOT
        elif overall_score >= 65:
            return QualificationStatus.WARM
        elif overall_score >= 45:
            return QualificationStatus.COLD
        else:
            return QualificationStatus.NURTURE

    def _generate_next_best_action(
        self, 
        overall_score: int, 
        factors: Dict[str, int], 
        persona_data: Dict[str, Any]
    ) -> str:
        """Generate next best action recommendation."""
        
        if overall_score >= 80:
            return "Contacto inmediato por teléfono o reunión presencial. Lead de alta prioridad."
        
        elif overall_score >= 65:
            return "Enviar propuesta personalizada y agendar demo. Seguimiento en 2-3 días."
        
        elif overall_score >= 45:
            # Find lowest scoring factor for targeted improvement
            lowest_factor = min(factors, key=factors.get)
            
            if lowest_factor == "budget_fit":
                return "Enviar caso de ROI y beneficios económicos. Nutrir con contenido de valor."
            elif lowest_factor == "authority_level":
                return "Identificar decisor real y solicitar introducción. Contenido para múltiples stakeholders."
            elif lowest_factor == "need_urgency":
                return "Crear sentido de urgencia con casos de éxito y oportunidades limitadas."
            elif lowest_factor == "solution_fit":
                return "Enviar demo personalizada y casos de uso específicos de su industria."
            else:
                return "Nutrir con contenido educativo y reevaluar en 30 días."
        
        else:
            return "Incluir en campaña de nurturing a largo plazo. Reevaluar en 3 meses."

    def _calculate_scoring_confidence(
        self, 
        persona_data: Dict[str, Any], 
        behavioral_data: Optional[Dict[str, Any]]
    ) -> float:
        """Calculate confidence in scoring accuracy."""
        confidence = 0.6  # Base confidence
        
        # More complete data = higher confidence
        if persona_data.get('job', {}).get('title'):
            confidence += 0.1
        if persona_data.get('job', {}).get('industry'):
            confidence += 0.1
        if persona_data.get('goals'):
            confidence += 0.05
        if persona_data.get('challenges'):
            confidence += 0.05
        if behavioral_data:
            confidence += 0.1
        
        return min(confidence, 0.95)

    def _categorize_role(self, job_title: str) -> str:
        """Categorize job title into role type."""
        title_lower = job_title.lower()
        if any(word in title_lower for word in ["ceo", "director", "vp", "president"]):
            return "executive"
        elif any(word in title_lower for word in ["manager", "lead", "head"]):
            return "manager"
        else:
            return "individual_contributor"

    def _load_scoring_weights(self) -> Dict[str, float]:
        """Load scoring factor weights."""
        return {
            "budget_fit": 0.25,
            "authority_level": 0.30,
            "need_urgency": 0.20,
            "solution_fit": 0.15,
            "timing_alignment": 0.10
        }

    def _load_industry_modifiers(self) -> Dict[str, float]:
        """Load industry-specific scoring modifiers."""
        return {
            "technology": 1.1,
            "finance": 1.05,
            "healthcare": 1.0,
            "retail": 0.95,
            "education": 0.9,
            "non-profit": 0.85
        }

    def _load_role_modifiers(self) -> Dict[str, float]:
        """Load role-specific scoring modifiers."""
        return {
            "executive": 1.15,
            "manager": 1.05,
            "individual_contributor": 0.9
        }

    def _get_default_lead_score(self) -> LeadScore:
        """Get default lead score for fallback."""
        return LeadScore(
            overall_score=50,
            scoring_factors={
                "budget_fit": 50,
                "authority_level": 50,
                "need_urgency": 50,
                "solution_fit": 50,
                "timing_alignment": 50
            },
            qualification_status=QualificationStatus.COLD,
            next_best_action="Evaluar con más información y nutrir con contenido relevante",
            confidence_score=0.6
        )


# Create singleton instance
lead_scoring_service = LeadScoringService()
