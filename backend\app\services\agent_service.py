"""
Agent Service
Provides services for interacting with the custom agent system
"""

import logging
import time
import uuid
import os
import asyncio
from typing import Dict, Any, Optional, List, AsyncGenerator, Protocol
from dataclasses import dataclass
from abc import ABC, abstractmethod

try:
    from fastapi import HTTPException
except ImportError:
    # Define a fallback HTTPException class if Fast<PERSON>I is not available
    class HTTPException(Exception):
        def __init__(self, status_code: int, detail: str):
            self.status_code = status_code
            self.detail = detail
            super().__init__(f"{status_code}: {detail}")
from sqlalchemy.orm import Session

from app.schemas.crew import CrewRunRequest, CrewRunResponse, AgentChatRequest, AgentChatResponse
from app.db.history_store import save_trace_to_history
from app.db.session import SessionLocal
from app.services.smart_routing_service import SmartRoutingService, RoutingDecision
from app.core.config import (
    AgentType,
    TaskPriorityLevel,
    WorkflowConstants,
    ValidationConstants,
    get_all_agent_metadata,
    agent_settings
)

from agents import (
    AgentOrchest<PERSON>,
    TaskStatus,
    TaskPriority,
    AgentTask
)
from agents.specialized import EmmaA<PERSON>, SEOAgent, ContentAgent
try:
    from agents.llm_providers import GeminiProvider, OpenAIProvider
    LLM_PROVIDERS_AVAILABLE = True
except ImportError:
    # Configure logging first to avoid reference before assignment
    logger = logging.getLogger(__name__)
    logger.warning("LLM providers not available. Install required packages.")
    LLM_PROVIDERS_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)


# ============================================================================
# DEPENDENCY INJECTION INTERFACES AND PROTOCOLS
# ============================================================================

class LLMProviderProtocol(Protocol):
    """Protocol for LLM providers."""
    async def generate(self, prompt: str, **kwargs) -> str: ...
    async def generate_stream(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]: ...


class AgentRegistryProtocol(Protocol):
    """Protocol for agent registry."""
    def get_agent(self, agent_id: str) -> Optional[Any]: ...
    def get_available_agents(self) -> List[Dict[str, Any]]: ...
    def register_agent(self, agent: Any) -> None: ...


class ReasoningTrackerProtocol(Protocol):
    """Protocol for reasoning tracking."""
    def get_trace(self, agent_id: str, request_id: str) -> List[Dict[str, Any]]: ...
    def add_trace_step(self, agent_id: str, request_id: str, step: Dict[str, Any]) -> None: ...
    def clear_trace(self, agent_id: str, request_id: str) -> None: ...


@dataclass
class AgentServiceConfig:
    """Configuration for the agent service."""
    llm_provider: LLMProviderProtocol
    orchestrator: AgentOrchestrator
    reasoning_tracker: ReasoningTrackerProtocol
    agent_registry: AgentRegistryProtocol
    smart_routing: SmartRoutingService


# Initialize the LLM provider
# Try to use Gemini first, fall back to OpenAI if Gemini is not available
gemini_api_key = os.environ.get("GEMINI_API_KEY")
openai_api_key = os.environ.get("OPENAI_API_KEY")

# ============================================================================
# CONCRETE IMPLEMENTATIONS
# ============================================================================

class FallbackLLMProvider:
    """Fallback provider for when no API keys are configured."""

    async def generate(self, prompt: str, **_kwargs) -> str:
        logger.warning("No valid API key configured. Please set GEMINI_API_KEY or OPENAI_API_KEY environment variable.")
        return "I'm sorry, but I need a valid API key to be configured to provide AI responses. Please contact your administrator to set up the required API keys."

    async def generate_stream(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Generate text from a prompt with streaming response."""
        logger.warning("No valid API key configured for streaming.")
        response = await self.generate(prompt, **kwargs)
        yield response


class AgentRegistry:
    """Registry for managing agents."""

    def __init__(self):
        self._agents: Dict[str, Any] = {}
        self._agent_metadata: Dict[str, Dict[str, Any]] = {}

    def register_agent(self, agent: Any) -> None:
        """Register an agent with the registry."""
        self._agents[agent.id] = agent

        # Store comprehensive metadata for quick access
        if hasattr(agent, 'name') and hasattr(agent, 'id'):
            # Extract metadata from the actual agent instance
            metadata = {
                "id": agent.id,
                "name": agent.name,
                "type": type(agent).__name__
            }

            # Add additional metadata if available
            if hasattr(agent, '_role'):
                metadata["role"] = agent._role
            if hasattr(agent, '_backstory'):
                metadata["description"] = agent._backstory
            if hasattr(agent, '_personality'):
                metadata["personality"] = agent._personality
            if hasattr(agent, '_goal'):
                metadata["goal"] = agent._goal
            if hasattr(agent, '_system_prompt'):
                metadata["system_template"] = agent._system_prompt
            if hasattr(agent, 'agent_identity') and hasattr(agent.agent_identity, 'capabilities'):
                metadata["specialties"] = agent.agent_identity.capabilities

            # Add visual metadata
            metadata.update(self._get_agent_visual_metadata(agent.id))

            self._agent_metadata[agent.id] = metadata

    def get_agent(self, agent_id: str) -> Optional[Any]:
        """Get an agent by ID."""
        return self._agents.get(agent_id)

    def get_available_agents(self) -> List[Dict[str, Any]]:
        """Get a list of all available agents with their actual metadata."""
        # Return metadata from registered agents if available
        if self._agent_metadata:
            agents_list = list(self._agent_metadata.values())
            logger.info(f"Returning {len(agents_list)} agents from registry metadata")
            return agents_list

        # Fallback to basic metadata if no agents are registered
        logger.warning("No agents registered in registry, returning fallback metadata")
        return self._get_fallback_agent_metadata()

    def _get_agent_visual_metadata(self, agent_id: str) -> Dict[str, str]:
        """Get visual metadata (color, icon) for an agent."""
        visual_metadata = {
            "emma": {"color": "#4F46E5", "icon": "👩‍💼"},
            "seo": {"color": "#059669", "icon": "📈"},
            "content": {"color": "#DC2626", "icon": "✍️"}
        }
        return visual_metadata.get(agent_id, {"color": "#6B7280", "icon": "🤖"})

    def _get_fallback_agent_metadata(self) -> List[Dict[str, Any]]:
        """Get fallback agent metadata when no agents are registered."""
        return [
            {
                "id": "emma",
                "name": "Emma",
                "role": "Coordinator",
                "description": "I coordinate our team of specialized agents to help with your requests and ensure everything runs smoothly.",
                "specialties": ["coordination", "team management", "task delegation", "workflow optimization"],
                "color": "#4F46E5",
                "icon": "👩‍💼"
            },
            {
                "id": "seo",
                "name": "SEO Specialist",
                "role": "Analyst",
                "description": "I analyze and optimize content for search engines to improve visibility and drive organic traffic.",
                "specialties": ["keyword research", "content optimization", "SEO analysis", "search trends"],
                "color": "#059669",
                "icon": "📈"
            },
            {
                "id": "content",
                "name": "Content Creator",
                "role": "Creator",
                "description": "I create engaging, high-quality content for various platforms tailored to your specific audience and goals.",
                "specialties": ["content creation", "copywriting", "editing", "storytelling", "brand voice"],
                "color": "#DC2626",
                "icon": "✍️"
            }
        ]


class AgentReasoningTracker:
    """Manages reasoning traces for agents."""

    def __init__(self):
        self._traces: Dict[str, List[Dict[str, Any]]] = {}

    def get_trace(self, agent_id: str, request_id: str) -> List[Dict[str, Any]]:
        """Get the reasoning trace for a specific agent and request."""
        trace_key = f"{agent_id}:{request_id}"
        return self._traces.get(trace_key, [])

    def add_trace_step(self, agent_id: str, request_id: str, step: Dict[str, Any]) -> None:
        """Add a step to the reasoning trace."""
        trace_key = f"{agent_id}:{request_id}"
        if trace_key not in self._traces:
            self._traces[trace_key] = []
        self._traces[trace_key].append(step)

    def clear_trace(self, agent_id: str, request_id: str) -> None:
        """Clear the reasoning trace for a specific agent and request."""
        trace_key = f"{agent_id}:{request_id}"
        self._traces.pop(trace_key, None)

# ============================================================================
# FACTORY AND CONFIGURATION
# ============================================================================

class AgentServiceFactory:
    """Factory for creating agent service configurations."""

    @staticmethod
    def create_llm_provider() -> LLMProviderProtocol:
        """Create and configure the LLM provider."""
        gemini_api_key = os.environ.get("GEMINI_API_KEY")
        openai_api_key = os.environ.get("OPENAI_API_KEY")

        if LLM_PROVIDERS_AVAILABLE and gemini_api_key:
            logger.info("Using Gemini LLM provider")
            return GeminiProvider(api_key=gemini_api_key)
        elif LLM_PROVIDERS_AVAILABLE and openai_api_key:
            logger.info("Using OpenAI LLM provider")
            return OpenAIProvider(api_key=openai_api_key)
        else:
            if not LLM_PROVIDERS_AVAILABLE:
                logger.warning("LLM providers not available. Using fallback provider.")
            else:
                logger.warning("No LLM API keys found. Using fallback provider.")
            return FallbackLLMProvider()

    @staticmethod
    def create_agent_registry(llm_provider: LLMProviderProtocol) -> AgentRegistryProtocol:
        """Create and configure the agent registry."""
        registry = AgentRegistry()

        # Load agent configurations from YAML
        agent_configs = AgentServiceFactory._load_agent_configs()

        # Create and register agents from configuration
        for agent_config in agent_configs.get("agents", []):
            agent_name = agent_config.get("name", "").lower()

            if agent_name == "emma":
                emma_agent = AgentServiceFactory._create_emma_from_config(agent_config, llm_provider)
                registry.register_agent(emma_agent)
            elif agent_name == "seo agent" or agent_name == "seo":
                seo_agent = AgentServiceFactory._create_seo_from_config(agent_config, llm_provider)
                # Force the ID to be "seo" for consistency
                seo_agent.id = "seo"
                registry.register_agent(seo_agent)
            elif agent_name == "content agent" or agent_name == "content":
                content_agent = AgentServiceFactory._create_content_from_config(agent_config, llm_provider)
                # Force the ID to be "content" for consistency
                content_agent.id = "content"
                registry.register_agent(content_agent)

        return registry

    @staticmethod
    def _load_agent_configs() -> Dict[str, Any]:
        """Load agent configurations from YAML file."""
        import yaml
        import os

        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "agents.yaml")

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file) or {}
        except FileNotFoundError:
            logger.warning(f"Agent config file not found at {config_path}")
            return {"agents": []}
        except Exception as e:
            logger.error(f"Error loading agent config: {e}")
            return {"agents": []}

    @staticmethod
    def _create_emma_from_config(config: Dict[str, Any], llm_provider: LLMProviderProtocol):
        """Create Emma agent from configuration."""
        from agents.specialized.emma_agent import EmmaAgent

        # Create Emma with configuration-based system prompt
        emma = EmmaAgent("emma", "Emma", llm_provider)

        # Override the system prompt with the one from config
        if "system_template" in config:
            emma._system_prompt = config["system_template"]
            emma._personality = config.get("personality", "")
            emma._role = config.get("role", "")
            emma._backstory = config.get("backstory", "")
            emma._goal = config.get("goal", "")

        return emma

    @staticmethod
    def _create_seo_from_config(config: Dict[str, Any], llm_provider: LLMProviderProtocol):
        """Create SEO agent from configuration."""
        from agents.specialized.seo_agent import SEOAgent

        # Create SEO agent with configuration-based system prompt
        seo = SEOAgent("seo", "SEO Specialist", llm_provider)

        # Override the system prompt with the one from config
        if "system_template" in config:
            seo._system_prompt = config["system_template"]
            seo._personality = config.get("personality", "")
            seo._role = config.get("role", "")
            seo._backstory = config.get("backstory", "")
            seo._goal = config.get("goal", "")

        return seo

    @staticmethod
    def _create_content_from_config(config: Dict[str, Any], llm_provider: LLMProviderProtocol):
        """Create Content agent from configuration."""
        from agents.specialized.content_agent import ContentAgent

        # Create Content agent with configuration-based system prompt
        content = ContentAgent("content", "Content Creator", llm_provider)

        # Override the system prompt with the one from config
        if "system_template" in config:
            content._system_prompt = config["system_template"]
            content._personality = config.get("personality", "")
            content._role = config.get("role", "")
            content._backstory = config.get("backstory", "")
            content._goal = config.get("goal", "")

        return content

    @staticmethod
    def create_orchestrator(agent_registry: AgentRegistryProtocol) -> AgentOrchestrator:
        """Create and configure the orchestrator."""
        orchestrator = AgentOrchestrator("Emma Orchestrator")

        # Register agents with orchestrator
        for agent_data in agent_registry.get_available_agents():
            agent = agent_registry.get_agent(agent_data["id"])
            if agent:
                orchestrator.register_agent(agent)

        return orchestrator

    @staticmethod
    def create_config() -> AgentServiceConfig:
        """Create a complete agent service configuration."""
        llm_provider = AgentServiceFactory.create_llm_provider()
        agent_registry = AgentServiceFactory.create_agent_registry(llm_provider)
        orchestrator = AgentServiceFactory.create_orchestrator(agent_registry)
        reasoning_tracker = AgentReasoningTracker()

        # Load agent configurations for smart routing
        agent_configs = AgentServiceFactory._load_agent_configs()
        agent_config_dict = {}
        for agent_config in agent_configs.get("agents", []):
            agent_name = agent_config.get("name", "").lower()
            agent_config_dict[agent_name] = agent_config

        smart_routing = SmartRoutingService(agent_config_dict)

        return AgentServiceConfig(
            llm_provider=llm_provider,
            orchestrator=orchestrator,
            reasoning_tracker=reasoning_tracker,
            agent_registry=agent_registry,
            smart_routing=smart_routing
        )


# ============================================================================
# MAIN SERVICE CLASS
# ============================================================================

class AgentService:
    """Main service class for agent operations with dependency injection."""

    def __init__(self, config: AgentServiceConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

    def get_available_agents(self) -> List[Dict[str, Any]]:
        """Get a list of all available agents with their actual configurations."""
        try:
            # First try to get agents from the registry (actual agent instances)
            if hasattr(self.config, 'agent_registry') and self.config.agent_registry:
                registry_agents = self.config.agent_registry.get_available_agents()
                if registry_agents:
                    self.logger.info(f"Retrieved {len(registry_agents)} agents from registry")
                    return registry_agents

            # If registry is not available, load from YAML configuration
            agent_configs = AgentServiceFactory._load_agent_configs()
            agents = []

            for agent_config in agent_configs.get("agents", []):
                agent_name = agent_config.get("name", "").lower()

                # Normalize agent ID for consistency
                if agent_name == "emma":
                    agent_id = "emma"
                elif agent_name == "seo agent" or agent_name == "seo":
                    agent_id = "seo"
                elif agent_name == "content agent" or agent_name == "content":
                    agent_id = "content"
                else:
                    agent_id = agent_name

                # Create agent metadata from YAML configuration
                agent_metadata = {
                    "id": agent_id,
                    "name": agent_config.get("name", ""),
                    "role": agent_config.get("role", ""),
                    "description": agent_config.get("backstory", ""),
                    "personality": agent_config.get("personality", ""),
                    "goal": agent_config.get("goal", ""),
                    "system_template": agent_config.get("system_template", ""),
                    "specialties": self._extract_specialties_from_config(agent_config),
                    "color": self._get_agent_color(agent_id),
                    "icon": self._get_agent_icon(agent_id),
                    "model": agent_config.get("model", "gemini-1.5-pro"),
                    "allow_delegation": agent_config.get("allow_delegation", False)
                }
                agents.append(agent_metadata)

            if agents:
                self.logger.info(f"Retrieved {len(agents)} agents from YAML configuration")
                return agents

            # Final fallback to hardcoded list only if no configuration is found
            self.logger.warning("No agent configuration found, using fallback agents")
            return self._get_fallback_agents()

        except Exception as e:
            self.logger.error(f"Error getting available agents: {e}")
            return self._get_fallback_agents()

    def _extract_specialties_from_config(self, agent_config: Dict[str, Any]) -> List[str]:
        """Extract specialties from agent configuration."""
        # Try to extract from various possible fields
        specialties = []

        # Check for explicit specialties field
        if "specialties" in agent_config:
            specialties.extend(agent_config["specialties"])

        # Extract from role and goal
        role = agent_config.get("role", "").lower()
        goal = agent_config.get("goal", "").lower()

        if "seo" in role or "seo" in goal:
            specialties.extend(["keyword research", "content optimization", "SEO analysis", "search trends"])
        elif "content" in role or "content" in goal or "creación de contenido" in role:
            specialties.extend(["content creation", "copywriting", "editing", "storytelling", "brand voice"])
        elif "orquestación" in role or "coordinator" in role.lower():
            specialties.extend(["coordination", "team management", "task delegation", "workflow optimization"])

        return list(set(specialties))  # Remove duplicates

    def _get_agent_color(self, agent_name: str) -> str:
        """Get color for agent based on name."""
        color_map = {
            "emma": "#4F46E5",
            "seo": "#059669",
            "content": "#DC2626",
            "seo agent": "#059669",
            "content agent": "#DC2626"
        }
        return color_map.get(agent_name, "#6B7280")

    def _get_agent_icon(self, agent_name: str) -> str:
        """Get icon for agent based on name."""
        icon_map = {
            "emma": "👩‍💼",
            "seo": "📈",
            "content": "✍️",
            "seo agent": "📈",
            "content agent": "✍️"
        }
        return icon_map.get(agent_name, "🤖")

    def _get_fallback_agents(self) -> List[Dict[str, Any]]:
        """Get fallback agent list when configuration is not available."""
        return [
            {
                "id": "emma",
                "name": "Emma",
                "role": "Coordinator",
                "description": "I coordinate our team of specialized agents to help with your requests and ensure everything runs smoothly.",
                "specialties": ["coordination", "team management", "task delegation", "workflow optimization"],
                "color": "#4F46E5",
                "icon": "👩‍💼"
            },
            {
                "id": "seo",
                "name": "SEO Specialist",
                "role": "Analyst",
                "description": "I analyze and optimize content for search engines to improve visibility and drive organic traffic.",
                "specialties": ["keyword research", "content optimization", "SEO analysis", "search trends"],
                "color": "#059669",
                "icon": "📈"
            },
            {
                "id": "content",
                "name": "Content Creator",
                "role": "Creator",
                "description": "I create engaging, high-quality content for various platforms tailored to your specific audience and goals.",
                "specialties": ["content creation", "copywriting", "editing", "storytelling", "brand voice"],
                "color": "#DC2626",
                "icon": "✍️"
            }
        ]

    def _get_agent_by_id(self, agent_id: str) -> Optional[Any]:
        """Get an agent by ID with normalization."""
        normalized_agent_id = agent_id.lower().strip()
        return self.config.agent_registry.get_agent(normalized_agent_id)

    async def _chat_directly_with_agent(self, request: AgentChatRequest, agent_id: str) -> AgentChatResponse:
        """
        Chat directly with a specific agent, bypassing Emma orchestration.

        Args:
            request: The agent chat request
            agent_id: ID of the agent to chat with directly

        Returns:
            Agent response from the specific agent
        """
        request_id = str(uuid.uuid4())

        try:
            # Get the specific agent
            agent = self._get_agent_by_id(agent_id)
            if not agent:
                raise ValueError(f"Agent {agent_id} not found")

            self.logger.info(f"Direct chat with {agent_id} agent: {request.message[:100]}...")

            # Create a task for direct processing
            task = AgentTask(
                id=str(uuid.uuid4()),
                description=request.message,
                priority=TaskPriority.HIGH.value,
                status=TaskStatus.PENDING,
                assigned_to=agent.id,
                metadata={
                    "request_id": request_id,
                    "context": request.context or {},
                    "direct_access": True,
                    "bypass_orchestration": True
                }
            )

            # Create reasoning trace for direct access
            reasoning_trace = [
                {
                    "type": "direct_access",
                    "content": f"Direct communication with {agent.name} - bypassing orchestration",
                    "agent": agent.id,
                    "agent_name": agent.name,
                    "timestamp": str(int(time.time() * 1000))
                },
                {
                    "type": "processing",
                    "content": f"{agent.name} processing request directly",
                    "agent": agent.id,
                    "agent_name": agent.name,
                    "timestamp": str(int(time.time() * 1000))
                }
            ]

            # Process the task directly with the agent
            result = await agent.process_task(task)
            response = result.get("result", "I processed your request successfully.")

            reasoning_trace.append({
                "type": "completion",
                "content": f"Direct response generated by {agent.name}",
                "agent": agent.id,
                "agent_name": agent.name,
                "timestamp": str(int(time.time() * 1000))
            })

            return AgentChatResponse(
                response=response,
                reasoning_trace=reasoning_trace,
                metadata={
                    "agent_id": agent_id,
                    "timestamp": time.time(),
                    "routing_type": "direct_access",
                    "bypass_orchestration": True
                },
                error=None
            )

        except Exception as e:
            self.logger.error(f"Error in direct chat with {agent_id}: {str(e)}", exc_info=True)

            error_trace = [
                {
                    "type": "error",
                    "content": f"Error in direct communication with {agent_id}: {str(e)}",
                    "agent": agent_id,
                    "agent_name": agent_id.title(),
                    "timestamp": str(int(time.time() * 1000))
                }
            ]

            return AgentChatResponse(
                response=f"I'm sorry, I encountered an error while processing your request directly. Error: {str(e)}",
                reasoning_trace=error_trace,
                metadata={
                    "agent_id": agent_id,
                    "timestamp": time.time(),
                    "routing_type": "direct_access_error"
                },
                error=str(e)
            )

    def _validate_agent_request(self, request: AgentChatRequest) -> str:
        """Validate agent chat request and return normalized agent ID."""
        if not request.agent_id:
            raise ValueError("agent_id is required")

        normalized_agent_id = request.agent_id.lower().strip()
        agent = self._get_agent_by_id(normalized_agent_id)

        if not agent:
            available_agents = [agent["id"] for agent in self.get_available_agents()]
            raise ValueError(f"Agent '{request.agent_id}' not found. Available agents: {available_agents}")

        return normalized_agent_id

    def _create_workflow_tasks(self, request: CrewRunRequest, request_id: str, prompt: str) -> List[Any]:
        """Create workflow tasks based on the request."""
        tasks = []

        # Create the main task for Emma agent
        emma_agent = self._get_agent_by_id("emma")
        main_task = self.config.orchestrator.create_task(
            description=prompt,
            priority=TaskPriority.HIGH,
            assigned_to=emma_agent.id if emma_agent else "emma",
            metadata={"crew_id": request.crew_id, "request_id": request_id}
        )
        tasks.append(main_task)

        # Determine if we need specialized agents based on the prompt
        if "content" in prompt.lower() or "write" in prompt.lower() or "blog" in prompt.lower():
            content_agent = self._get_agent_by_id("content")
            content_task = self.config.orchestrator.create_task(
                description=f"Create content for: {prompt}",
                priority=TaskPriority.MEDIUM,
                assigned_to=content_agent.id if content_agent else "content",
                dependencies=[main_task.id],
                metadata={"crew_id": request.crew_id, "request_id": request_id, "parent_task": main_task.id}
            )
            tasks.append(content_task)

        if "seo" in prompt.lower() or "search" in prompt.lower() or "keyword" in prompt.lower():
            seo_agent = self._get_agent_by_id("seo")
            seo_task = self.config.orchestrator.create_task(
                description=f"Analyze SEO for: {prompt}",
                priority=TaskPriority.MEDIUM,
                assigned_to=seo_agent.id if seo_agent else "seo",
                dependencies=[main_task.id],
                metadata={"crew_id": request.crew_id, "request_id": request_id, "parent_task": main_task.id}
            )
            tasks.append(seo_task)

        return tasks

    def _extract_workflow_result(self, result: Any, request_id: str) -> tuple[str, List[Dict[str, Any]]]:
        """Extract workflow result and reasoning trace."""
        workflow_result = ""
        reasoning_trace = []

        if hasattr(result, 'task_results') and result.task_results:
            for task_result in result.task_results:
                if hasattr(task_result, 'result') and task_result.result:
                    workflow_result += task_result.result + "\n"

                    # Add to reasoning trace
                    reasoning_trace.append({
                        "type": "info",
                        "content": f"Task processed successfully",
                        "agent": getattr(task_result, 'assigned_to', 'unknown'),
                        "timestamp": str(getattr(task_result, 'timestamp', time.time())),
                        "trace_snippet": [getattr(task_result, 'reasoning', "Task completed")]
                    })

        # Get additional reasoning from tracker
        trace_data = self.config.reasoning_tracker.get_trace("workflow", request_id)
        reasoning_trace.extend(trace_data)

        return workflow_result.strip(), reasoning_trace

    async def run_workflow(self, request: CrewRunRequest) -> CrewRunResponse:
        """Run a workflow with the agent system."""
        request_id = str(uuid.uuid4())
        start_time = time.time()

        try:
            # Validate required fields
            if not request.crew_id:
                raise ValueError("crew_id is required")

            # Get input parameters with proper fallbacks
            prompt = getattr(request, 'prompt', "") or ""
            input_params = getattr(request, 'inputs', {}) or {}

            # Create a workflow
            workflow = self.config.orchestrator.create_workflow(
                name=f"Workflow {request_id}",
                description=f"Workflow for request {request_id}",
                initial_query=prompt,
                priority=TaskPriority.MEDIUM,
                metadata={"crew_id": request.crew_id, "request_id": request_id, **input_params}
            )

            # Create and add tasks to workflow
            tasks = self._create_workflow_tasks(request, request_id, prompt)
            for task in tasks:
                self.config.orchestrator.add_task_to_workflow(workflow.id, task)

            # Execute the workflow
            self.logger.info(f"Running workflow for request {request_id}")
            result = await self.config.orchestrator.execute_workflow(workflow.id)

            # Extract results
            workflow_result, reasoning_trace = self._extract_workflow_result(result, request_id)
            execution_time = time.time() - start_time

            # Save trace to history if available
            try:
                session = SessionLocal()
                trace_id = await save_trace_to_history(
                    session, request_id, reasoning_trace, workflow_result
                )
                session.close()
            except Exception as e:
                self.logger.warning(f"Failed to save trace to history: {e}")
                trace_id = None

            return CrewRunResponse(
                request_id=request_id,
                status="success" if not result.error else "error",
                result=workflow_result,
                reasoning_trace=reasoning_trace,
                metadata={
                    "execution_time_seconds": execution_time,
                    "trace_id": trace_id,
                    "crew_id": request.crew_id
                },
                error=result.error
            )

        except ValueError as ve:
            execution_time = time.time() - start_time
            self.logger.error(f"Validation error running workflow: {str(ve)}", exc_info=True)
            raise HTTPException(
                status_code=400,
                detail={
                    "request_id": request_id,
                    "error": {"code": "validation_error", "message": str(ve)}
                }
            )
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Error running workflow: {str(e)}", exc_info=True)
            return CrewRunResponse(
                request_id=request_id,
                status="error",
                result="",
                reasoning_trace=[],
                metadata={
                    "execution_time_seconds": execution_time,
                    "crew_id": getattr(request, 'crew_id', None)
                },
                error=str(e)
            )

    async def chat_with_agent(self, request: AgentChatRequest) -> AgentChatResponse:
        """Chat with a specific agent using smart routing to determine orchestration vs direct access."""
        request_id = str(uuid.uuid4())

        try:
            # Validate the agent request first
            original_agent_id = self._validate_agent_request(request)

            # Use smart routing to determine how to handle the request
            routing_result = self.config.smart_routing.analyze_request(request)

            self.logger.info(f"Smart routing decision: {routing_result.decision.value} for agent {original_agent_id}")
            self.logger.info(f"Routing reasoning: {routing_result.reasoning}")

            # SISTEMA AUTÓNOMO ACTIVADO: Siempre usar Emma autónoma
            # Emma decide autónomamente si necesita especialistas
            self.logger.info(f"AUTONOMOUS SYSTEM: Routing all requests to Emma for autonomous decision making")
            return await self._orchestrate_with_emma(request, original_agent_id, routing_result)

        except ValueError as ve:
            self.logger.error(f"Validation error chatting with agent: {str(ve)}", exc_info=True)
            raise HTTPException(
                status_code=400,
                detail={
                    "request_id": request_id,
                    "error": {"code": "validation_error", "message": str(ve)}
                }
            )
        except Exception as e:
            self.logger.error(f"Error chatting with agent: {str(e)}", exc_info=True)
            return AgentChatResponse(
                response="I'm sorry, an error occurred while processing your request.",
                reasoning_trace=[
                    {
                        "type": "error",
                        "content": f"Error general del sistema: {str(e)}",
                        "agent": getattr(request, 'agent_id', 'unknown'),
                        "agent_name": "Sistema",
                        "timestamp": str(int(time.time() * 1000))
                    }
                ],
                metadata={
                    "agent_id": getattr(request, 'agent_id', None),
                    "timestamp": time.time()
                },
                error=str(e)
            )

    async def _orchestrate_with_emma(self, request: AgentChatRequest, original_agent_id: str, routing_result) -> AgentChatResponse:
        """
        Orchestrate request through Emma for standard coordination.

        Args:
            request: The agent chat request
            original_agent_id: The originally requested agent ID
            routing_result: Result from smart routing analysis

        Returns:
            Agent response from Emma orchestration
        """
        request_id = str(uuid.uuid4())

        try:
            # Get Emma agent
            emma_agent = self._get_agent_by_id("emma")
            if not emma_agent:
                raise ValueError("Emma agent not found - orchestration not available")

            # Modify the message to include orchestration context
            orchestrated_message = request.message
            if original_agent_id != "emma":
                orchestrated_message = f"The user requested to work with the {original_agent_id} agent. Please coordinate with them as needed. User's message: {request.message}"

            # Create a task for Emma
            task = AgentTask(
                id=str(uuid.uuid4()),
                description=orchestrated_message,
                priority=TaskPriority.HIGH.value,
                status=TaskStatus.PENDING,
                assigned_to=emma_agent.id,
                metadata={
                    "request_id": request_id,
                    "context": request.context or {},
                    "original_agent_requested": original_agent_id,
                    "orchestration": True,
                    "routing_reasoning": routing_result.reasoning
                }
            )

            # Create reasoning trace for orchestration
            reasoning_trace = [
                {
                    "type": "orchestration",
                    "content": f"Emma orchestrating request - {routing_result.reasoning}",
                    "agent": emma_agent.id,
                    "agent_name": emma_agent.name,
                    "timestamp": str(int(time.time() * 1000))
                },
                {
                    "type": "analysis",
                    "content": f"Emma analyzing request: {request.message[:100]}...",
                    "agent": emma_agent.id,
                    "agent_name": emma_agent.name,
                    "timestamp": str(int(time.time() * 1000))
                }
            ]

            # Process the task with Emma
            result = await emma_agent.process_task(task)
            response = result.get("result", "I processed your request successfully.")

            reasoning_trace.append({
                "type": "completion",
                "content": "Response generated through Emma orchestration",
                "agent": emma_agent.id,
                "agent_name": emma_agent.name,
                "timestamp": str(int(time.time() * 1000))
            })

            return AgentChatResponse(
                response=response,
                reasoning_trace=reasoning_trace,
                metadata={
                    "agent_id": request.agent_id,
                    "timestamp": time.time(),
                    "routing_type": "emma_orchestration",
                    "routing_reasoning": routing_result.reasoning
                },
                error=None
            )

        except Exception as e:
            self.logger.error(f"Error in Emma orchestration: {str(e)}", exc_info=True)

            error_trace = [
                {
                    "type": "error",
                    "content": f"Error in Emma orchestration: {str(e)}",
                    "agent": "emma",
                    "agent_name": "Emma",
                    "timestamp": str(int(time.time() * 1000))
                }
            ]

            return AgentChatResponse(
                response=f"I'm sorry, I encountered an error while orchestrating your request. Error: {str(e)}",
                reasoning_trace=error_trace,
                metadata={
                    "agent_id": request.agent_id,
                    "timestamp": time.time(),
                    "routing_type": "emma_orchestration_error"
                },
                error=str(e)
            )

    async def _orchestrate_multi_agent_request(self, request: AgentChatRequest, routing_result) -> AgentChatResponse:
        """
        Orchestrate complex multi-agent coordination through Emma.

        Args:
            request: The agent chat request
            routing_result: Result from smart routing analysis

        Returns:
            Agent response from multi-agent coordination
        """
        request_id = str(uuid.uuid4())

        try:
            # Get Emma agent for coordination
            emma_agent = self._get_agent_by_id("emma")
            if not emma_agent:
                raise ValueError("Emma agent not found - multi-agent coordination not available")

            # Create enhanced message for multi-agent coordination
            coordination_message = f"This request requires coordination between multiple specialists: {', '.join(routing_result.target_agents)}. User's message: {request.message}"

            # Create a task for Emma with multi-agent context
            task = AgentTask(
                id=str(uuid.uuid4()),
                description=coordination_message,
                priority=TaskPriority.HIGH.value,
                status=TaskStatus.PENDING,
                assigned_to=emma_agent.id,
                metadata={
                    "request_id": request_id,
                    "context": request.context or {},
                    "multi_agent_coordination": True,
                    "required_agents": routing_result.target_agents,
                    "routing_reasoning": routing_result.reasoning
                }
            )

            # Create reasoning trace for multi-agent coordination
            reasoning_trace = [
                {
                    "type": "multi_agent_coordination",
                    "content": f"Emma coordinating multi-agent response - {routing_result.reasoning}",
                    "agent": emma_agent.id,
                    "agent_name": emma_agent.name,
                    "timestamp": str(int(time.time() * 1000))
                },
                {
                    "type": "coordination_planning",
                    "content": f"Planning coordination between: {', '.join(routing_result.target_agents)}",
                    "agent": emma_agent.id,
                    "agent_name": emma_agent.name,
                    "timestamp": str(int(time.time() * 1000))
                }
            ]

            # Process the task with Emma for multi-agent coordination
            result = await emma_agent.process_task(task)
            response = result.get("result", "I coordinated with multiple specialists to provide you with a comprehensive response.")

            reasoning_trace.append({
                "type": "completion",
                "content": "Multi-agent coordination completed successfully",
                "agent": emma_agent.id,
                "agent_name": emma_agent.name,
                "timestamp": str(int(time.time() * 1000))
            })

            return AgentChatResponse(
                response=response,
                reasoning_trace=reasoning_trace,
                metadata={
                    "agent_id": request.agent_id,
                    "timestamp": time.time(),
                    "routing_type": "multi_agent_coordination",
                    "coordinated_agents": routing_result.target_agents,
                    "routing_reasoning": routing_result.reasoning
                },
                error=None
            )

        except Exception as e:
            self.logger.error(f"Error in multi-agent coordination: {str(e)}", exc_info=True)

            error_trace = [
                {
                    "type": "error",
                    "content": f"Error in multi-agent coordination: {str(e)}",
                    "agent": "emma",
                    "agent_name": "Emma",
                    "timestamp": str(int(time.time() * 1000))
                }
            ]

            return AgentChatResponse(
                response=f"I'm sorry, I encountered an error while coordinating multiple agents for your request. Error: {str(e)}",
                reasoning_trace=error_trace,
                metadata={
                    "agent_id": request.agent_id,
                    "timestamp": time.time(),
                    "routing_type": "multi_agent_coordination_error"
                },
                error=str(e)
            )

    async def chat_with_agent_stream(self, request: AgentChatRequest) -> AsyncGenerator[str, None]:
        """Chat with a specific agent with streaming response."""
        request_id = str(uuid.uuid4())

        try:
            # Validate and get agent
            normalized_agent_id = self._validate_agent_request(request)
            agent = self._get_agent_by_id(normalized_agent_id)

            if not agent:
                yield f"Error: Agent with ID {request.agent_id} not found"
                return

            # Create a task for the agent
            task = AgentTask(
                id=str(uuid.uuid4()),
                description=request.message,
                priority=TaskPriority.HIGH.value,
                status=TaskStatus.PENDING,
                assigned_to=agent.id,
                metadata={"request_id": request_id, "context": request.context or {}}
            )

            # Track current reasoning step
            current_reasoning = {"timestamp": int(time.time() * 1000), "content": "", "type": "thinking"}

            try:
                # Use the streaming version of process_task
                async for chunk in agent.process_task_stream(task):
                    # Check if this is a reasoning marker
                    if chunk.startswith("REASONING:"):
                        # Save current reasoning if it has content
                        if current_reasoning["content"]:
                            self.config.reasoning_tracker.add_trace_step(agent.id, request_id, current_reasoning)

                        # Start a new reasoning step
                        current_reasoning = {
                            "timestamp": int(time.time() * 1000),
                            "type": "reasoning",
                            "agent": agent.id,
                            "agent_name": agent.name,
                            "content": chunk[10:].strip()  # Remove the REASONING: prefix
                        }
                        continue

                    # Check if this is a tool usage marker
                    elif chunk.startswith("TOOL:"):
                        # Save current reasoning if it has content
                        if current_reasoning["content"]:
                            self.config.reasoning_tracker.add_trace_step(agent.id, request_id, current_reasoning)

                        # Start a new tool usage step
                        current_reasoning = {
                            "timestamp": int(time.time() * 1000),
                            "type": "tool_usage",
                            "agent": agent.id,
                            "agent_name": agent.name,
                            "content": chunk[5:].strip()  # Remove the TOOL: prefix
                        }
                        continue

                    # Regular content
                    if "content" in current_reasoning:
                        current_reasoning["content"] += chunk

                    # Yield the chunk for the regular response
                    yield chunk

                # Save the final reasoning step if it has content
                if current_reasoning.get("content"):
                    self.config.reasoning_tracker.add_trace_step(agent.id, request_id, current_reasoning)

                # Add a completion step
                self.config.reasoning_tracker.add_trace_step(agent.id, request_id, {
                    "timestamp": int(time.time() * 1000),
                    "type": "completion",
                    "agent": agent.id,
                    "agent_name": agent.name,
                    "content": "Task completed successfully"
                })

            except Exception as e:
                self.logger.error(f"Error processing streaming task with agent {agent.name}: {str(e)}", exc_info=True)

                # Add error to reasoning trace
                self.config.reasoning_tracker.add_trace_step(agent.id, request_id, {
                    "timestamp": int(time.time() * 1000),
                    "type": "error",
                    "agent": agent.id,
                    "agent_name": agent.name,
                    "content": f"Error: {str(e)}"
                })

                yield f"Error: {str(e)}"

        except Exception as e:
            self.logger.error(f"Error in streaming chat: {str(e)}", exc_info=True)
            yield f"Error: {str(e)}"


# Create global service instance
_service_config = AgentServiceFactory.create_config()
agent_service = AgentService(_service_config)


# Backward compatibility functions
def get_available_agents():
    """Get a list of all available agents with their details."""
    return agent_service.get_available_agents()


async def run_agent_workflow(request: CrewRunRequest, _db: Optional[Session] = None) -> CrewRunResponse:
    """Run a workflow with the agent system (backward compatibility)."""
    return await agent_service.run_workflow(request)


async def chat_with_agent(request: AgentChatRequest, _db: Optional[Session] = None) -> AgentChatResponse:
    """Chat with a specific agent (backward compatibility)."""
    return await agent_service.chat_with_agent(request)


async def chat_with_agent_stream(request: AgentChatRequest) -> AsyncGenerator[str, None]:
    """Chat with a specific agent with streaming response (backward compatibility)."""
    async for chunk in agent_service.chat_with_agent_stream(request):
        yield chunk


async def get_agent_reasoning_trace(agent_id: str, request_id: str) -> List[Dict[str, Any]]:
    """Get the reasoning trace for a specific agent and request (backward compatibility)."""
    return agent_service.config.reasoning_tracker.get_trace(agent_id, request_id)
