import { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Send, Code, Play, FileJson, RefreshCw } from "lucide-react";
import { Agent } from "@/data/agents-data";

interface LangFlowRunPanelProps {
  agent: Agent;
}

export function LangFlowRunPanel({ agent }: LangFlowRunPanelProps) {
  const [inputText, setInputText] = useState("");
  const [inputData, setInputData] = useState('{\n  "text": ""\n}');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<"simple" | "advanced">("simple");

  // Función para ejecutar el agente de LangFlow
  const runAgent = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Preparar los datos de entrada según el modo activo
      let payload;

      if (activeView === "simple") {
        // Modo simple: solo texto
        payload = { text: inputText };
      } else {
        // Modo avanzado: JSON personalizado
        try {
          payload = JSON.parse(inputData);
        } catch (jsonError) {
          setError("Error en el formato JSON. Por favor revisa la sintaxis.");
          setIsLoading(false);
          return;
        }
      }

      // Ejecutar el agente mediante la API
      const response = await fetch(`/api/langflow/run/${agent.id}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      }).then((res) => res.json());

      setResult(response);
    } catch (error: any) {
      console.error("Error al ejecutar el agente:", error);
      setError(
        error.message ||
          "Error al ejecutar el agente. Por favor intenta de nuevo.",
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Función para formatear el resultado como JSON legible
  const formatResult = (data: any) => {
    try {
      return JSON.stringify(data, null, 2);
    } catch (e) {
      return String(data);
    }
  };

  return (
    <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6">
      <div className="flex items-center mb-4">
        <div className="w-5 h-5 bg-blue-500 rounded-md mr-2"></div>
        <h3 className="text-xl font-black">Ejecutar Agente</h3>
      </div>

      <Tabs
        defaultValue="simple"
        className="w-full"
        onValueChange={(value) => setActiveView(value as "simple" | "advanced")}
      >
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="simple" className="flex items-center gap-2">
            <Play size={16} />
            <span>Modo Simple</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Code size={16} />
            <span>Modo Avanzado</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="simple" className="mt-0">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                Texto de entrada
              </label>
              <Textarea
                placeholder="Escribe el texto que quieres procesar con el agente..."
                className="min-h-32 border-2 border-gray-300 focus:border-blue-500"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
              />
            </div>

            <Button
              onClick={runAgent}
              disabled={!inputText || isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white border-2 border-black rounded-lg shadow-[3px_3px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[5px_5px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all flex items-center gap-2 justify-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Procesando...</span>
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  <span>Ejecutar Agente</span>
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="mt-0">
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium">
                  Datos de entrada JSON
                </label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setInputData('{\n  "text": ""\n}')}
                  className="h-7 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Reiniciar
                </Button>
              </div>
              <Textarea
                placeholder='{"text": "Ejemplo", "otras_variables": "valores"}'
                className="min-h-48 font-mono text-sm border-2 border-gray-300 focus:border-blue-500"
                value={inputData}
                onChange={(e) => setInputData(e.target.value)}
              />
              <p className="text-xs text-gray-500 mt-1">
                Formato JSON con las variables de entrada para el agente de
                LangFlow
              </p>
            </div>

            <Button
              onClick={runAgent}
              disabled={!inputData || isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white border-2 border-black rounded-lg shadow-[3px_3px_0px_0px_rgba(0,0,0,0.8)] hover:shadow-[5px_5px_0px_0px_rgba(0,0,0,0.8)] hover:translate-y-[-2px] transition-all flex items-center gap-2 justify-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Procesando...</span>
                </>
              ) : (
                <>
                  <FileJson className="h-4 w-4" />
                  <span>Ejecutar con Parámetros</span>
                </>
              )}
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Mostrar errores */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 p-4 border-2 border-red-300 bg-red-50 rounded-lg text-red-700"
        >
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
        </motion.div>
      )}

      {/* Mostrar resultados */}
      {result && !error && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4"
        >
          <div className="p-4 border-2 border-green-300 bg-green-50 rounded-t-lg text-green-700 font-medium">
            Resultado
          </div>
          <pre className="p-4 bg-gray-900 text-gray-100 rounded-b-lg overflow-auto text-sm font-mono max-h-96">
            {formatResult(result)}
          </pre>
        </motion.div>
      )}
    </div>
  );
}

export default LangFlowRunPanel;
