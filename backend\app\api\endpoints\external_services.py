"""
API endpoints for external services integration.

This module provides endpoints for managing external service API keys
and other integration points with third-party services.
"""

import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel

from app.core.dependencies import verify_api_key, get_external_api_key
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


class ApiKeyResponse(BaseModel):
    """Response model for API key status and information."""
    status: str
    service: str
    configured: bool
    key_preview: Optional[str] = None
    key: Optional[str] = None


class ApiKeyStatusResponse(BaseModel):
    """Response model for checking API key status."""
    status: str
    services: Dict[str, bool]


@router.get("/status", response_model=ApiKeyStatusResponse)
async def check_api_keys_status():
    """
    Check the status of all external service API keys.

    Returns:
        Status information for all configured external services
    """
    # Check status of all external service API keys
    services_status = {
        "gemini": bool(settings.GEMINI_API_KEY),
        "stability": bool(settings.STABILITY_API_KEY),
        "elevenlabs": bool(settings.ELEVENLABS_API_KEY),
    }

    return {
        "status": "success",
        "services": services_status
    }


@router.get("/{service_name}/key", response_model=ApiKeyResponse, dependencies=[Depends(verify_api_key)])
async def get_service_api_key(service_name: str, request: Request):
    """
    Get the API key for a specific external service.

    Args:
        service_name: The name of the external service (e.g., 'gemini', 'stability')

    Returns:
        API key information for the specified service

    Raises:
        HTTPException: If the service is not supported or the API key is not configured
    """
    from app.utils.validation import validate_string

    try:
        # Validate and sanitize service name
        service_name = validate_string(
            service_name.lower(),
            min_length=1,
            max_length=50,
            pattern=r'^[a-z0-9_-]+$',  # Only allow lowercase letters, numbers, underscore, and hyphen
            sanitize=True
        )
    except ValueError as e:
        logger.warning(f"Invalid service name format: {service_name}")
        raise HTTPException(
            status_code=400,
            detail={
                "code": "invalid_service_format",
                "message": f"Invalid service name format: {str(e)}"
            }
        )

    # Validate service name against allowed list
    valid_services = ["gemini", "stability", "elevenlabs"]
    if service_name not in valid_services:
        logger.warning(f"Unsupported service requested: {service_name}")
        raise HTTPException(
            status_code=400,
            detail={
                "code": "invalid_service",
                "message": f"Invalid service name. Supported services: {', '.join(valid_services)}"
            }
        )

    try:
        # Get the API key for the requested service
        api_key = await get_external_api_key(service_name)

        # Create a preview of the API key (first 4 and last 4 characters)
        key_preview = f"{api_key[:4]}...{api_key[-4:]}" if len(api_key) > 8 else None

        # Include the actual API key in the response
        # This is needed for the frontend to use the API key
        # In a production environment, this should be more secure
        return {
            "status": "success",
            "service": service_name,
            "configured": bool(api_key),
            "key_preview": key_preview,
            "key": api_key  # Include the actual API key
        }

    except HTTPException as e:
        # Re-raise the exception from get_external_api_key
        raise e
    except Exception as e:
        logger.error(f"Error retrieving API key for {service_name}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "code": "api_key_retrieval_error",
                "message": f"Error retrieving API key for {service_name}"
            }
        )
